<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الملف الشخصي</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="profile-container">
        <div class="profile-card">
            @auth
                @if(Auth::user()->hasProfileImage())
                    <img src="{{ Auth::user()->getProfileImageUrl() }}" alt="الصورة الشخصية" class="avatar">
                @else
                    <img src="{{ Auth::user()->getDefaultAvatar() }}" alt="صورة افتراضية" class="avatar">
                @endif
                <h2 class="username">{{ Auth::user()->name }}</h2>
                <p class="email">{{ Auth::user()->email }}</p>
                <p class="role">الدور: <span>مستخدم</span></p>
                <p class="status active">الحالة: نشط</p>
                <a href="{{ route('profile.index') }}" class="edit-btn">عرض الملف الشخصي</a>
            @else
                <img src="https://ui-avatars.com/api/?name=مستخدم&background=random&color=fff&size=100&rounded=true" alt="صورة افتراضية" class="avatar">
                <h2 class="username">اسم المستخدم</h2>
                <p class="email"><EMAIL></p>
                <p class="role">الدور: <span>مستخدم</span></p>
                <p class="status active">الحالة: نشط</p>
                <a href="{{ route('login') }}" class="edit-btn">تسجيل الدخول</a>
            @endauth
        </div>
    </div>
</body>
</html>

<style>
    body {
        font-family: 'Tajawal', sans-serif;
        background-color: #f4f4f4;
        text-align: center;
    }
    .profile-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
    }
    .profile-card {
        background: #fff;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
        text-align: center;
        width: 300px;
    }
    .avatar {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        border: 3px solid #007BFF;
    }
    .username {
        font-size: 22px;
        font-weight: bold;
    }
    .email, .role {
        color: #666;
    }
    .status {
        font-weight: bold;
        margin-top: 10px;
    }
    .active { color: green; }
    .inactive { color: red; }
    .edit-btn {
        background: #007BFF;
        color: #fff;
        padding: 10px 15px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        margin-top: 10px;
    }
    .edit-btn:hover {
        background: #0056b3;
    }
</style>
