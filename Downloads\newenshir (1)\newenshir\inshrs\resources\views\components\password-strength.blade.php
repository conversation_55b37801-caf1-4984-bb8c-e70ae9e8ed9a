@props([
    'inputId' => 'password',
    'showRequirements' => true,
    'showStrengthBar' => true,
    'minLength' => 8,
    'requireUppercase' => true,
    'requireLowercase' => true,
    'requireNumbers' => true,
    'requireSpecialChars' => true,
    'realTimeValidation' => true
])

<div class="password-strength-container" data-input-id="{{ $inputId }}">
    <style>
        .password-strength-container {
            margin-top: 0.5rem;
        }
        
        .password-strength-bar {
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .password-strength-fill {
            height: 100%;
            width: 0%;
            transition: all 0.3s ease;
            border-radius: 3px;
        }
        
        .strength-very-weak { background: linear-gradient(90deg, #dc3545 0%, #c82333 100%); }
        .strength-weak { background: linear-gradient(90deg, #fd7e14 0%, #e55a00 100%); }
        .strength-medium { background: linear-gradient(90deg, #ffc107 0%, #e0a800 100%); }
        .strength-strong { background: linear-gradient(90deg, #20c997 0%, #1aa179 100%); }
        .strength-very-strong { background: linear-gradient(90deg, #28a745 0%, #1e7e34 100%); }
        
        .password-requirements {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 0.75rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .requirements-title {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.75rem;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
        }
        
        .requirements-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .requirement-item {
            display: flex;
            align-items: center;
            padding: 0.25rem 0;
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }
        
        .requirement-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 0.5rem;
            font-size: 0.75rem;
            transition: all 0.3s ease;
        }
        
        .requirement-item.valid .requirement-icon {
            background: #28a745;
            color: white;
        }
        
        .requirement-item.invalid .requirement-icon {
            background: #dc3545;
            color: white;
        }
        
        .requirement-item.pending .requirement-icon {
            background: #6c757d;
            color: white;
        }
        
        .requirement-item.valid {
            color: #28a745;
        }
        
        .requirement-item.invalid {
            color: #dc3545;
        }
        
        .requirement-item.pending {
            color: #6c757d;
        }
        
        .strength-indicator {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .strength-text {
            font-size: 0.875rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .strength-percentage {
            font-size: 0.875rem;
            color: #6c757d;
        }
        
        .password-tips {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196f3;
            border-radius: 0.75rem;
            padding: 1rem;
            margin-top: 1rem;
            display: none;
        }
        
        .password-tips.show {
            display: block;
            animation: slideDown 0.3s ease;
        }
        
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .tips-title {
            font-weight: 600;
            color: #1976d2;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .tips-list {
            font-size: 0.875rem;
            color: #1565c0;
            margin: 0;
            padding-right: 1rem;
        }
        
        .tips-list li {
            margin-bottom: 0.25rem;
        }
    </style>
    
    @if($showStrengthBar)
        <div class="strength-indicator">
            <span class="strength-text" id="{{ $inputId }}-strength-text">قوة كلمة المرور</span>
            <span class="strength-percentage" id="{{ $inputId }}-strength-percentage">0%</span>
        </div>
        <div class="password-strength-bar">
            <div class="password-strength-fill" id="{{ $inputId }}-strength-fill"></div>
        </div>
    @endif
    
    @if($showRequirements)
        <div class="password-requirements">
            <div class="requirements-title">
                <i class="fas fa-shield-alt me-2"></i>
                متطلبات كلمة المرور
            </div>
            <ul class="requirements-list">
                <li class="requirement-item pending" id="{{ $inputId }}-req-length">
                    <span class="requirement-icon">
                        <i class="fas fa-times"></i>
                    </span>
                    <span>على الأقل {{ $minLength }} أحرف</span>
                </li>
                
                @if($requireUppercase)
                    <li class="requirement-item pending" id="{{ $inputId }}-req-uppercase">
                        <span class="requirement-icon">
                            <i class="fas fa-times"></i>
                        </span>
                        <span>حرف كبير واحد على الأقل (A-Z)</span>
                    </li>
                @endif
                
                @if($requireLowercase)
                    <li class="requirement-item pending" id="{{ $inputId }}-req-lowercase">
                        <span class="requirement-icon">
                            <i class="fas fa-times"></i>
                        </span>
                        <span>حرف صغير واحد على الأقل (a-z)</span>
                    </li>
                @endif
                
                @if($requireNumbers)
                    <li class="requirement-item pending" id="{{ $inputId }}-req-numbers">
                        <span class="requirement-icon">
                            <i class="fas fa-times"></i>
                        </span>
                        <span>رقم واحد على الأقل (0-9)</span>
                    </li>
                @endif
                
                @if($requireSpecialChars)
                    <li class="requirement-item pending" id="{{ $inputId }}-req-special">
                        <span class="requirement-icon">
                            <i class="fas fa-times"></i>
                        </span>
                        <span>رمز خاص واحد على الأقل (!@#$%^&*)</span>
                    </li>
                @endif
            </ul>
        </div>
    @endif
    
    <div class="password-tips" id="{{ $inputId }}-tips">
        <div class="tips-title">
            <i class="fas fa-lightbulb me-2"></i>
            نصائح لكلمة مرور قوية
        </div>
        <ul class="tips-list">
            <li>استخدم مزيج من الأحرف الكبيرة والصغيرة</li>
            <li>أضف أرقام ورموز خاصة</li>
            <li>تجنب المعلومات الشخصية الواضحة</li>
            <li>استخدم عبارة يسهل تذكرها</li>
            <li>لا تستخدم نفس كلمة المرور في مواقع متعددة</li>
        </ul>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const inputId = '{{ $inputId }}';
    const passwordInput = document.getElementById(inputId);
    const container = document.querySelector(`[data-input-id="${inputId}"]`);
    
    if (!passwordInput || !container) return;
    
    const config = {
        minLength: {{ $minLength }},
        requireUppercase: {{ $requireUppercase ? 'true' : 'false' }},
        requireLowercase: {{ $requireLowercase ? 'true' : 'false' }},
        requireNumbers: {{ $requireNumbers ? 'true' : 'false' }},
        requireSpecialChars: {{ $requireSpecialChars ? 'true' : 'false' }},
        realTimeValidation: {{ $realTimeValidation ? 'true' : 'false' }}
    };
    
    const elements = {
        strengthFill: document.getElementById(`${inputId}-strength-fill`),
        strengthText: document.getElementById(`${inputId}-strength-text`),
        strengthPercentage: document.getElementById(`${inputId}-strength-percentage`),
        tips: document.getElementById(`${inputId}-tips`),
        requirements: {
            length: document.getElementById(`${inputId}-req-length`),
            uppercase: document.getElementById(`${inputId}-req-uppercase`),
            lowercase: document.getElementById(`${inputId}-req-lowercase`),
            numbers: document.getElementById(`${inputId}-req-numbers`),
            special: document.getElementById(`${inputId}-req-special`)
        }
    };
    
    function checkPasswordStrength(password) {
        const checks = {
            length: password.length >= config.minLength,
            uppercase: config.requireUppercase ? /[A-Z]/.test(password) : true,
            lowercase: config.requireLowercase ? /[a-z]/.test(password) : true,
            numbers: config.requireNumbers ? /[0-9]/.test(password) : true,
            special: config.requireSpecialChars ? /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password) : true
        };
        
        const validChecks = Object.values(checks).filter(Boolean).length;
        const totalChecks = Object.values(checks).length;
        const percentage = Math.round((validChecks / totalChecks) * 100);
        
        let strength = 'very-weak';
        let strengthText = 'ضعيفة جداً';
        
        if (percentage >= 100) {
            strength = 'very-strong';
            strengthText = 'قوية جداً';
        } else if (percentage >= 80) {
            strength = 'strong';
            strengthText = 'قوية';
        } else if (percentage >= 60) {
            strength = 'medium';
            strengthText = 'متوسطة';
        } else if (percentage >= 40) {
            strength = 'weak';
            strengthText = 'ضعيفة';
        }
        
        return {
            checks,
            percentage,
            strength,
            strengthText,
            isValid: percentage === 100
        };
    }
    
    function updateRequirementItem(element, isValid) {
        if (!element) return;
        
        element.className = `requirement-item ${isValid ? 'valid' : 'invalid'}`;
        const icon = element.querySelector('.requirement-icon i');
        if (icon) {
            icon.className = isValid ? 'fas fa-check' : 'fas fa-times';
        }
    }
    
    function updatePasswordStrength(password) {
        const result = checkPasswordStrength(password);
        
        // تحديث شريط القوة
        if (elements.strengthFill) {
            elements.strengthFill.style.width = `${result.percentage}%`;
            elements.strengthFill.className = `password-strength-fill strength-${result.strength}`;
        }
        
        // تحديث نص القوة
        if (elements.strengthText) {
            elements.strengthText.textContent = `قوة كلمة المرور: ${result.strengthText}`;
            elements.strengthText.style.color = getStrengthColor(result.strength);
        }
        
        // تحديث النسبة المئوية
        if (elements.strengthPercentage) {
            elements.strengthPercentage.textContent = `${result.percentage}%`;
        }
        
        // تحديث المتطلبات
        updateRequirementItem(elements.requirements.length, result.checks.length);
        updateRequirementItem(elements.requirements.uppercase, result.checks.uppercase);
        updateRequirementItem(elements.requirements.lowercase, result.checks.lowercase);
        updateRequirementItem(elements.requirements.numbers, result.checks.numbers);
        updateRequirementItem(elements.requirements.special, result.checks.special);
        
        // إظهار/إخفاء النصائح
        if (elements.tips) {
            if (password.length > 0 && result.percentage < 80) {
                elements.tips.classList.add('show');
            } else {
                elements.tips.classList.remove('show');
            }
        }
        
        return result;
    }
    
    function getStrengthColor(strength) {
        const colors = {
            'very-weak': '#dc3545',
            'weak': '#fd7e14',
            'medium': '#ffc107',
            'strong': '#20c997',
            'very-strong': '#28a745'
        };
        return colors[strength] || '#6c757d';
    }
    
    // مستمع الأحداث
    if (config.realTimeValidation) {
        passwordInput.addEventListener('input', function() {
            updatePasswordStrength(this.value);
        });
        
        passwordInput.addEventListener('focus', function() {
            if (elements.tips && this.value.length > 0) {
                elements.tips.classList.add('show');
            }
        });
        
        passwordInput.addEventListener('blur', function() {
            if (elements.tips) {
                setTimeout(() => {
                    elements.tips.classList.remove('show');
                }, 200);
            }
        });
    }
    
    // تصدير الدالة للاستخدام الخارجي
    window[`checkPassword_${inputId}`] = function() {
        return updatePasswordStrength(passwordInput.value);
    };
    
    // التحقق الأولي
    if (passwordInput.value) {
        updatePasswordStrength(passwordInput.value);
    }
});
</script>
