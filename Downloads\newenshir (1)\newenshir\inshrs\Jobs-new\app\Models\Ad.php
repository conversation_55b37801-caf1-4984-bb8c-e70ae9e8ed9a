<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Ad extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'title', 'description', 'image', 'category', 'subcategory', 'location',
        'latitude', 'longitude', 'price', 'whatsapp', 'email', 'phone',
        'is_featured', 'featured_until'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_featured' => 'boolean',
        'featured_until' => 'datetime',
        'latitude' => 'decimal:7',
        'longitude' => 'decimal:7',
    ];


    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * نطاق للإعلانات المميزة النشطة
     */
    public function scopeActiveFeatured($query)
    {
        return $query->where('is_featured', true)
                    ->where(function($q) {
                        $q->whereNull('featured_until')
                          ->orWhere('featured_until', '>', now());
                    });
    }

    /**
     * التحقق مما إذا كان الإعلان مميزًا ونشطًا
     */
    public function isActiveFeatured()
    {
        return $this->is_featured &&
               ($this->featured_until === null || $this->featured_until > now());
    }

    /**
     * الحصول على مسار الصورة مع صورة افتراضية إذا لم تكن موجودة
     */
    public function getImageUrl()
    {
        // إذا كان هناك صورة مخزنة في قاعدة البيانات
        if ($this->image) {
            // المسار الكامل للصورة في مجلد التخزين
            $storagePath = storage_path('app/public/' . $this->image);

            // التحقق من وجود الملف في مجلد التخزين
            if (file_exists($storagePath)) {
                // إضافة معلمة عشوائية لتجنب التخزين المؤقت للمتصفح
                // استخدام المسار المباشر إلى مجلد التخزين بدلاً من الرابط الرمزي
                return url('storage-direct.php?file=' . urlencode($this->image) . '&v=' . time());
            } else {
                // تسجيل خطأ إذا كان الملف غير موجود
                \Log::error('Ad image file not found in storage: ' . $storagePath . ' for ad ID: ' . $this->id);
            }
        }

        // إرجاع الصورة الافتراضية
        return 'https://png.pngtree.com/element_our/20190528/ourlarge/pngtree-no-photography-image_1128321.jpg';
    }
}
