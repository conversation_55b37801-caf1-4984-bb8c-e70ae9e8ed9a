@extends('layouts.main')

@section('title', 'إعدادات الحساب')

@section('styles')
<style>
    .settings-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background: #f8f9fa;
        min-height: 100vh;
    }

    .settings-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        text-align: center;
    }

    .settings-header h1 {
        margin: 0;
        font-size: 2.5rem;
        font-weight: bold;
    }

    .settings-header p {
        margin: 10px 0 0 0;
        opacity: 0.9;
        font-size: 1.1rem;
    }

    .settings-tabs {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 30px;
    }

    .tab-nav {
        display: flex;
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        overflow-x: auto;
    }

    .tab-btn {
        flex: 1;
        padding: 15px 20px;
        background: none;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 600;
        color: #6c757d;
        white-space: nowrap;
    }

    .tab-btn.active {
        background: var(--primary-color);
        color: white;
    }

    .tab-btn:hover {
        background: var(--primary-color);
        color: white;
    }

    .tab-content {
        display: none;
        padding: 30px;
    }

    .tab-content.active {
        display: block;
    }

    .settings-section {
        margin-bottom: 30px;
        padding: 25px;
        background: #f8f9fa;
        border-radius: 10px;
        border-left: 4px solid var(--primary-color);
    }

    .section-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
        display: block;
    }

    .form-control {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 12px 15px;
        transition: all 0.3s ease;
        width: 100%;
    }

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
    }

    .form-check {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
        padding: 15px;
        background: white;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-check:hover {
        border-color: var(--primary-color);
        background: #f8f9fa;
    }

    .form-check-input {
        width: 20px;
        height: 20px;
        margin: 0;
    }

    .form-check-label {
        margin: 0;
        font-weight: 500;
        color: #495057;
        cursor: pointer;
        flex: 1;
    }

    .form-check-description {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 5px;
    }

    .btn-custom {
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        margin: 0 10px 10px 0;
        transition: all 0.3s ease;
        border: none;
    }

    .btn-primary-custom {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
    }

    .btn-primary-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.4);
    }

    .btn-danger-custom {
        background: #dc3545;
        color: white;
    }

    .btn-secondary-custom {
        background: #6c757d;
        color: white;
    }

    .alert-custom {
        border-radius: 10px;
        padding: 15px 20px;
        margin-bottom: 20px;
    }

    .blocked-users-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .blocked-user-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        background: white;
        border-radius: 8px;
        margin-bottom: 10px;
        border: 1px solid #e9ecef;
    }

    .user-info {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--primary-color);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
    }

    .danger-zone {
        background: #fff5f5;
        border: 2px solid #fed7d7;
        border-radius: 10px;
        padding: 20px;
        margin-top: 30px;
    }

    .danger-zone h4 {
        color: #e53e3e;
        margin-bottom: 15px;
    }

    /* Profile Image Styles */
    .profile-image-section {
        text-align: center;
    }

    .current-image {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-bottom: 20px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .image-container {
        position: relative;
        width: 150px;
        height: 150px;
        border-radius: 50%;
        overflow: hidden;
        border: 4px solid var(--primary-color);
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .image-container:hover {
        transform: scale(1.05);
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    }

    .profile-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: all 0.3s ease;
    }

    .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.7);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: all 0.3s ease;
        color: white;
        font-size: 0.9rem;
    }

    .image-container:hover .image-overlay {
        opacity: 1;
    }

    .image-overlay i {
        font-size: 1.5rem;
        margin-bottom: 5px;
    }

    .image-info {
        text-align: center;
        flex: 1;
        min-width: 200px;
    }

    .image-info h5 {
        margin-bottom: 5px;
        color: #495057;
    }

    .upload-controls {
        display: flex;
        gap: 10px;
        justify-content: center;
        flex-wrap: wrap;
        margin-bottom: 20px;
    }

    .upload-guidelines {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        text-align: right;
        max-width: 500px;
        margin: 0 auto;
    }

    .upload-guidelines h6 {
        color: #495057;
        margin-bottom: 10px;
    }

    .upload-guidelines ul {
        margin: 0;
        padding-right: 20px;
        color: #6c757d;
    }

    .upload-guidelines li {
        margin-bottom: 5px;
    }

    /* Loading States */
    .uploading .image-container {
        opacity: 0.7;
        pointer-events: none;
    }

    .uploading .image-overlay {
        opacity: 1;
        background: rgba(var(--primary-color-rgb), 0.8);
    }

    .upload-progress {
        position: absolute;
        bottom: 10px;
        left: 10px;
        right: 10px;
        height: 4px;
        background: rgba(255,255,255,0.3);
        border-radius: 2px;
        overflow: hidden;
    }

    .upload-progress-bar {
        height: 100%;
        background: white;
        width: 0%;
        transition: width 0.3s ease;
    }

    @media (max-width: 768px) {
        .settings-container {
            padding: 10px;
        }

        .tab-nav {
            flex-direction: column;
        }

        .tab-btn {
            text-align: center;
        }

        .tab-content {
            padding: 20px;
        }

        .settings-section {
            padding: 15px;
        }

        .current-image {
            flex-direction: column;
            text-align: center;
        }

        .image-container {
            width: 120px;
            height: 120px;
        }

        .upload-controls {
            flex-direction: column;
            align-items: center;
        }

        .upload-controls .btn {
            width: 100%;
            max-width: 250px;
        }
    }
</style>
@endsection

@section('content')
<div class="settings-container">
    <!-- Header -->
    <div class="settings-header">
        <h1><i class="fas fa-user-cog"></i> إعدادات الحساب</h1>
        <p>إدارة خصوصيتك وتفضيلاتك الشخصية</p>
    </div>

    <!-- رسائل النجاح والخطأ -->
    @if(session('success'))
        <div class="alert alert-success alert-custom">
            <i class="fas fa-check-circle"></i> {{ session('success') }}
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-custom">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>يرجى تصحيح الأخطاء التالية:</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <!-- التبويبات -->
    <div class="settings-tabs">
        <div class="tab-nav">
            <button class="tab-btn active" onclick="showTab('profile')">
                <i class="fas fa-user-circle"></i> الملف الشخصي
            </button>
            <button class="tab-btn" onclick="showTab('privacy')">
                <i class="fas fa-shield-alt"></i> الخصوصية
            </button>
            <button class="tab-btn" onclick="showTab('notifications')">
                <i class="fas fa-bell"></i> الإشعارات
            </button>
            <button class="tab-btn" onclick="showTab('preferences')">
                <i class="fas fa-palette"></i> التفضيلات
            </button>
            <button class="tab-btn" onclick="showTab('security')">
                <i class="fas fa-lock"></i> الأمان
            </button>
            <button class="tab-btn" onclick="showTab('blocked')">
                <i class="fas fa-user-slash"></i> المحظورون
            </button>
        </div>

        <!-- تبويب الملف الشخصي -->
        <div id="profile" class="tab-content active">
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="fas fa-camera"></i> الصورة الشخصية
                </h3>

                <div class="profile-image-section">
                    <div class="current-image">
                        <div class="image-container">
                            <img src="{{ Auth::user()->getProfileImageUrl() }}" alt="الصورة الشخصية" id="current-profile-image" class="profile-image">
                            <div class="image-overlay" onclick="triggerImageUpload()">
                                <i class="fas fa-camera"></i>
                                <span>تغيير الصورة</span>
                            </div>
                        </div>
                        <div class="image-info">
                            <h5>{{ Auth::user()->name }}</h5>
                            <p class="text-muted">{{ Auth::user()->email }}</p>
                            @if(Auth::user()->hasProfileImage())
                                <small class="text-success">
                                    <i class="fas fa-check-circle"></i>
                                    حجم الصورة: {{ Auth::user()->getProfileImageSizeForHumans() }}
                                </small>
                            @else
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    لم يتم رفع صورة شخصية
                                </small>
                            @endif
                        </div>
                    </div>

                    <div class="upload-controls">
                        <input type="file" id="profile-image-input" accept="image/*" style="display: none;">
                        <button type="button" class="btn btn-custom btn-primary-custom" onclick="triggerImageUpload()">
                            <i class="fas fa-upload"></i> رفع صورة جديدة
                        </button>
                        @if(Auth::user()->hasProfileImage())
                            <button type="button" class="btn btn-custom btn-danger-custom" onclick="deleteProfileImage()">
                                <i class="fas fa-trash"></i> حذف الصورة
                            </button>
                        @endif
                    </div>

                    <div class="upload-guidelines">
                        <h6><i class="fas fa-info-circle"></i> إرشادات الرفع:</h6>
                        <ul>
                            <li>الحد الأقصى لحجم الصورة: 5 ميجابايت</li>
                            <li>الأنواع المدعومة: JPG, PNG, GIF</li>
                            <li>الأبعاد المفضلة: 400x400 بكسل أو أكبر</li>
                            <li>سيتم ضغط الصور الكبيرة تلقائياً</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="settings-section">
                <h3 class="section-title">
                    <i class="fas fa-user-edit"></i> معلومات الملف الشخصي
                </h3>

                <form action="{{ route('user.settings.profile') }}" method="POST">
                    @csrf
                    @method('PUT')

                    <div class="form-group">
                        <label for="name" class="form-label">الاسم الكامل</label>
                        <input type="text" name="name" id="name" class="form-control"
                               value="{{ Auth::user()->name }}" required>
                    </div>

                    <div class="form-group">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" name="email" id="email" class="form-control"
                               value="{{ Auth::user()->email }}" required>
                    </div>

                    <div class="form-group">
                        <label for="phone" class="form-label">رقم الهاتف</label>
                        <input type="tel" name="phone" id="phone" class="form-control"
                               value="{{ Auth::user()->phone }}">
                    </div>

                    <button type="submit" class="btn btn-custom btn-primary-custom">
                        <i class="fas fa-save"></i> حفظ معلومات الملف الشخصي
                    </button>
                </form>
            </div>
        </div>

        <!-- تبويب الخصوصية -->
        <div id="privacy" class="tab-content">
            <form action="{{ route('user.settings.privacy') }}" method="POST">
                @csrf
                @method('PUT')

                <div class="settings-section">
                    <h3 class="section-title">
                        <i class="fas fa-comments"></i> إعدادات المراسلة
                    </h3>

                    <div class="form-check">
                        <input type="checkbox" name="allow_messages" id="allow_messages" class="form-check-input"
                               {{ $user->allow_messages ? 'checked' : '' }}>
                        <label for="allow_messages" class="form-check-label">
                            السماح بالمراسلة
                            <div class="form-check-description">السماح للمستخدمين الآخرين بإرسال رسائل خاصة إليك</div>
                        </label>
                    </div>

                    <div class="form-check">
                        <input type="checkbox" name="allow_comments" id="allow_comments" class="form-check-input"
                               {{ $user->allow_comments ? 'checked' : '' }}>
                        <label for="allow_comments" class="form-check-label">
                            السماح بالتعليقات
                            <div class="form-check-description">السماح للمستخدمين بالتعليق على إعلاناتك</div>
                        </label>
                    </div>
                </div>

                <div class="settings-section">
                    <h3 class="section-title">
                        <i class="fas fa-eye"></i> إعدادات العرض
                    </h3>

                    <div class="form-check">
                        <input type="checkbox" name="show_phone" id="show_phone" class="form-check-input"
                               {{ $user->show_phone ? 'checked' : '' }}>
                        <label for="show_phone" class="form-check-label">
                            إظهار رقم الهاتف
                            <div class="form-check-description">عرض رقم هاتفك في الملف الشخصي والإعلانات</div>
                        </label>
                    </div>

                    <div class="form-check">
                        <input type="checkbox" name="show_email" id="show_email" class="form-check-input"
                               {{ $user->show_email ? 'checked' : '' }}>
                        <label for="show_email" class="form-check-label">
                            إظهار البريد الإلكتروني
                            <div class="form-check-description">عرض بريدك الإلكتروني في الملف الشخصي</div>
                        </label>
                    </div>

                    <div class="form-check">
                        <input type="checkbox" name="show_online_status" id="show_online_status" class="form-check-input"
                               {{ $user->show_online_status ? 'checked' : '' }}>
                        <label for="show_online_status" class="form-check-label">
                            إظهار حالة الاتصال
                            <div class="form-check-description">إظهار ما إذا كنت متصلاً أم لا</div>
                        </label>
                    </div>
                </div>

                <div class="settings-section">
                    <h3 class="section-title">
                        <i class="fas fa-user"></i> إعدادات الملف الشخصي
                    </h3>

                    <div class="form-check">
                        <input type="checkbox" name="profile_public" id="profile_public" class="form-check-input"
                               {{ $user->profile_public ? 'checked' : '' }}>
                        <label for="profile_public" class="form-check-label">
                            الملف الشخصي عام
                            <div class="form-check-description">السماح لأي شخص بمشاهدة ملفك الشخصي</div>
                        </label>
                    </div>

                    <div class="form-check">
                        <input type="checkbox" name="show_ads_count" id="show_ads_count" class="form-check-input"
                               {{ $user->show_ads_count ? 'checked' : '' }}>
                        <label for="show_ads_count" class="form-check-label">
                            إظهار عدد الإعلانات
                            <div class="form-check-description">عرض عدد الإعلانات التي نشرتها</div>
                        </label>
                    </div>

                    <div class="form-check">
                        <input type="checkbox" name="show_join_date" id="show_join_date" class="form-check-input"
                               {{ $user->show_join_date ? 'checked' : '' }}>
                        <label for="show_join_date" class="form-check-label">
                            إظهار تاريخ الانضمام
                            <div class="form-check-description">عرض تاريخ انضمامك للموقع</div>
                        </label>
                    </div>

                    <div class="form-check">
                        <input type="checkbox" name="searchable_profile" id="searchable_profile" class="form-check-input"
                               {{ $user->searchable_profile ? 'checked' : '' }}>
                        <label for="searchable_profile" class="form-check-label">
                            قابلية البحث
                            <div class="form-check-description">السماح للآخرين بالعثور على ملفك الشخصي عبر البحث</div>
                        </label>
                    </div>

                    <div class="form-check">
                        <input type="checkbox" name="show_in_suggestions" id="show_in_suggestions" class="form-check-input"
                               {{ $user->show_in_suggestions ? 'checked' : '' }}>
                        <label for="show_in_suggestions" class="form-check-label">
                            الظهور في الاقتراحات
                            <div class="form-check-description">إظهار ملفك الشخصي في اقتراحات المستخدمين</div>
                        </label>
                    </div>
                </div>

                <button type="submit" class="btn btn-custom btn-primary-custom">
                    <i class="fas fa-save"></i> حفظ إعدادات الخصوصية
                </button>
            </form>
        </div>

        <!-- تبويب الإشعارات -->
        <div id="notifications" class="tab-content">
            <form action="{{ route('user.settings.notifications') }}" method="POST">
                @csrf
                @method('PUT')

                <div class="settings-section">
                    <h3 class="section-title">
                        <i class="fas fa-envelope"></i> إشعارات البريد الإلكتروني
                    </h3>

                    <div class="form-check">
                        <input type="checkbox" name="email_notifications" id="email_notifications" class="form-check-input"
                               {{ $user->email_notifications ? 'checked' : '' }}>
                        <label for="email_notifications" class="form-check-label">
                            تفعيل إشعارات البريد الإلكتروني
                            <div class="form-check-description">تلقي إشعارات عبر البريد الإلكتروني للأنشطة المهمة</div>
                        </label>
                    </div>

                    <div class="form-check">
                        <input type="checkbox" name="marketing_emails" id="marketing_emails" class="form-check-input"
                               {{ $user->marketing_emails ? 'checked' : '' }}>
                        <label for="marketing_emails" class="form-check-label">
                            رسائل التسويق
                            <div class="form-check-description">تلقي رسائل تسويقية وعروض خاصة</div>
                        </label>
                    </div>
                </div>

                <div class="settings-section">
                    <h3 class="section-title">
                        <i class="fas fa-mobile-alt"></i> الإشعارات المنبثقة
                    </h3>

                    <div class="form-check">
                        <input type="checkbox" name="push_notifications" id="push_notifications" class="form-check-input"
                               {{ $user->push_notifications ? 'checked' : '' }}>
                        <label for="push_notifications" class="form-check-label">
                            الإشعارات المنبثقة
                            <div class="form-check-description">تلقي إشعارات منبثقة في المتصفح</div>
                        </label>
                    </div>

                    <div class="form-check">
                        <input type="checkbox" name="sms_notifications" id="sms_notifications" class="form-check-input"
                               {{ $user->sms_notifications ? 'checked' : '' }}>
                        <label for="sms_notifications" class="form-check-label">
                            الرسائل النصية
                            <div class="form-check-description">تلقي إشعارات عبر الرسائل النصية</div>
                        </label>
                    </div>
                </div>

                <div class="settings-section">
                    <h3 class="section-title">
                        <i class="fas fa-shield-alt"></i> إشعارات الأمان
                    </h3>

                    <div class="form-check">
                        <input type="checkbox" name="login_alerts" id="login_alerts" class="form-check-input"
                               {{ $user->login_alerts ? 'checked' : '' }}>
                        <label for="login_alerts" class="form-check-label">
                            تنبيهات تسجيل الدخول
                            <div class="form-check-description">تلقي تنبيه عند تسجيل الدخول من جهاز جديد</div>
                        </label>
                    </div>
                </div>

                <button type="submit" class="btn btn-custom btn-primary-custom">
                    <i class="fas fa-save"></i> حفظ إعدادات الإشعارات
                </button>
            </form>
        </div>

        <!-- تبويب التفضيلات -->
        <div id="preferences" class="tab-content">
            <form action="{{ route('user.settings.preferences') }}" method="POST">
                @csrf
                @method('PUT')

                <div class="settings-section">
                    <h3 class="section-title">
                        <i class="fas fa-language"></i> اللغة والمنطقة
                    </h3>

                    <div class="form-group">
                        <label for="preferred_language" class="form-label">اللغة المفضلة</label>
                        <select name="preferred_language" id="preferred_language" class="form-control">
                            <option value="ar" {{ $user->preferred_language == 'ar' ? 'selected' : '' }}>العربية</option>
                            <option value="en" {{ $user->preferred_language == 'en' ? 'selected' : '' }}>English</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="timezone" class="form-label">المنطقة الزمنية</label>
                        <select name="timezone" id="timezone" class="form-control">
                            <option value="Asia/Riyadh" {{ $user->timezone == 'Asia/Riyadh' ? 'selected' : '' }}>الرياض (GMT+3)</option>
                            <option value="Asia/Dubai" {{ $user->timezone == 'Asia/Dubai' ? 'selected' : '' }}>دبي (GMT+4)</option>
                            <option value="Asia/Kuwait" {{ $user->timezone == 'Asia/Kuwait' ? 'selected' : '' }}>الكويت (GMT+3)</option>
                            <option value="Asia/Qatar" {{ $user->timezone == 'Asia/Qatar' ? 'selected' : '' }}>قطر (GMT+3)</option>
                            <option value="Asia/Bahrain" {{ $user->timezone == 'Asia/Bahrain' ? 'selected' : '' }}>البحرين (GMT+3)</option>
                        </select>
                    </div>
                </div>

                <div class="settings-section">
                    <h3 class="section-title">
                        <i class="fas fa-palette"></i> المظهر
                    </h3>

                    <div class="form-group">
                        <label for="theme_preference" class="form-label">تفضيل المظهر</label>
                        <select name="theme_preference" id="theme_preference" class="form-control">
                            <option value="light" {{ $user->theme_preference == 'light' ? 'selected' : '' }}>فاتح</option>
                            <option value="dark" {{ $user->theme_preference == 'dark' ? 'selected' : '' }}>داكن</option>
                            <option value="auto" {{ $user->theme_preference == 'auto' ? 'selected' : '' }}>تلقائي</option>
                        </select>
                    </div>
                </div>

                <button type="submit" class="btn btn-custom btn-primary-custom">
                    <i class="fas fa-save"></i> حفظ التفضيلات
                </button>
            </form>
        </div>

        <!-- تبويب الأمان -->
        <div id="security" class="tab-content">
            <!-- تغيير كلمة المرور -->
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="fas fa-key"></i> تغيير كلمة المرور
                </h3>

                <form action="{{ route('user.settings.password') }}" method="POST">
                    @csrf
                    @method('PUT')

                    <div class="form-group">
                        <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                        <input type="password" name="current_password" id="current_password" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                        <input type="password" name="new_password" id="new_password" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label for="new_password_confirmation" class="form-label">تأكيد كلمة المرور الجديدة</label>
                        <input type="password" name="new_password_confirmation" id="new_password_confirmation" class="form-control" required>
                    </div>

                    <button type="submit" class="btn btn-custom btn-primary-custom">
                        <i class="fas fa-save"></i> تحديث كلمة المرور
                    </button>
                </form>
            </div>

            <!-- المصادقة الثنائية -->
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="fas fa-shield-alt"></i> المصادقة الثنائية
                </h3>

                <div class="form-check">
                    <form action="{{ route('user.settings.two-factor') }}" method="POST" style="display: inline;">
                        @csrf
                        <button type="submit" class="btn btn-custom {{ $user->two_factor_enabled ? 'btn-danger-custom' : 'btn-primary-custom' }}">
                            <i class="fas fa-{{ $user->two_factor_enabled ? 'times' : 'check' }}"></i>
                            {{ $user->two_factor_enabled ? 'إلغاء تفعيل' : 'تفعيل' }} المصادقة الثنائية
                        </button>
                    </form>
                </div>

                <p class="text-muted mt-2">
                    {{ $user->two_factor_enabled ? 'المصادقة الثنائية مفعلة حالياً' : 'المصادقة الثنائية غير مفعلة' }}
                </p>
            </div>

            <!-- منطقة الخطر -->
            <div class="danger-zone">
                <h4><i class="fas fa-exclamation-triangle"></i> منطقة الخطر</h4>
                <p>العمليات التالية لا يمكن التراجع عنها. يرجى التأكد قبل المتابعة.</p>

                <button type="button" class="btn btn-danger-custom" onclick="showDeleteModal()">
                    <i class="fas fa-trash"></i> حذف الحساب نهائياً
                </button>
            </div>
        </div>

        <!-- تبويب المحظورون -->
        <div id="blocked" class="tab-content">
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="fas fa-user-slash"></i> المستخدمون المحظورون
                </h3>

                @if($blockedUsers && $blockedUsers->count() > 0)
                    <div class="blocked-users-list">
                        @foreach($blockedUsers as $blockedUser)
                            <div class="blocked-user-item">
                                <div class="user-info">
                                    <div class="user-avatar">
                                        {{ substr($blockedUser->name, 0, 1) }}
                                    </div>
                                    <div>
                                        <strong>{{ $blockedUser->name }}</strong>
                                        <div class="text-muted">{{ $blockedUser->email }}</div>
                                    </div>
                                </div>
                                <form action="{{ route('user.settings.unblock') }}" method="POST" style="display: inline;">
                                    @csrf
                                    <input type="hidden" name="user_id" value="{{ $blockedUser->id }}">
                                    <button type="submit" class="btn btn-sm btn-secondary-custom">
                                        <i class="fas fa-unlock"></i> إلغاء الحظر
                                    </button>
                                </form>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-users text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3">لا توجد مستخدمون محظورون</p>
                    </div>
                @endif

                <!-- إضافة مستخدم للحظر -->
                <div class="mt-4">
                    <h5>حظر مستخدم جديد</h5>
                    <form action="{{ route('user.settings.block') }}" method="POST" class="d-flex gap-2">
                        @csrf
                        <input type="number" name="user_id" class="form-control" placeholder="ID المستخدم" required>
                        <button type="submit" class="btn btn-danger-custom">
                            <i class="fas fa-ban"></i> حظر
                        </button>
                    </form>
                    <small class="text-muted">أدخل ID المستخدم الذي تريد حظره</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal حذف الحساب -->
<div class="modal fade" id="deleteAccountModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle"></i> تأكيد حذف الحساب
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <strong>تحذير!</strong> هذا الإجراء لا يمكن التراجع عنه.
                </div>

                <p>سيتم حذف جميع بياناتك نهائياً بما في ذلك:</p>
                <ul>
                    <li>الملف الشخصي</li>
                    <li>جميع الإعلانات</li>
                    <li>المحادثات والرسائل</li>
                    <li>التعليقات والتقييمات</li>
                </ul>

                <form action="{{ route('user.settings.delete') }}" method="POST" id="deleteAccountForm">
                    @csrf
                    @method('DELETE')

                    <div class="form-group mb-3">
                        <label for="delete_password" class="form-label">أدخل كلمة المرور للتأكيد:</label>
                        <input type="password" name="password" id="delete_password" class="form-control" required>
                    </div>

                    <div class="form-group mb-3">
                        <label for="delete_confirmation" class="form-label">اكتب "DELETE" للتأكيد:</label>
                        <input type="text" name="confirmation" id="delete_confirmation" class="form-control" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="submit" form="deleteAccountForm" class="btn btn-danger">
                    <i class="fas fa-trash"></i> حذف الحساب نهائياً
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function showTab(tabName) {
    // إخفاء جميع التبويبات
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(tab => tab.classList.remove('active'));

    // إزالة الفئة النشطة من جميع الأزرار
    const tabBtns = document.querySelectorAll('.tab-btn');
    tabBtns.forEach(btn => btn.classList.remove('active'));

    // إظهار التبويب المحدد
    document.getElementById(tabName).classList.add('active');

    // إضافة الفئة النشطة للزر المحدد
    event.target.classList.add('active');
}

function showDeleteModal() {
    const modal = new bootstrap.Modal(document.getElementById('deleteAccountModal'));
    modal.show();
}

// التحقق من صحة نموذج حذف الحساب
document.getElementById('deleteAccountForm').addEventListener('submit', function(e) {
    const confirmation = document.getElementById('delete_confirmation').value;
    if (confirmation !== 'DELETE') {
        e.preventDefault();
        alert('يجب كتابة "DELETE" بالضبط للتأكيد');
        return false;
    }

    if (!confirm('هل أنت متأكد من حذف حسابك نهائياً؟ هذا الإجراء لا يمكن التراجع عنه!')) {
        e.preventDefault();
        return false;
    }
});

// تحديث آخر ظهور
function updateLastSeen() {
    fetch('{{ url("/user/update-last-seen") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        }
    }).catch(error => {
        console.log('خطأ في تحديث آخر ظهور:', error);
    });
}

// تحديث آخر ظهور كل دقيقة
setInterval(updateLastSeen, 60000);

// تحديث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', updateLastSeen);

// Profile Image Upload Functions
function triggerImageUpload() {
    document.getElementById('profile-image-input').click();
}

// Handle image upload
document.getElementById('profile-image-input').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
        alert('نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, أو GIF');
        return;
    }

    // Validate file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
        alert('حجم الصورة كبير جداً. الحد الأقصى 5 ميجابايت');
        return;
    }

    // Show loading state
    const container = document.querySelector('.profile-image-section');
    container.classList.add('uploading');

    // Create FormData
    const formData = new FormData();
    formData.append('profile_image', file);
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

    // Upload image
    fetch('{{ route("user.profile-image.upload") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        container.classList.remove('uploading');

        if (data.success) {
            // Update image
            document.getElementById('current-profile-image').src = data.image_url;

            // Update size info
            const sizeInfo = document.querySelector('.image-info small');
            if (sizeInfo) {
                sizeInfo.innerHTML = '<i class="fas fa-check-circle"></i> حجم الصورة: ' + data.image_size;
                sizeInfo.className = 'text-success';
            }

            // Show delete button if not visible
            const deleteBtn = document.querySelector('[onclick="deleteProfileImage()"]');
            if (deleteBtn) {
                deleteBtn.style.display = 'inline-block';
            }

            // Show success message
            showNotification('تم رفع الصورة الشخصية بنجاح!', 'success');
        } else {
            showNotification(data.message || 'حدث خطأ أثناء رفع الصورة', 'error');
        }
    })
    .catch(error => {
        container.classList.remove('uploading');
        console.error('Error:', error);
        showNotification('حدث خطأ أثناء رفع الصورة', 'error');
    });

    // Reset input
    e.target.value = '';
});

// Delete profile image
function deleteProfileImage() {
    if (!confirm('هل أنت متأكد من حذف الصورة الشخصية؟')) {
        return;
    }

    fetch('{{ route("user.profile-image.delete") }}', {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update image to default
            document.getElementById('current-profile-image').src = data.default_avatar;

            // Update size info
            const sizeInfo = document.querySelector('.image-info small');
            if (sizeInfo) {
                sizeInfo.innerHTML = '<i class="fas fa-info-circle"></i> لم يتم رفع صورة شخصية';
                sizeInfo.className = 'text-muted';
            }

            // Hide delete button
            const deleteBtn = document.querySelector('[onclick="deleteProfileImage()"]');
            if (deleteBtn) {
                deleteBtn.style.display = 'none';
            }

            showNotification('تم حذف الصورة الشخصية بنجاح!', 'success');
        } else {
            showNotification(data.message || 'حدث خطأ أثناء حذف الصورة', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('حدث خطأ أثناء حذف الصورة', 'error');
    });
}

// Show notification
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification-toast');
    existingNotifications.forEach(notification => notification.remove());

    // Create notification
    const notification = document.createElement('div');
    notification.className = `notification-toast alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;

    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Preview image before upload
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('current-profile-image').src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    }
}
</script>
@endsection
