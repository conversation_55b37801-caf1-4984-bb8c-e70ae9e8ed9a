<?php

// تشخيص مشكلة زر الحفظ
// تشغيل: php debug_save_system.php

require_once 'vendor/autoload.php';

// تحميل إعدادات Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\SavedItem;
use App\Models\Ad;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Route;

echo "🔍 تشخيص مشكلة زر الحفظ\n";
echo "========================\n\n";

try {
    // 1. فحص الجدول
    echo "1️⃣ فحص جدول saved_items:\n";
    if (Schema::hasTable('saved_items')) {
        echo "   ✅ الجدول موجود\n";
        
        $columns = Schema::getColumnListing('saved_items');
        echo "   📋 الأعمدة: " . implode(', ', $columns) . "\n";
        
        $count = SavedItem::count();
        echo "   📊 عدد السجلات: {$count}\n";
    } else {
        echo "   ❌ الجدول غير موجود\n";
        exit(1);
    }

    // 2. فحص النموذج
    echo "\n2️⃣ فحص نموذج SavedItem:\n";
    if (class_exists('App\Models\SavedItem')) {
        echo "   ✅ النموذج موجود\n";
        
        // اختبار الدوال
        $methods = ['isSaved', 'saveItem', 'unsaveItem', 'toggleSave', 'getSavedStats'];
        foreach ($methods as $method) {
            if (method_exists(SavedItem::class, $method)) {
                echo "   ✅ دالة {$method} موجودة\n";
            } else {
                echo "   ❌ دالة {$method} مفقودة\n";
            }
        }
    } else {
        echo "   ❌ النموذج غير موجود\n";
        exit(1);
    }

    // 3. فحص Controller
    echo "\n3️⃣ فحص SavedItemController:\n";
    if (class_exists('App\Http\Controllers\SavedItemController')) {
        echo "   ✅ Controller موجود\n";
    } else {
        echo "   ❌ Controller غير موجود\n";
        exit(1);
    }

    // 4. فحص الروابط
    echo "\n4️⃣ فحص الروابط:\n";
    
    // تحميل الروابط
    $routes = Route::getRoutes();
    $savedRoutes = [];
    
    foreach ($routes as $route) {
        $uri = $route->uri();
        if (strpos($uri, 'saved') !== false) {
            $savedRoutes[] = [
                'method' => implode('|', $route->methods()),
                'uri' => $uri,
                'name' => $route->getName(),
                'action' => $route->getActionName()
            ];
        }
    }
    
    if (!empty($savedRoutes)) {
        echo "   ✅ روابط نظام الحفظ موجودة:\n";
        foreach ($savedRoutes as $route) {
            echo "   📍 {$route['method']} /{$route['uri']} → {$route['name']}\n";
        }
    } else {
        echo "   ❌ لا توجد روابط لنظام الحفظ\n";
        echo "   🔧 تشغيل: php artisan route:clear\n";
    }

    // 5. اختبار دالة toggleSave
    echo "\n5️⃣ اختبار دالة toggleSave:\n";
    
    // البحث عن أول إعلان متاح
    $firstAd = Ad::first();
    if ($firstAd) {
        echo "   📢 اختبار مع الإعلان ID: {$firstAd->id}\n";
        
        $testUserId = 1; // افتراض وجود مستخدم
        
        try {
            // اختبار الحفظ
            $saved = SavedItem::toggleSave($testUserId, 'ad', $firstAd->id);
            echo "   ✅ دالة toggleSave تعمل - النتيجة: " . ($saved ? 'محفوظ' : 'غير محفوظ') . "\n";
            
            // اختبار التحقق
            $isSaved = SavedItem::isSaved($testUserId, 'ad', $firstAd->id);
            echo "   ✅ دالة isSaved تعمل - النتيجة: " . ($isSaved ? 'محفوظ' : 'غير محفوظ') . "\n";
            
            // إلغاء الحفظ للتنظيف
            SavedItem::unsaveItem($testUserId, 'ad', $firstAd->id);
            echo "   🧹 تم تنظيف البيانات التجريبية\n";
            
        } catch (Exception $e) {
            echo "   ❌ خطأ في اختبار toggleSave: " . $e->getMessage() . "\n";
        }
    } else {
        echo "   ⚠️ لا توجد إعلانات للاختبار\n";
    }

    // 6. فحص ملفات Views
    echo "\n6️⃣ فحص ملفات Views:\n";
    
    $viewFiles = [
        'resources/views/components/save-button.blade.php' => 'زر الحفظ',
        'resources/views/saved/index.blade.php' => 'صفحة المحفوظات'
    ];
    
    foreach ($viewFiles as $file => $description) {
        if (file_exists($file)) {
            echo "   ✅ {$description} موجود\n";
        } else {
            echo "   ❌ {$description} غير موجود ({$file})\n";
        }
    }

    // 7. فحص JavaScript في زر الحفظ
    echo "\n7️⃣ فحص JavaScript:\n";
    
    $saveButtonFile = 'resources/views/components/save-button.blade.php';
    if (file_exists($saveButtonFile)) {
        $content = file_get_contents($saveButtonFile);
        
        $jsChecks = [
            '/saved/toggle' => 'رابط toggle',
            '/saved/check' => 'رابط check',
            'X-CSRF-TOKEN' => 'CSRF token',
            'handleSaveClick' => 'دالة handleSaveClick',
            'checkSavedStatus' => 'دالة checkSavedStatus'
        ];
        
        foreach ($jsChecks as $check => $description) {
            if (strpos($content, $check) !== false) {
                echo "   ✅ {$description} موجود\n";
            } else {
                echo "   ❌ {$description} مفقود\n";
            }
        }
    }

    // 8. اختبار إنشاء سجل جديد
    echo "\n8️⃣ اختبار إنشاء سجل محفوظ:\n";
    
    if ($firstAd) {
        try {
            $testUserId = 999; // مستخدم تجريبي
            
            // محاولة الحفظ المباشر
            $savedItem = SavedItem::create([
                'user_id' => $testUserId,
                'item_type' => 'ad',
                'item_id' => $firstAd->id,
                'saved_at' => now()
            ]);
            
            if ($savedItem) {
                echo "   ✅ تم إنشاء سجل محفوظ بنجاح (ID: {$savedItem->id})\n";
                
                // حذف السجل التجريبي
                $savedItem->delete();
                echo "   🧹 تم حذف السجل التجريبي\n";
            }
            
        } catch (Exception $e) {
            echo "   ❌ خطأ في إنشاء سجل محفوظ: " . $e->getMessage() . "\n";
            
            // فحص إذا كان الخطأ بسبب foreign key
            if (strpos($e->getMessage(), 'foreign key') !== false) {
                echo "   💡 المشكلة: المستخدم التجريبي غير موجود\n";
                echo "   🔧 الحل: استخدم معرف مستخدم حقيقي\n";
            }
        }
    }

    // 9. فحص إعدادات قاعدة البيانات
    echo "\n9️⃣ فحص إعدادات قاعدة البيانات:\n";
    
    try {
        $connection = \DB::connection();
        $databaseName = $connection->getDatabaseName();
        echo "   ✅ متصل بقاعدة البيانات: {$databaseName}\n";
        
        // فحص foreign key constraints
        $foreignKeys = \DB::select("
            SELECT CONSTRAINT_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME 
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_NAME = 'saved_items' 
            AND CONSTRAINT_NAME != 'PRIMARY'
        ");
        
        if (!empty($foreignKeys)) {
            echo "   ✅ Foreign key constraints موجودة:\n";
            foreach ($foreignKeys as $fk) {
                echo "   🔗 {$fk->COLUMN_NAME} → {$fk->REFERENCED_TABLE_NAME}.{$fk->REFERENCED_COLUMN_NAME}\n";
            }
        } else {
            echo "   ⚠️ لا توجد foreign key constraints\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "\n";
    }

    echo "\n🎯 خلاصة التشخيص:\n";
    echo "==================\n";
    
    $issues = [];
    
    // فحص المتطلبات الأساسية
    if (!Schema::hasTable('saved_items')) {
        $issues[] = "جدول saved_items غير موجود";
    }
    
    if (!class_exists('App\Models\SavedItem')) {
        $issues[] = "نموذج SavedItem غير موجود";
    }
    
    if (!class_exists('App\Http\Controllers\SavedItemController')) {
        $issues[] = "SavedItemController غير موجود";
    }
    
    if (empty($savedRoutes)) {
        $issues[] = "روابط نظام الحفظ غير محملة";
    }
    
    if (!file_exists('resources/views/components/save-button.blade.php')) {
        $issues[] = "component زر الحفظ غير موجود";
    }
    
    if (empty($issues)) {
        echo "🎉 جميع المكونات موجودة ومكتملة!\n\n";
        
        echo "🔧 خطوات استكشاف الأخطاء:\n";
        echo "1. تأكد من تسجيل الدخول\n";
        echo "2. افتح Developer Tools في المتصفح (F12)\n";
        echo "3. انتقل إلى تبويب Console\n";
        echo "4. اضغط على زر الحفظ وراقب الأخطاء\n";
        echo "5. انتقل إلى تبويب Network وراقب طلبات AJAX\n\n";
        
        echo "🌐 روابط للاختبار:\n";
        echo "- /saved - صفحة المحفوظات\n";
        echo "- أي صفحة إعلان مع زر الحفظ\n";
        
    } else {
        echo "⚠️ مشاكل تحتاج إلى حل:\n";
        foreach ($issues as $issue) {
            echo "❌ {$issue}\n";
        }
        
        echo "\n🔧 حلول مقترحة:\n";
        echo "1. php artisan migrate\n";
        echo "2. php artisan route:clear\n";
        echo "3. php artisan config:clear\n";
        echo "4. composer dump-autoload\n";
    }

} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
    echo "\nتفاصيل الخطأ:\n";
    echo $e->getTraceAsString() . "\n";
}

echo "\n📚 للمزيد من المساعدة:\n";
echo "======================\n";
echo "- راجع ملف SAVE_SYSTEM_DOCUMENTATION.md\n";
echo "- راجع ملف SAVE_SYSTEM_SETUP.md\n";
echo "- تحقق من logs في storage/logs/laravel.log\n";

?>
