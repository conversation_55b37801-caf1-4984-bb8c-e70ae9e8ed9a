<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SavedItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'item_type',
        'item_id',
        'saved_at'
    ];

    protected $casts = [
        'saved_at' => 'datetime',
    ];

    /**
     * العلاقة مع المستخدم
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * الحصول على العنصر المحفوظ (polymorphic)
     */
    public function getItemAttribute()
    {
        switch ($this->item_type) {
            case 'ad':
                return Ad::find($this->item_id);
            case 'job':
                return JobPosting::find($this->item_id);
            case 'job_seeker':
                return JobSeeker::find($this->item_id);
            default:
                return null;
        }
    }

    /**
     * التحقق من وجود عنصر محفوظ
     */
    public static function isSaved($userId, $itemType, $itemId)
    {
        return self::where('user_id', $userId)
            ->where('item_type', $itemType)
            ->where('item_id', $itemId)
            ->exists();
    }

    /**
     * حفظ عنصر جديد
     */
    public static function saveItem($userId, $itemType, $itemId)
    {
        try {
            return self::create([
                'user_id' => $userId,
                'item_type' => $itemType,
                'item_id' => $itemId,
                'saved_at' => now(),
            ]);
        } catch (\Exception $e) {
            // في حالة التكرار أو أي خطأ آخر
            return false;
        }
    }

    /**
     * إلغاء حفظ عنصر
     */
    public static function unsaveItem($userId, $itemType, $itemId)
    {
        return self::where('user_id', $userId)
            ->where('item_type', $itemType)
            ->where('item_id', $itemId)
            ->delete();
    }

    /**
     * تبديل حالة الحفظ
     */
    public static function toggleSave($userId, $itemType, $itemId)
    {
        if (self::isSaved($userId, $itemType, $itemId)) {
            self::unsaveItem($userId, $itemType, $itemId);
            return false; // تم إلغاء الحفظ
        } else {
            self::saveItem($userId, $itemType, $itemId);
            return true; // تم الحفظ
        }
    }

    /**
     * الحصول على العناصر المحفوظة للمستخدم
     */
    public static function getSavedItems($userId, $itemType = null, $limit = null)
    {
        $query = self::where('user_id', $userId)
            ->orderBy('saved_at', 'desc');

        if ($itemType) {
            $query->where('item_type', $itemType);
        }

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * الحصول على إحصائيات العناصر المحفوظة
     */
    public static function getSavedStats($userId)
    {
        return [
            'total' => self::where('user_id', $userId)->count(),
            'ads' => self::where('user_id', $userId)->where('item_type', 'ad')->count(),
            'jobs' => self::where('user_id', $userId)->where('item_type', 'job')->count(),
            'job_seekers' => self::where('user_id', $userId)->where('item_type', 'job_seeker')->count(),
        ];
    }

    /**
     * الحصول على عنوان العنصر
     */
    public function getItemTitleAttribute()
    {
        $item = $this->item;

        if (!$item) {
            return 'عنصر محذوف';
        }

        switch ($this->item_type) {
            case 'ad':
                return $item->title;
            case 'job':
                return $item->title;
            case 'job_seeker':
                return $item->user->name ?? 'باحث عن عمل';
            default:
                return 'غير محدد';
        }
    }

    /**
     * الحصول على رابط العنصر
     */
    public function getItemUrlAttribute()
    {
        switch ($this->item_type) {
            case 'ad':
                return route('ads.show', $this->item_id);
            case 'job':
                return route('jobs.show', $this->item_id);
            case 'job_seeker':
                return route('job-seekers.show', $this->item_id);
            default:
                return '#';
        }
    }

    /**
     * الحصول على نوع العنصر بالعربية
     */
    public function getItemTypeArabicAttribute()
    {
        $types = [
            'ad' => 'إعلان',
            'job' => 'وظيفة',
            'job_seeker' => 'باحث عن عمل'
        ];

        return $types[$this->item_type] ?? 'غير محدد';
    }
}
