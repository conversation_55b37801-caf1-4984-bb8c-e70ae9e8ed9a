
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بحث الوظائف | منصة التوظيف</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
   <style>
        :root {
            --primary-color: #1e40af;
            --primary-light: #3b82f6;
            --primary-dark: #1e3a8a;
            --secondary-color: #059669;
            --accent-color: #f59e0b;
            --light-bg: #f8fafc;
            --card-bg: #ffffff;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --text-white: #ffffff;
            --border-color: #e5e7eb;
            --success-color: #10b981;
            --shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Cairo', 'Tajawal', sans-serif;
        }

        body {
            background-color: var(--light-bg);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background: linear-gradient(to left, var(--primary-color), var(--primary-dark));
            color: var(--text-white);
            padding: 1rem 0;
            box-shadow: var(--shadow);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.75rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .logo i {
            font-size: 2rem;
            color: var(--accent-color);
        }

        nav ul {
            display: flex;
            list-style: none;
            gap: 1rem;
        }

        nav a {
            color: var(--text-white);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        nav a:hover, nav a.active {
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        nav a i {
            font-size: 1.1rem;
            color: var(--accent-color);
        }

        .auth-buttons {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.6rem 1.25rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .btn-login {
            background-color: transparent;
            color: var(--text-white);
            border: 2px solid var(--text-white);
        }

        .btn-login:hover {
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .btn-signup {
            background-color: var(--accent-color);
            color: var(--text-dark);
            font-weight: 700;
        }

        .btn-signup:hover {
            background-color: #f3a533;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        /* Search Section */
        .search-section {
            padding: 3rem 0;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            position: relative;
            overflow: hidden;
        }

        /* Animation pattern for background */
        .search-section::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 100%),
                radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
            z-index: 1;
        }

        .search-section .container {
            position: relative;
            z-index: 2;
        }

        .search-title {
            text-align: center;
            margin-bottom: 2.5rem;
            color: var(--text-white);
        }

        .search-title h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: 800;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.3);
        }

        .search-title p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
            margin-bottom: 1.5rem;
        }

        .admin-action {
            margin-bottom: 2rem;
        }

        .admin-action .btn-signup {
            font-size: 1.1rem;
            padding: 0.75rem 1.5rem;
            background: var(--secondary-color);
            color: white;
        }

        .admin-action .btn-signup:hover {
            background: #047857;
        }

        .search-form {
            display: flex;
            gap: 0.5rem;
            max-width: 900px;
            margin: 0 auto;
            background-color: var(--card-bg);
            padding: 1.5rem;
            border-radius: 16px;
            box-shadow: var(--shadow);
            position: relative;
        }

        .form-group {
            flex: 1;
            position: relative;
        }

        .form-group i {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--primary-light);
            font-size: 1.2rem;
        }

        .search-form input,
        .search-form select {
            width: 100%;
            padding: 0.9rem 2.5rem 0.9rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s;
            background-color: #f9fafb;
        }

        .search-form input:focus,
        .search-form select:focus {
            border-color: var(--primary-light);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
            outline: none;
            background-color: white;
        }

        .search-form button {
            background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
            color: var(--text-white);
            padding: 0.9rem 2rem;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: var(--transition);
            font-weight: 600;
            font-size: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }

        .search-form button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(0,0,0,0.15);
        }

        /* Jobs Section */
        .jobs-section {
            padding: 3rem 0;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e5e7eb;
        }

        .section-header h2 {
            font-size: 1.8rem;
            color: var(--primary-dark);
            font-weight: 700;
            position: relative;
            padding-right: 1rem;
        }

        .section-header h2::before {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            height: 100%;
            width: 4px;
            background-color: var(--accent-color);
            border-radius: 2px;
        }

        .job-count {
            background-color: #e0f2fe;
            color: var(--primary-dark);
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 1.5rem;
            display: inline-block;
        }

        .sort-options {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            background: white;
            padding: 0.5rem 1rem;
            border-radius: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            border: 1px solid #e5e7eb;
        }

        .sort-options span {
            color: var(--text-light);
            font-weight: 600;
        }

        .sort-options select {
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--card-bg);
            color: var(--primary-dark);
            font-weight: 500;
            cursor: pointer;
        }

        .job-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
        }

        .job-card {
            background-color: var(--card-bg);
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
            padding: 1.75rem;
            transition: var(--transition);
            border: 1px solid #f1f5f9;
            position: relative;
            overflow: hidden;
            opacity: 0;
            animation: fadeIn 0.5s ease forwards;
        }

        .job-card:hover {
            transform: translateY(-7px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            border-color: rgba(59, 130, 246, 0.3);
        }

        .job-card::before {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            width: 6px;
            height: 100%;
            background: linear-gradient(to bottom, var(--primary-color), var(--primary-light));
            border-radius: 0 16px 16px 0;
        }

        .job-badge {
            position: absolute;
            top: 1.5rem;
            left: 1.5rem;
            background: linear-gradient(to right, #fde68a, #fbbf24);
            color: #92400e;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 700;
            box-shadow: 0 2px 5px rgba(251, 191, 36, 0.3);
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }

        .job-badge i {
            font-size: 0.7rem;
        }

        .job-card h3 {
            color: var(--primary-dark);
            margin-bottom: 0.75rem;
            font-size: 1.35rem;
            line-height: 1.3;
        }

        .job-company {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1.25rem;
        }

        .company-logo {
            width: 50px;
            height: 50px;
            background: linear-gradient(to bottom right, var(--primary-light), var(--primary-dark));
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-white);
            font-weight: 700;
            font-size: 1.5rem;
            box-shadow: 0 3px 8px rgba(30, 64, 175, 0.2);
        }

        .job-details {
            margin-bottom: 1.25rem;
            background-color: #f8fafc;
            border-radius: 10px;
            padding: 1rem;
        }

        .job-detail {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.75rem;
            color: var(--text-dark);
        }

        .job-detail i {
            color: var(--primary-light);
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }

        .job-description {
            margin-top: 1.25rem;
            margin-bottom: 1.5rem;
            color: var(--text-light);
            line-height: 1.6;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            position: relative;
            padding-bottom: 0.5rem;
        }

        .job-description::after {
            content: "...";
            position: absolute;
            bottom: 0;
            right: 0;
            background-color: var(--card-bg);
            padding-left: 0.3rem;
        }

        .job-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1.5rem;
            gap: 0.75rem;
        }

        .btn-apply {
            background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
            color: var(--text-white);
            flex: 1;
            transition: all 0.3s;
        }

        .btn-apply:hover {
            background: linear-gradient(to right, var(--primary-dark), var(--primary-color));
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(30, 64, 175, 0.3);
        }

        .btn-save {
            background-color: #f1f5f9;
            color: var(--text-light);
            border: 1px solid #e2e8f0;
            padding: 0.6rem;
            border-radius: 8px;
        }

        .btn-save:hover {
            background-color: #e2e8f0;
            color: var(--primary-dark);
        }
        
        .contact-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            margin-top: 1.25rem;
        }
        
        .btn-whatsapp {
            background-color: #25D366;
            color: var(--text-white);
            flex: 1;
            transition: all 0.3s;
        }
        
        .btn-whatsapp:hover {
            background-color: #22c55e;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(34, 197, 94, 0.3);
        }
        
        .btn-email {
            background-color: #EA4335;
            color: var(--text-white);
            flex: 1;
            transition: all 0.3s;
        }
        
        .btn-email:hover {
            background-color: #dc2626;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(220, 38, 38, 0.3);
        }
        
        .btn-phone {
            background-color: #0284c7;
            color: var(--text-white);
            flex: 1;
            transition: all 0.3s;
        }
        
        .btn-phone:hover {
            background-color: #0369a1;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(3, 105, 161, 0.3);
        }

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 3rem;
            background-color: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        }

        .pagination-inner {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .page-nav {
            padding: 0.7rem 1.5rem;
            background: linear-gradient(to right, var(--primary-light), var(--primary-color));
            color: white;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .page-nav:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(59, 130, 246, 0.3);
        }

        .page-nav.disabled {
            opacity: 0.5;
            pointer-events: none;
        }

        .page-number {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            color: var(--primary-dark);
            font-weight: 600;
            transition: all 0.3s;
            text-decoration: none;
            border: 1px solid #e5e7eb;
        }

        .page-number:hover {
            background-color: #f1f5f9;
            border-color: #cbd5e1;
        }

        .page-number.active {
            background: linear-gradient(to right, var(--primary-light), var(--primary-color));
            color: white;
            border: none;
            box-shadow: 0 3px 10px rgba(59, 130, 246, 0.3);
        }

        /* Empty state */
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background-color: var(--card-bg);
            border-radius: 16px;
            box-shadow: var(--shadow);
        }

        .empty-state i {
            font-size: 4rem;
            color: #cbd5e1;
            margin-bottom: 1.5rem;
            display: block;
        }

        .empty-state h3 {
            margin-bottom: 1rem;
            color: var(--text-dark);
            font-size: 1.75rem;
        }

        .empty-state p {
            color: var(--text-light);
            margin-bottom: 2rem;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
            font-size: 1.1rem;
        }

        .empty-state .btn {
            font-size: 1.1rem;
            padding: 0.75rem 2rem;
        }

        /* Footer */
        footer {
            background: linear-gradient(to right, #1e293b, #0f172a);
            color: var(--text-white);
            padding: 4rem 0 2rem;
            margin-top: 4rem;
            position: relative;
            overflow: hidden;
        }

        footer::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(to right, var(--primary-light), var(--accent-color));
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 3rem;
        }

        .footer-column h3 {
            margin-bottom: 1.5rem;
            color: var(--text-white);
            font-size: 1.3rem;
            position: relative;
            padding-bottom: 0.75rem;
        }

        .footer-column h3::after {
            content: "";
            position: absolute;
            bottom: 0;
            right: 0;
            width: 50px;
            height: 3px;
            background-color: var(--accent-color);
            border-radius: 2px;
        }

        .footer-links {
            list-style: none;
        }

        .footer-links li {
            margin-bottom: 0.75rem;
        }

        .footer-links a {
            color: #cbd5e1;
            text-decoration: none;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .footer-links a::before {
            content: "\f054";
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            font-size: 0.7rem;
            color: var(--accent-color);
        }

        .footer-links a:hover {
            color: var(--text-white);
            transform: translateX(-5px);
        }

        .footer-bottom {
            margin-top: 3rem;
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #334155;
            color: #94a3b8;
        }

        /* Animations */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animated {
            animation: fadeIn 0.5s ease forwards;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1.5rem;
                padding: 1rem 0;
            }

            nav ul {
                flex-wrap: wrap;
                justify-content: center;
                gap: 0.5rem;
            }
            
            nav a {
                padding: 0.5rem;
                font-size: 0.9rem;
            }

            .search-title h1 {
                font-size: 1.8rem;
            }

            .search-form {
                flex-direction: column;
                padding: 1rem;
            }
            
            .form-group {
                margin-bottom: 0.75rem;
            }

            .job-cards {
                grid-template-columns: 1fr;
            }

            .section-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
            
            .job-actions {
                flex-direction: column;
                align-items: stretch;
            }
            
            .contact-buttons {
                flex-direction: column;
            }
            
            .pagination-inner {
                flex-wrap: wrap;
                justify-content: center;
            }
        }

        @media (max-width: 576px) {
            .logo {
                font-size: 1.4rem;
            }
            
            .auth-buttons {
                flex-direction: column;
                gap: 0.5rem;
                width: 100%;
            }
            
            .auth-buttons .btn {
                width: 100%;
            }
            
            .search-title h1 {
                font-size: 1.5rem;
            }
            
            .search-title p {
                font-size: 1rem;
            }
            
            .search-form button {
                width: 100%;
                justify-content: center;
            }
            
            .pagination {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>

 

 
    <!-- Header -->
    <header>
        <div class="container">
            <div class="header-content">
                <!-- <div class="logo">
                    <i class="fas fa-briefcase"></i>
                    <span>منصة إنشر</span>
                </div> -->
                <nav>
                    <ul>
                        <li><a href="{{ url('/') }}" ><i class="fas fa-home"  ></i> الرئيسية</a></li>
                        <!-- <li><a href="{{ route('job_seekers.index') }}" class="active"><i class="fas fa-search"></i>   الوظائف</a></li> -->
                        <li><a href="{{ route('job_seekers.index') }}" ><i class="fas fa-building"></i> الباحثين عن عمل </a></li>
                        <li><a href="{{ route('jobs.index') }}" class="active"><i class="fas fa-search"></i>   الوظائف</a></li>
                      
                        <!-- <li><a href=" "><i class="fas fa-newspaper"></i> المدونة</a></li> -->
                        <!-- <li><a href=" "><i class="fas fa-headset"></i> الدعم</a></li> -->

                        <li><a href="{{ route('ads.index') }}"><i class="fas fa-bullhorn"></i> الإعلانات</a></li>
                       


                        <li>
    <a href="{{ url('/dashboard') }}" class="flex items-center space-x-2 text-blue-500 hover:text-blue-700 border border-transparent hover:border-blue-500 px-4 py-2 rounded-lg bg-transparent hover:bg-blue-100 transition-all duration-300">
        <i class="fas fa-tachometer-alt"></i> <!-- أيقونة جديدة -->
        <span>لوحة التحكم</span>
    </a>
</li>


</ul>
                </nav>

 


               
             
            </div>
            
        </div>
    </header>
    </header>



    



<main class="flex-grow container mx-auto px-4 py-8">
        <!-- البحث والفلترة (كما في النسخة السابقة) -->
        <form method="GET" action="{{ route('job_seekers.index') }}" class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <div>
        <label class="block text-gray-700 mb-2">الخبرة</label>
        <select name="experience" class="w-full p-3 border border-gray-300 rounded-lg">
            <option value="">كل مستويات الخبرة</option>
            <option value="0-2" {{ request('experience') == '0-2' ? 'selected' : '' }}>0-2 سنة</option>
            <option value="2-5" {{ request('experience') == '2-5' ? 'selected' : '' }}>2-5 سنوات</option>
            <option value="5+" {{ request('experience') == '5+' ? 'selected' : '' }}>5+ سنوات</option>
        </select>
    </div>

    <div>
        <label class="block text-gray-700 mb-2">المدينة</label>
        <select name="location" class="w-full p-3 border border-gray-300 rounded-lg">
            <option value="">كل المدن</option>
            <option value="الرياض" {{ request('location') == 'الرياض' ? 'selected' : '' }}>الرياض</option>
            <option value="جدة" {{ request('location') == 'جدة' ? 'selected' : '' }}>جدة</option>
            <option value="مكة" {{ request('location') == 'مكة' ? 'selected' : '' }}>مكة</option>
            <!-- أضف باقي المدن -->
        </select>
    </div>

    <div>
        <label class="block text-gray-700 mb-2">البحث</label>
        <div class="flex">
            <input type="text" name="keyword" value="{{ request('keyword') }}" placeholder="المسمى الوظيفي أو المهارات"
                   class="w-full p-3 border border-gray-300 rounded-r-lg">
            <button type="submit" class="bg-blue-500 text-white px-6 rounded-l-lg hover:bg-blue-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                </svg>
            </button>
        </div>
    </div>
</form>


        <!-- عنوان الصفحة -->
        <h1 class="text-4xl font-bold text-center text-gray-800 mb-12 tracking-wide bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-teal-400">الباحثين عن عمل</h1>

        @if($jobSeekers->isEmpty())
            <!-- حالة عدم وجود نتائج (كما في النسخة السابقة) -->
            <div class="text-center bg-white shadow-2xl rounded-2xl p-12 max-w-xl mx-auto">
                <svg class="mx-auto h-24 w-24 text-blue-300 mb-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p class="mt-4 text-2xl text-gray-500 font-bold">لا يوجد باحثين عن عمل حاليًا</p>
            </div>
        @else
            <!-- قسم البطاقات -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                @foreach($jobSeekers as $seeker)
                    <div class="bg-white rounded-2xl shadow-lg card-hover overflow-hidden border border-gray-100">
                        <div class="p-6 space-y-5">
                            <div class="flex items-center space-x-4 reverse-x">
                                <div class="flex-shrink-0">
                                    <img src="{{ $seeker->user->avatar ?? '/default-avatar.png' }}" 
                                         alt="{{ $seeker->user->name ?? 'باحث عن عمل' }}" 
                                         class="w-20 h-20 rounded-full object-cover ring-4 ring-blue-100">
                                </div>
                                <div>
                                    <h3 class="text-2xl font-bold text-blue-700">{{ $seeker->user->name ?? 'غير معروف' }}</h3>
                                    <p class="text-sm text-gray-500">{{ $seeker->specialization ?? 'التخصص غير محدد' }}</p>
                                </div>
                            </div>

                            <div class="space-y-3">
                                <div class="flex items-center space-x-2 reverse-x">
                                    <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                    <span class="text-gray-700">الخبرة: {{ $seeker->experience ? $seeker->experience . ' سنوات' : 'بدون خبرة' }}</span>
                                </div>

                                <div class="flex items-center space-x-2 reverse-x">
                                    <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                    </svg>
                                    <span class="text-gray-700">المهارات: {{ $seeker->skills ?? 'غير محدد' }}</span>
                                </div>

                                <div class="flex items-center space-x-2 reverse-x">
                                    <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    <span class="text-gray-700">{{ $seeker->location ?? 'الموقع غير محدد' }}</span>
                                </div>
                            </div>

                            <div class="bg-blue-50 p-4 rounded-lg border-r-4 border-blue-300">
                                <p class="text-sm text-gray-700">{{ Str::limit($seeker->description ?? 'لم يتم إضافة وصف', 100) }}</p>
                            </div>
                        </div>
                       
                        <div class="bg-gray-100 p-4 border-t">
                          
                           <a href="{{ route('jobSeekers.show', ['id' => $seeker->id]) }}"  class="block w-full text-center bg-gradient-to-r from-blue-500 to-teal-400 text-white py-3 rounded-lg hover:from-blue-600 hover:to-teal-500 transition-all duration-300 transform hover:-translate-y-1 shadow-md hover:shadow-lg">
                                عرض التفاصيل
                            </a>
                        </div>

                    </div>
                @endforeach
            </div>

            <!-- ترقيم الصفحات وأزرار التنقل -->
            <div class="flex justify-center items-center space-x-4 bg-white rounded-2xl shadow-lg p-4">
                <div class="flex items-center space-x-2">
                    <!-- زر السابق -->
                    <a href="{{ $jobSeekers->previousPageUrl() }}" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 {{ $jobSeekers->onFirstPage() ? 'pointer-events-none opacity-50' : '' }}">
                        السابق
                    </a>

                    <!-- أرقام الصفحات -->
                    @for ($i = 1; $i <= $jobSeekers->lastPage(); $i++)
                        <a href="{{ $jobSeekers->url($i) }}" class="px-4 py-2 {{ $jobSeekers->currentPage() == $i ? 'bg-blue-600 text-white' : 'bg-white text-blue-500 border' }} rounded-lg hover:bg-blue-100">
                            {{ $i }}
                        </a>
                    @endfor

                    <!-- زر التالي -->
                    <a href="{{ $jobSeekers->nextPageUrl() }}" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 {{ $jobSeekers->hasMorePages() ? '' : 'pointer-events-none opacity-50' }}">
                        التالي
                    </a>
                </div>
            </div>
        @endif
    </main>

    
    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3>منصة إنشر</h3>
                    <p>منصتك الأولى  للبحث عن الاعلانات والوظائف على الانترنت</p>
                </div>
                <div class="footer-column">
                    <h3>روابط سريعة</h3>
                    <ul class="footer-links">
                        <li><a href=" ">الرئيسية</a></li>
                        <li><a href=" ">بحث الوظائف</a></li>
                        <li><a href=" ">الشركات</a></li>
                        <li><a href=" ">من نحن</a></li>
                        <li><a href=" ">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>للباحثين عن عمل</h3>
                    <ul class="footer-links">
                        <li><a href=" ">إنشاء سيرة ذاتية</a></li>
                        <li><a href=" ">نصائح المقابلة</a></li>
                        <li><a href=" ">تطوير المهارات</a></li>
                        <li><a href=" ">دليل الرواتب</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>للشركات</h3>
                    <ul class="footer-links">
                        <li><a href=" ">نشر وظيفة</a></li>
                        <li><a href=" ">البحث عن مرشحين</a></li>
                        <li><a href=" ">حلول التوظيف</a></li>
                        <li><a href=" ">الباقات والأسعار</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>جميع الحقوق محفوظة &copy; {{ date('Y') }} منصة إنشر</p>
            </div>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة طبقة التحريك للبطاقات
            setTimeout(() => {
                document.querySelectorAll('.job-card').forEach(card => {
                    card.classList.add('animated');
                });
            }, 100);

            // وظيفة الحفظ
            document.addEventListener('click', function(e) {
                if (e.target.closest('.btn-save')) {
                    const saveBtn = e.target.closest('.btn-save');
                    const icon = saveBtn.querySelector('i');
                    
                    if (icon.classList.contains('far')) {
                        icon.classList.remove('far');
                        icon.classList.add('fas');
                        saveBtn.style.color = '#2563eb';
                    } else {
                        icon.classList.remove('fas');
                        icon.classList.add('far');
                        saveBtn.style.color = '';
                    }
                }
            });

            // وظيفة الترتيب
            const sortSelect = document.getElementById('sortOptions');
            if (sortSelect) {
                sortSelect.addEventListener('change', function() {
                    // هنا يمكن إضافة طلب AJAX لجلب البيانات المرتبة
                    // أو في حالة لارافل يمكن إعادة توجيه الصفحة مع معلمات الترتيب
                    const sortValue = this.value;
                    window.location.href = `{{ route('jobs.index') }}?sort=${sortValue}`;
                });
            }
        });
    </script>
</body>
</html>







