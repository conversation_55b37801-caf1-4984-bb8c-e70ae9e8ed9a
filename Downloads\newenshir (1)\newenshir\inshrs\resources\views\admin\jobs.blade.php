@extends('layouts.admin')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">إدارة الوظائف</h5>
                    </div>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>عنوان الوظيفة</th>
                                    <th>الشركة</th>
                                    <th>المستخدم</th>
                                    <th>الحالة</th>
                                    <th>تاريخ النشر</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($jobs as $job)
                                <tr>
                                    <td>{{ $job->id }}</td>
                                    <td>{{ $job->job_title }}</td>
                                    <td>{{ $job->company_name }}</td>
                                    <td>{{ $job->user->name ?? 'غير معروف' }}</td>
                                    <td>
                                        @if($job->is_featured)
                                            <span class="badge bg-success">مميزة</span>
                                        @else
                                            <span class="badge bg-secondary">عادية</span>
                                        @endif
                                    </td>
                                    <td>{{ $job->created_at->format('Y-m-d') }}</td>
                                    <td>
                                        <a href="{{ route('jobs.show', $job->id) }}" class="btn btn-sm btn-outline-primary" title="عرض الوظيفة">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <form action="{{ route('admin.jobs.delete', $job->id) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('هل أنت متأكد من حذف هذه الوظيفة؟')" title="حذف الوظيفة">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center">لا توجد وظائف</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="d-flex justify-content-center mt-4">
                        {{ $jobs->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
