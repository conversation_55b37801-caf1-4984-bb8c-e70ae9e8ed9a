<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('jobs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
       
            $table->string('job_title');
            $table->string('company_name');
            $table->string('location');
            $table->decimal('salary', 10, 2);
            $table->string('experience_required');
            $table->text('job_description');
            $table->string('whatsapp', 50);
            $table->string('email', 100);
            $table->string('phone', 50); // تغيير الاسم من phone_number إلى phone
            $table->timestamps();
        });
    }
    

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('jobs');
    }
};



 