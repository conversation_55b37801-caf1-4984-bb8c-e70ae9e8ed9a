<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContactInfo extends Model
{
    use HasFactory;

    protected $fillable = [
        'basic_info_id',
        'contact_type',
        'contact_value',
        'created_at',
        'updated_at'
    ];

    // العلاقة مع BasicInfo
    public function basicInfo()
    {
        return $this->belongsTo(BasicInfo::class);
    }
}
