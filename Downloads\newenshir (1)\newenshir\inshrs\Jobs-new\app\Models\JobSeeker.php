<?php

// app/Models/Data.php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JobSeeker extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 
        'job_title', 
        'description', 
        'specialization', 
        'experience', 
        'skills', 
        'location', 
        'whatsapp', 
        'phone'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
