<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * عرض جميع الإشعارات للمستخدم الحالي
     */
    public function index()
    {
        $user = Auth::user();
        $notifications = Notification::forUser($user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('notifications.index', compact('notifications'));
    }

    /**
     * تحديد إشعار كمقروء
     */
    public function markAsRead($id)
    {
        $notification = Notification::findOrFail($id);

        // التحقق من أن الإشعار ينتمي للمستخدم الحالي
        if ($notification->user_id !== Auth::id()) {
            return redirect()->route('notifications.index')
                ->with('error', 'ليس لديك صلاحية للوصول إلى هذا الإشعار.');
        }

        $notification->markAsRead();

        return redirect()->back()->with('success', 'تم تحديد الإشعار كمقروء.');
    }

    /**
     * تحديد جميع الإشعارات كمقروءة
     */
    public function markAllAsRead()
    {
        $user = Auth::user();

        Notification::forUser($user->id)
            ->unread()
            ->update([
                'is_read' => true,
                'read_at' => now()
            ]);

        return redirect()->back()->with('success', 'تم تحديد جميع الإشعارات كمقروءة.');
    }

    /**
     * حذف إشعار
     */
    public function destroy($id)
    {
        $notification = Notification::findOrFail($id);

        // التحقق من أن الإشعار ينتمي للمستخدم الحالي
        if ($notification->user_id !== Auth::id()) {
            return redirect()->route('notifications.index')
                ->with('error', 'ليس لديك صلاحية للوصول إلى هذا الإشعار.');
        }

        $notification->delete();

        return redirect()->route('notifications.index')
            ->with('success', 'تم حذف الإشعار بنجاح.');
    }
}
