<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض الوظيفة</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Cairo', sans-serif;
        }
        .card {
            border-radius: 1rem;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            border: 2px solid #d1d5db;
        }
        .btn {
            transition: all 0.3s ease;
            font-weight: 600;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .user-container {
            display: flex;
            align-items: center;
            justify-content: center;
            background: #e2e8f0;
            border-radius: 1rem;
            padding: 10px;
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
        }
        .user-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #3b82f6;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <div class="bg-white p-6 md:p-8 card max-w-3xl w-full text-right">
        <!-- Header with User Icon -->
        <div class="flex flex-col items-center mb-6">
            <div class="user-container mb-4">
                <div class="user-icon">
                    <i class="fas fa-user"></i>
                </div>
                <h3 class="text-2xl font-bold text-blue-700 ml-4">{{ $job->user->name ?? 'غير معروف' }}</h3>
            </div>
            <h1 class="text-3xl md:text-4xl font-bold text-blue-600">تفاصيل الوظيفة</h1>
        </div>
        
        <!-- Job Details -->
        <div class="grid md:grid-cols-2 gap-6 mb-6">
            <div class="space-y-4">
                <p class="text-lg text-gray-700"><strong class="text-gray-900">عنوان الوظيفة:</strong> {{ $job->job_title }}</p>
                <p class="text-lg text-gray-700"><strong class="text-gray-900">اسم الجهة:</strong> {{ $job->company_name }}</p>
                <p class="text-lg text-gray-700"><strong class="text-gray-900">الموقع:</strong> {{ $job->location }}</p>
                <p class="text-lg text-gray-700"><strong class="text-gray-900">الراتب:</strong> {{ number_format($job->salary, 2) }} <span class="font-bold">ر.س</span></p>
            </div>
            <div class="space-y-4">
                <p class="text-lg text-gray-700"><strong class="text-gray-900">التاريخ:</strong> {{ $job->created_at ? $job->created_at->format('Y-m-d') : 'لم يتم تحديد التاريخ' }}</p>
                <p class="text-lg text-gray-700"><strong class="text-gray-900">الخبرة المطلوبة:</strong> {{ $job->experience_required }} سنوات</p>
            </div>
        </div>

        <!-- Job Description -->
        <div class="mb-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-2">وصف الوظيفة</h2>
            <p class="text-lg text-gray-700">{{ $job->job_description }}</p>
        </div>

        <!-- Contact Details -->
        <div class="mb-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">بيانات الاتصال</h2>
            <div class="space-y-3">
                <p class="flex items-center text-lg text-gray-700">
                    <i class="fab fa-whatsapp ml-3 text-xl text-green-500"></i>
                    <strong class="ml-1">واتساب:</strong> <a href="https://wa.me/{{ $job->whatsapp }}" target="_blank" class="text-blue-600 hover:underline">{{ $job->whatsapp }}</a>
                </p>
                <p class="flex items-center text-lg text-gray-700">
                    <i class="fas fa-envelope ml-3 text-xl text-blue-500"></i>
                    <strong class="ml-1">الإيميل:</strong> <a href="mailto:{{ $job->email }}" class="text-blue-600 hover:underline">{{ $job->email }}</a>
                </p>
                <p class="flex items-center text-lg text-gray-700">
                    <i class="fas fa-phone ml-3 text-xl text-gray-500"></i>
                    <strong class="ml-1">اتصال:</strong> {{ $job->phone_number }}
                </p>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col md:flex-row justify-center gap-4">
            <!-- <button class="btn bg-green-500 text-white py-2 px-6 rounded-lg hover:bg-green-600" onclick="window.location='{{ url('/Apply-job-company') }}'">تقدم الآن</button>
            <button class="btn bg-blue-500 text-white py-2 px-6 rounded-lg hover:bg-blue-600" onclick="window.location='{{ url('/CompanyProfileShow') }}'">عرض ملف الشركة</button> -->
            <button class="btn bg-yellow-500 text-white py-2 px-6 rounded-lg hover:bg-yellow-600" onclick="reportJob()">تبليغ عن الوظيفة</button>
            <button class="btn bg-red-500 text-white py-2 px-6 rounded-lg hover:bg-red-600" onclick="goBack()">رجوع</button>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        function reportJob() {
            alert('شكرًا لك على تقديم التبليغ. سنتخذ الإجراءات اللازمة.');
        }
        function goBack() {
            window.history.back();
        }
    </script>
</body>
</html>
