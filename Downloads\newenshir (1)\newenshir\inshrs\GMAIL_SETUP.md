# دليل إعداد Gmail للمشروع

## المشكلة الحالية
الكود يصل للإيميلات الوهمية فقط، يجب إصلاحه ليصل للإيميلات الحقيقية مثل Gmail.

## الحل المطبق

### 1. تحديث إعدادات البريد الإلكتروني

تم تحديث ملف `.env` ليستخدم Gmail كمزود افتراضي:

```env
# إعدادات البريد الإلكتروني - Gmail
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
```

### 2. إعداد Gmail App Password

لاستخدام Gmail مع Laravel، تحتاج إلى:

#### الخطوة 1: تفعيل التحقق بخطوتين
1. اذهب إلى [Google Account Settings](https://myaccount.google.com/)
2. اختر "Security" من القائمة الجانبية
3. فعل "2-Step Verification"

#### الخطوة 2: إنشاء App Password
1. في نفس صفحة الأمان، اختر "App passwords"
2. اختر "Mail" كنوع التطبيق
3. اختر "Other" واكتب "Laravel App"
4. انسخ كلمة المرور المُنشأة (16 رقم)

#### الخطوة 3: تحديث ملف .env
```env
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-16-digit-app-password
MAIL_FROM_ADDRESS="<EMAIL>"
```

### 3. مزودي البريد المدعومين

تم إضافة دعم لعدة مزودي بريد إلكتروني:

#### Gmail
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_ENCRYPTION=tls
```

#### Outlook/Hotmail
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp-mail.outlook.com
MAIL_PORT=587
MAIL_ENCRYPTION=tls
```

#### Yahoo
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.mail.yahoo.com
MAIL_PORT=587
MAIL_ENCRYPTION=tls
```

### 4. اختبار الإعدادات

#### اختبار سريع عبر Tinker:
```bash
php artisan tinker
```

```php
use Illuminate\Support\Facades\Mail;

Mail::raw('اختبار إرسال البريد الإلكتروني', function ($message) {
    $message->to('<EMAIL>')
            ->subject('اختبار من Laravel');
});
```

#### اختبار متقدم:
```php
// إنشاء Mailable للاختبار
php artisan make:mail TestMail

// في TestMail.php
public function build()
{
    return $this->subject('اختبار البريد الإلكتروني')
                ->view('emails.test')
                ->with(['message' => 'تم إرسال البريد بنجاح!']);
}

// إرسال الاختبار
Mail::to('<EMAIL>')->send(new TestMail());
```

### 5. معالجة الأخطاء الشائعة

#### خطأ: "Authentication failed"
- تأكد من تفعيل 2-Step Verification
- استخدم App Password وليس كلمة المرور العادية
- تأكد من صحة البريد الإلكتروني

#### خطأ: "Connection timeout"
- تأكد من إعدادات الشبكة
- جرب تغيير المنفذ إلى 465 مع SSL:
```env
MAIL_PORT=465
MAIL_ENCRYPTION=ssl
```

#### خطأ: "Less secure app access"
- Gmail لا يدعم هذا الخيار بعد الآن
- يجب استخدام App Password

### 6. إعدادات الأمان

#### تحسين الأمان:
```php
// في config/mail.php
'verify_peer' => env('MAIL_VERIFY_PEER', true),
'verify_peer_name' => env('MAIL_VERIFY_PEER_NAME', true),
```

#### للبيئة المحلية فقط:
```env
MAIL_VERIFY_PEER=false
```

### 7. مراقبة الإرسال

#### تسجيل الأخطاء:
```php
// في AppServiceProvider
use Illuminate\Mail\Events\MessageSending;
use Illuminate\Mail\Events\MessageSent;

Event::listen(MessageSending::class, function ($event) {
    Log::info('Sending email to: ' . implode(', ', array_keys($event->message->getTo())));
});

Event::listen(MessageSent::class, function ($event) {
    Log::info('Email sent successfully');
});
```

### 8. البدائل المتاحة

#### استخدام Mailgun:
```env
MAIL_MAILER=mailgun
MAILGUN_DOMAIN=your-domain.com
MAILGUN_SECRET=your-secret-key
```

#### استخدام SendGrid:
```bash
composer require sendgrid/sendgrid
```

### 9. نصائح مهمة

1. **لا تشارك App Password**: احتفظ بها آمنة
2. **استخدم متغيرات البيئة**: لا تكتب كلمات المرور في الكود
3. **اختبر في البيئة المحلية أولاً**: قبل النشر
4. **راقب حدود الإرسال**: Gmail له حدود يومية
5. **استخدم Queue للإرسال الكثيف**: لتجنب timeout

### 10. أوامر مفيدة

```bash
# مسح cache الإعدادات
php artisan config:clear

# إعادة تحميل الإعدادات
php artisan config:cache

# اختبار الاتصال
php artisan tinker
>>> config('mail.mailers.smtp')

# عرض الإعدادات الحالية
php artisan env
```

## الخلاصة

تم إصلاح إعدادات البريد الإلكتروني لتعمل مع Gmail والإيميلات الحقيقية. يجب على المطور:

1. إعداد App Password في Gmail
2. تحديث ملف .env بالبيانات الصحيحة
3. اختبار الإرسال قبل النشر
4. مراقبة الأخطاء والحدود

الآن يمكن للموقع إرسال إيميلات حقيقية إلى عناوين Gmail وغيرها من مزودي البريد الإلكتروني.
