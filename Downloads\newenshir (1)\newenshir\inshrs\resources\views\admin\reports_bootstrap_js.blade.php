@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // بيانات البلاغات
        const reportsData = [
            {
                id: 1001,
                title: "وظيفة مطور ويب",
                department: "قسم تقنية المعلومات",
                type: "محتوى غير لائق",
                typeClass: "danger",
                reporter: { name: "أحمد محمد", email: "<EMAIL>" },
                date: "10 فبراير 2025",
                status: "pending",
                statusText: "قيد المراجعة",
                statusClass: "warning",
                description: "هذا الإعلان يحتوي على محتوى غير لائق ومخالف لشروط الاستخدام."
            },
            {
                id: 1002,
                title: "إعلان منتج إلكتروني",
                department: "قسم الإعلانات التجارية",
                type: "إعلان مضلل",
                typeClass: "warning",
                reporter: { name: "سارة علي", email: "<EMAIL>" },
                date: "8 فبراير 2025",
                status: "pending",
                statusText: "قيد المراجعة",
                statusClass: "warning",
                description: "الإعلان يحتوي على معلومات مضللة حول المنتج."
            },
            {
                id: 1003,
                title: "وظيفة محاسب",
                department: "قسم المالية",
                type: "طلب معلومات شخصية",
                typeClass: "info",
                reporter: { name: "محمد خالد", email: "<EMAIL>" },
                date: "5 فبراير 2025",
                status: "processed",
                statusText: "تمت المعالجة",
                statusClass: "success",
                description: "يطلب الإعلان معلومات شخصية حساسة غير ضرورية."
            },
            {
                id: 1004,
                title: "إعلان عقار للبيع",
                department: "قسم العقارات",
                type: "معلومات غير دقيقة",
                typeClass: "primary",
                reporter: { name: "فاطمة أحمد", email: "<EMAIL>" },
                date: "3 فبراير 2025",
                status: "rejected",
                statusText: "مرفوض",
                statusClass: "danger",
                description: "الإعلان يحتوي على معلومات غير دقيقة عن العقار."
            }
        ];

        // إعدادات الترقيم
        const itemsPerPage = 10;
        let currentPage = 1;
        let filteredData = reportsData;

        // عناصر الواجهة
        const reportsTableBody = document.getElementById('reportsTableBody');
        const paginationContainer = document.getElementById('paginationContainer');
        const prevButton = document.getElementById('prevButton');
        const nextButton = document.getElementById('nextButton');
        const paginationInfo = document.getElementById('paginationInfo');
        const statusFilter = document.getElementById('statusFilter');
        const loadingIndicator = document.getElementById('loadingIndicator');
        const reportsTable = document.getElementById('reportsTable');
        const noResultsMessage = document.getElementById('noResultsMessage');
        const filterBtn = document.getElementById('filterBtn');
        const exportBtn = document.getElementById('exportBtn');
        const reportModal = new bootstrap.Modal(document.getElementById('reportModal'));
        const successToast = new bootstrap.Toast(document.getElementById('successToast'));

        // تحديث إحصائيات البطاقات
        function updateStats() {
            document.getElementById('totalReports').textContent = reportsData.length;
            document.getElementById('pendingReports').textContent = reportsData.filter(r => r.status === 'pending').length;
            document.getElementById('processedReports').textContent = reportsData.filter(r => r.status === 'processed').length;
            document.getElementById('rejectedReports').textContent = reportsData.filter(r => r.status === 'rejected').length;
        }

        // دالة لعرض البيانات
        function displayData(page, data) {
            reportsTableBody.innerHTML = '';
            const start = (page - 1) * itemsPerPage;
            const end = start + itemsPerPage;
            const paginatedData = data.slice(start, end);

            if (paginatedData.length === 0) {
                reportsTable.classList.add('d-none');
                noResultsMessage.classList.remove('d-none');
            } else {
                reportsTable.classList.remove('d-none');
                noResultsMessage.classList.add('d-none');
                
                paginatedData.forEach(report => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${report.id}</td>
                        <td>${report.title}</td>
                        <td><span class="badge bg-${report.typeClass}">${report.type}</span></td>
                        <td class="d-none d-md-table-cell">${report.reporter.name}</td>
                        <td class="d-none d-md-table-cell">${report.date}</td>
                        <td><span class="badge bg-${report.statusClass}">${report.statusText}</span></td>
                        <td>
                            <button class="btn btn-sm btn-primary view-report-btn" data-id="${report.id}">
                                <i class="fas fa-eye"></i> عرض
                            </button>
                        </td>
                    `;
                    reportsTableBody.appendChild(row);
                });

                // إضافة مستمعي الأحداث لأزرار العرض
                document.querySelectorAll('.view-report-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const reportId = this.getAttribute('data-id');
                        showReportDetails(reportId);
                    });
                });
            }
        }

        // عرض تفاصيل البلاغ
        function showReportDetails(reportId) {
            const report = reportsData.find(r => r.id == reportId);
            if (report) {
                document.getElementById('modalReportId').innerHTML = `رقم البلاغ: <span class="fw-bold">#${report.id}</span>`;
                document.getElementById('modalReportType').innerHTML = `نوع البلاغ: <span class="text-${report.typeClass} fw-bold">${report.type}</span>`;
                document.getElementById('modalReportDate').innerHTML = `تاريخ التقديم: <span>${report.date}</span>`;
                document.getElementById('modalReportStatus').innerHTML = `الحالة: <span class="badge bg-${report.statusClass}">${report.statusText}</span>`;
                document.getElementById('modalReportDescription').textContent = report.description;
                reportModal.show();
            }
        }

        // دالة لعرض الترقيم
        function displayPagination(totalItems) {
            paginationContainer.innerHTML = '';
            const pageCount = Math.ceil(totalItems / itemsPerPage);
            
            // تحديث أزرار السابق والتالي
            prevButton.disabled = currentPage === 1;
            nextButton.disabled = currentPage === pageCount || pageCount === 0;
            
            // تحديث معلومات الترقيم
            const start = totalItems === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1;
            const end = Math.min(currentPage * itemsPerPage, totalItems);
            paginationInfo.innerHTML = `عرض <span class="fw-bold">${start}</span> إلى <span class="fw-bold">${end}</span> من أصل <span class="fw-bold">${totalItems}</span> نتيجة`;
        }

        // تصفية البيانات بناءً على الحالة
        function filterData(status) {
            loadingIndicator.classList.remove('d-none');
            reportsTable.classList.add('d-none');
            
            setTimeout(() => {
                filteredData = status === 'all' ? reportsData : reportsData.filter(report => report.status === status);
                currentPage = 1;
                displayData(currentPage, filteredData);
                displayPagination(filteredData.length);
                loadingIndicator.classList.add('d-none');
            }, 500); // محاكاة تأخير الشبكة
        }

        // إظهار إشعار النجاح
        function showSuccessToast(message) {
            document.getElementById('toastMessage').textContent = message;
            successToast.show();
        }

        // تحميل البيانات الأولية
        updateStats();
        filterData('all');

        // مستمعي الأحداث
        filterBtn.addEventListener('click', () => {
            filterData(statusFilter.value);
        });

        prevButton.addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                displayData(currentPage, filteredData);
                displayPagination(filteredData.length);
            }
        });

        nextButton.addEventListener('click', () => {
            const pageCount = Math.ceil(filteredData.length / itemsPerPage);
            if (currentPage < pageCount) {
                currentPage++;
                displayData(currentPage, filteredData);
                displayPagination(filteredData.length);
            }
        });

        exportBtn.addEventListener('click', () => {
            showSuccessToast('تم تصدير البيانات بنجاح');
        });
    });
</script>
@endsection
