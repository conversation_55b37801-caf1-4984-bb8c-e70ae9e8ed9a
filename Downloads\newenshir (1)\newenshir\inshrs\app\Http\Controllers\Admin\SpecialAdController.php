<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Image;
use App\Models\SpecialAd;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class SpecialAdController extends Controller
{
    /**
     * إنشاء مثيل جديد من المتحكم
     */
    public function __construct()
    {
        // تم إزالة الوسيط permission:manage-special-ads للسماح بالوصول
    }
    /**
     * عرض قائمة الإعلانات الخاصة
     */
    public function index()
    {
        $specialAds = SpecialAd::with('image')->latest()->paginate(10);
        return view('admin.special_ads.index', compact('specialAds'));
    }

    /**
     * عرض نموذج إنشاء إعلان خاص جديد
     */
    public function create()
    {
        return view('admin.special_ads.create');
    }

    /**
     * تخزين إعلان خاص جديد
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'url' => 'nullable|url',
            'position' => 'required|in:top,middle,bottom,sidebar',
            'is_active' => 'boolean',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'advertiser_name' => 'nullable|string|max:255',
            'advertiser_phone' => 'nullable|string|max:20',
            'advertiser_email' => 'nullable|email|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // حفظ الصورة في قاعدة البيانات
            $image = null;
            if ($request->hasFile('image')) {
                $file = $request->file('image');
                $imageData = base64_encode(file_get_contents($file->getRealPath()));
                $image = new Image();
                $image->name = $file->getClientOriginalName();
                $image->type = $file->getMimeType();
                $image->data = $imageData;
                $image->save();
            }

            // إنشاء الإعلان الخاص
            $specialAd = new SpecialAd();
            $specialAd->title = $request->title;
            $specialAd->image_id = $image ? $image->id : null;
            $specialAd->url = $request->url;
            $specialAd->position = $request->position;
            $specialAd->is_active = $request->boolean('is_active');
            $specialAd->start_date = $request->start_date;
            $specialAd->end_date = $request->end_date;
            $specialAd->advertiser_name = $request->advertiser_name;
            $specialAd->advertiser_phone = $request->advertiser_phone;
            $specialAd->advertiser_email = $request->advertiser_email;
            $specialAd->save();

            return redirect()->route('admin.special-ads.index')
                ->with('success', 'تم إنشاء الإعلان الخاص بنجاح');
        } catch (\Exception $e) {
            Log::error('Error creating special ad: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء إنشاء الإعلان الخاص: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * عرض إعلان خاص محدد
     */
    public function show(SpecialAd $specialAd)
    {
        return view('admin.special_ads.show', compact('specialAd'));
    }

    /**
     * عرض نموذج تعديل إعلان خاص
     */
    public function edit(SpecialAd $specialAd)
    {
        return view('admin.special_ads.edit', compact('specialAd'));
    }

    /**
     * تحديث إعلان خاص محدد
     */
    public function update(Request $request, SpecialAd $specialAd)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'url' => 'nullable|url',
            'position' => 'required|in:top,middle,bottom,sidebar',
            'is_active' => 'boolean',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'advertiser_name' => 'nullable|string|max:255',
            'advertiser_phone' => 'nullable|string|max:20',
            'advertiser_email' => 'nullable|email|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // تحديث الصورة إذا تم تقديم صورة جديدة
            if ($request->hasFile('image')) {
                $file = $request->file('image');
                $imageData = base64_encode(file_get_contents($file->getRealPath()));

                // إنشاء صورة جديدة
                $image = new Image();
                $image->name = $file->getClientOriginalName();
                $image->type = $file->getMimeType();
                $image->data = $imageData;
                $image->save();

                $specialAd->image_id = $image->id;
            }

            // تحديث بيانات الإعلان الخاص
            $specialAd->title = $request->title;
            $specialAd->url = $request->url;
            $specialAd->position = $request->position;
            $specialAd->is_active = $request->boolean('is_active');
            $specialAd->start_date = $request->start_date;
            $specialAd->end_date = $request->end_date;
            $specialAd->advertiser_name = $request->advertiser_name;
            $specialAd->advertiser_phone = $request->advertiser_phone;
            $specialAd->advertiser_email = $request->advertiser_email;
            $specialAd->save();

            return redirect()->route('admin.special-ads.index')
                ->with('success', 'تم تحديث الإعلان الخاص بنجاح');
        } catch (\Exception $e) {
            Log::error('Error updating special ad: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء تحديث الإعلان الخاص: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * حذف إعلان خاص محدد
     */
    public function destroy(SpecialAd $specialAd)
    {
        try {
            $specialAd->delete();
            return redirect()->route('admin.special-ads.index')
                ->with('success', 'تم حذف الإعلان الخاص بنجاح');
        } catch (\Exception $e) {
            Log::error('Error deleting special ad: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء حذف الإعلان الخاص: ' . $e->getMessage());
        }
    }
}
