@extends('admin.reports.reports')

@section('table_headers')
<th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">رقم البلاغ</th>
<th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عنوان الإعلان</th>
<th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">نوع البلاغ</th>
<th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">مقدم البلاغ</th>
<th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
<th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
<th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
@endsection

@section('row_content')
row.innerHTML = `
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="text-sm text-gray-900">#${report.id}</div>
    </td>
    <td class="px-6 py-4">
        <div class="text-sm text-gray-900">${report.ad_title}</div>
    </td>
    <td class="px-6 py-4">
        <div class="text-sm text-gray-900">${report.type}</div>
    </td>
    <td class="px-6 py-4">
        <div class="text-sm text-gray-900">${report.reporter_name}</div>
    </td>
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="text-sm text-gray-900">${report.date}</div>
    </td>
    <td class="px-6 py-4 whitespace-nowrap">
        <select onchange="updateStatus(${report.id}, this.value)" class="form-select rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
            <option value="pending" ${report.status === 'pending' ? 'selected' : ''}>قيد الانتظار</option>
            <option value="processed" ${report.status === 'processed' ? 'selected' : ''}>تمت المعالجة</option>
            <option value="rejected" ${report.status === 'rejected' ? 'selected' : ''}>مرفوض</option>
        </select>
    </td>
    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <button class="text-blue-600 hover:text-blue-900 view-report-btn" data-id="${report.id}">
            <i class="fas fa-eye"></i>
        </button>
    </td>
`;
@endsection

@section('custom_scripts')
<script>
async function updateStatus(id, status) {
    try {
        const response = await fetch(`/admin/reports/ads/${id}/status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({ status })
        });

        if (response.ok) {
            // تحديث حالة البلاغ في المصفوفة
            const report = reportsData.find(r => r.id === id);
            if (report) {
                report.status = status;
                // تحديث العرض
                displayData(currentPage, filteredData);
                updateStats();
            }
        } else {
            alert('حدث خطأ أثناء تحديث حالة البلاغ');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('حدث خطأ أثناء تحديث حالة البلاغ');
    }
}
</script>
@endsection
