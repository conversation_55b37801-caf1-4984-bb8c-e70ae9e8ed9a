<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('site_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->text('value')->nullable();
            $table->string('type')->default('text'); // text, image, textarea, number, boolean
            $table->string('group')->default('general'); // general, appearance, contact, etc.
            $table->string('label');
            $table->text('description')->nullable();
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // إدراج البيانات الافتراضية
        DB::table('site_settings')->insert([
            [
                'key' => 'site_name',
                'value' => 'منصة إنشر',
                'type' => 'text',
                'group' => 'general',
                'label' => 'اسم الموقع',
                'description' => 'اسم الموقع الذي يظهر في العنوان والهيدر',
                'sort_order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'site_logo',
                'value' => 'images/enshir.ico',
                'type' => 'image',
                'group' => 'appearance',
                'label' => 'لوجو الموقع',
                'description' => 'لوجو الموقع الذي يظهر في الهيدر',
                'sort_order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'site_favicon',
                'value' => 'images/enshir.ico',
                'type' => 'image',
                'group' => 'appearance',
                'label' => 'أيقونة الموقع (Favicon)',
                'description' => 'الأيقونة التي تظهر في تبويب المتصفح',
                'sort_order' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'site_description',
                'value' => 'منصة إعلانات متنوعة تجمع بين البائعين والمشترين في مكان واحد',
                'type' => 'textarea',
                'group' => 'general',
                'label' => 'وصف الموقع',
                'description' => 'وصف مختصر للموقع يظهر في محركات البحث',
                'sort_order' => 4,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'site_keywords',
                'value' => 'إعلانات, تسوق, بيع, شراء, وظائف, عقارات, سيارات, خدمات',
                'type' => 'textarea',
                'group' => 'general',
                'label' => 'الكلمات المفتاحية',
                'description' => 'الكلمات المفتاحية للموقع لمحركات البحث',
                'sort_order' => 5,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'email',
                'group' => 'contact',
                'label' => 'البريد الإلكتروني للتواصل',
                'description' => 'البريد الإلكتروني الرئيسي للموقع',
                'sort_order' => 6,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'contact_phone',
                'value' => '+966500000000',
                'type' => 'text',
                'group' => 'contact',
                'label' => 'رقم الهاتف',
                'description' => 'رقم الهاتف للتواصل',
                'sort_order' => 7,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'primary_color',
                'value' => '#3AB0FF',
                'type' => 'color',
                'group' => 'appearance',
                'label' => 'اللون الأساسي',
                'description' => 'اللون الأساسي للموقع',
                'sort_order' => 8,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'secondary_color',
                'value' => '#E67E22',
                'type' => 'color',
                'group' => 'appearance',
                'label' => 'اللون الثانوي',
                'description' => 'اللون الثانوي للموقع',
                'sort_order' => 9,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'maintenance_mode',
                'value' => '0',
                'type' => 'boolean',
                'group' => 'general',
                'label' => 'وضع الصيانة',
                'description' => 'تفعيل وضع الصيانة للموقع',
                'sort_order' => 10,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('site_settings');
    }
};
