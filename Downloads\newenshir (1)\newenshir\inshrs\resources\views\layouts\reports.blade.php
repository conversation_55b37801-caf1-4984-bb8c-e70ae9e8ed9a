<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'التقارير') - {{ config('app.name', 'موقع الإعلانات') }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
            direction: rtl;
            text-align: right;
        }
        
        .reports-layout {
            min-height: 100vh;
            width: 100%;
        }
        
        .reports-navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .navbar-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .navbar-brand {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: white;
            text-decoration: none;
            font-size: 1.5rem;
            font-weight: 700;
        }
        
        .navbar-brand:hover {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
        }
        
        .navbar-brand i {
            font-size: 1.8rem;
        }
        
        .navbar-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
        }
        
        .user-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
        }
        
        .reports-content {
            width: 100%;
            min-height: calc(100vh - 80px);
        }
        
        .reports-footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 1.5rem 0;
            margin-top: 2rem;
        }
        
        .footer-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        
        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }
        
        .footer-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .footer-links a:hover {
            color: white;
        }
        
        .footer-copyright {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.9rem;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .navbar-content {
                flex-direction: column;
                text-align: center;
            }
            
            .navbar-brand {
                font-size: 1.3rem;
            }
            
            .navbar-actions {
                width: 100%;
                justify-content: center;
                flex-wrap: wrap;
            }
            
            .user-info {
                order: -1;
                margin-bottom: 0.5rem;
            }
            
            .footer-links {
                flex-direction: column;
                gap: 1rem;
            }
        }
        
        @media (max-width: 480px) {
            .navbar-content {
                padding: 0 0.5rem;
            }
            
            .back-btn {
                padding: 0.5rem 1rem;
                font-size: 0.9rem;
            }
            
            .navbar-brand {
                font-size: 1.2rem;
            }
            
            .navbar-brand i {
                font-size: 1.5rem;
            }
        }
        
        /* Print Styles */
        @media print {
            .reports-navbar,
            .reports-footer {
                display: none !important;
            }
            
            .reports-content {
                min-height: auto;
            }
            
            body {
                background: white !important;
            }
        }
        
        /* Loading Animation */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            color: white;
        }
        
        .loading-content {
            text-align: center;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            font-size: 16px;
            font-weight: 600;
        }
    </style>
    
    @stack('styles')
</head>
<body>
    <div class="reports-layout">
        <!-- Navbar -->
        <nav class="reports-navbar">
            <div class="navbar-content">
                <a href="{{ route('dashboard') }}" class="navbar-brand">
                    <i class="fas fa-chart-bar"></i>
                    @yield('title', 'التقارير الشاملة')
                </a>
                
                <div class="navbar-actions">
                    <a href="{{ route('dashboard') }}" class="back-btn">
                        <i class="fas fa-arrow-right"></i>
                        العودة للوحة التحكم
                    </a>
                    
                    @auth
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="user-details">
                            <div class="user-name">{{ auth()->user()->name }}</div>
                            <div class="user-role">{{ auth()->user()->role ?? 'مستخدم' }}</div>
                        </div>
                    </div>
                    @endauth
                </div>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="reports-content">
            @yield('content')
        </main>
        
        <!-- Footer -->
        <footer class="reports-footer">
            <div class="footer-content">
                <div class="footer-links">
                    <a href="{{ route('dashboard') }}">لوحة التحكم</a>
                    <a href="{{ route('ads.index') }}">الإعلانات</a>
                    <a href="{{ route('jobs.index') }}">الوظائف</a>
                    <a href="{{ route('job_seekers.index') }}">الباحثين عن عمل</a>
                </div>
                <div class="footer-copyright">
                    &copy; {{ date('Y') }} {{ config('app.name', 'موقع الإعلانات') }}. جميع الحقوق محفوظة.
                </div>
            </div>
        </footer>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    @stack('scripts')
    
    <script>
        // إضافة تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير التحميل
            const loadingOverlay = document.getElementById('loading-overlay');
            if (loadingOverlay) {
                setTimeout(() => {
                    loadingOverlay.style.opacity = '0';
                    setTimeout(() => {
                        loadingOverlay.remove();
                    }, 300);
                }, 500);
            }
            
            // تحسين تجربة الطباعة
            window.addEventListener('beforeprint', function() {
                document.body.classList.add('printing');
            });
            
            window.addEventListener('afterprint', function() {
                document.body.classList.remove('printing');
            });
        });
        
        // دالة إظهار التحميل
        function showLoading(message = 'جاري التحميل...') {
            const loadingDiv = document.createElement('div');
            loadingDiv.id = 'loading-overlay';
            loadingDiv.className = 'loading-overlay';
            loadingDiv.innerHTML = `
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">${message}</div>
                </div>
            `;
            document.body.appendChild(loadingDiv);
        }
        
        // دالة إخفاء التحميل
        function hideLoading() {
            const loadingDiv = document.getElementById('loading-overlay');
            if (loadingDiv) {
                loadingDiv.style.opacity = '0';
                setTimeout(() => {
                    loadingDiv.remove();
                }, 300);
            }
        }
    </script>
</body>
</html>
