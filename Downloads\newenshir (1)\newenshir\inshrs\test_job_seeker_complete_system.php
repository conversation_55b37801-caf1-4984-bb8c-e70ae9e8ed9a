<?php

// ملف اختبار شامل لنظام الباحثين عن عمل المحدث
// تشغيل: php test_job_seeker_complete_system.php

require_once 'vendor/autoload.php';

// تحميل إعدادات Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\Route;
use App\Models\JobSeeker;
use App\Models\User;

echo "🧪 اختبار شامل لنظام الباحثين عن عمل المحدث\n";
echo "===============================================\n\n";

try {
    // 1. فحص Routes المطلوبة
    echo "1️⃣ فحص Routes:\n";
    
    $requiredRoutes = [
        'job_seekers.create' => 'إنشاء طلب بحث عن عمل',
        'job_seekers.store' => 'حفظ طلب بحث عن عمل',
        'job_seekers.index' => 'قائمة الباحثين عن عمل',
        'job-seekers.show' => 'عرض تفاصيل باحث عن عمل',
        'jobSeeker.edit' => 'تعديل طلب بحث عن عمل',
        'jobSeeker.update' => 'تحديث طلب بحث عن عمل',
        'jobSeeker.destroy' => 'حذف طلب بحث عن عمل',
        'jobs.myJobs' => 'لوحة تحكم المستخدم',
        'profile.index' => 'الملف الشخصي'
    ];
    
    foreach ($requiredRoutes as $routeName => $description) {
        try {
            $route = Route::getRoutes()->getByName($routeName);
            if ($route) {
                echo "   ✅ {$routeName} - {$description}\n";
            } else {
                echo "   ❌ {$routeName} - غير موجود\n";
            }
        } catch (Exception $e) {
            echo "   ❌ {$routeName} - خطأ: {$e->getMessage()}\n";
        }
    }

    // 2. فحص الملفات المحدثة
    echo "\n2️⃣ فحص الملفات المحدثة:\n";
    
    $requiredFiles = [
        'resources/views/Jobs/post_job_user.blade.php' => 'صفحة إنشاء طلب بحث عن عمل',
        'resources/views/Jobs/edit_job_user.blade.php' => 'صفحة تعديل طلب بحث عن عمل',
        'resources/views/Jobs/my-jobs.blade.php' => 'لوحة تحكم المستخدم',
        'resources/views/profile/index.blade.php' => 'الملف الشخصي المحدث',
        'app/Http/Controllers/JobSeekerController.php' => 'JobSeeker Controller'
    ];
    
    foreach ($requiredFiles as $filePath => $description) {
        if (file_exists($filePath)) {
            echo "   ✅ {$description} - {$filePath}\n";
        } else {
            echo "   ❌ {$description} - {$filePath} غير موجود\n";
        }
    }

    // 3. فحص محتوى الملفات المحدثة
    echo "\n3️⃣ فحص محتوى الملفات المحدثة:\n";
    
    // فحص صفحة التعديل
    $editFileContent = file_get_contents('resources/views/Jobs/edit_job_user.blade.php');
    if (strpos($editFileContent, '@extends(\'layouts.appdata\')') !== false) {
        echo "   ✅ صفحة التعديل تستخدم Bootstrap layout\n";
    } else {
        echo "   ❌ صفحة التعديل لا تستخدم Bootstrap layout\n";
    }
    
    if (strpos($editFileContent, 'الموقع المفضل للعمل') !== false) {
        echo "   ✅ حقل الموقع موجود في صفحة التعديل\n";
    } else {
        echo "   ❌ حقل الموقع مفقود في صفحة التعديل\n";
    }
    
    if (strpos($editFileContent, 'form-control') !== false) {
        echo "   ✅ صفحة التعديل تستخدم Bootstrap classes\n";
    } else {
        echo "   ❌ صفحة التعديل لا تستخدم Bootstrap classes\n";
    }

    // فحص لوحة التحكم
    $myJobsContent = file_get_contents('resources/views/Jobs/my-jobs.blade.php');
    if (strpos($myJobsContent, 'قائمة الباحثين عن عمل') !== false) {
        echo "   ✅ زر قائمة الباحثين موجود في لوحة التحكم\n";
    } else {
        echo "   ❌ زر قائمة الباحثين مفقود في لوحة التحكم\n";
    }
    
    if (strpos($myJobsContent, 'الملف الشخصي') !== false) {
        echo "   ✅ زر الملف الشخصي موجود في لوحة التحكم\n";
    } else {
        echo "   ❌ زر الملف الشخصي مفقود في لوحة التحكم\n";
    }

    // فحص Controller
    $controllerContent = file_get_contents('app/Http/Controllers/JobSeekerController.php');
    if (strpos($controllerContent, "redirect()->route('job_seekers.index')") !== false) {
        echo "   ✅ Controller يوجه لقائمة الباحثين بعد الحفظ\n";
    } else {
        echo "   ❌ Controller لا يوجه لقائمة الباحثين بعد الحفظ\n";
    }

    // 4. فحص قاعدة البيانات
    echo "\n4️⃣ فحص قاعدة البيانات:\n";
    
    try {
        $jobSeekersCount = JobSeeker::count();
        echo "   📊 عدد طلبات البحث عن عمل: {$jobSeekersCount}\n";
        
        if ($jobSeekersCount > 0) {
            $latestJobSeeker = JobSeeker::latest()->first();
            echo "   📋 آخر طلب: {$latestJobSeeker->job_title}\n";
            echo "   👤 صاحب الطلب: {$latestJobSeeker->user->name}\n";
            echo "   📍 الموقع: " . ($latestJobSeeker->location ?? 'غير محدد') . "\n";
            
            // فحص حقل الموقع
            if ($latestJobSeeker->location) {
                echo "   ✅ حقل الموقع يعمل في قاعدة البيانات\n";
            } else {
                echo "   ⚠️ حقل الموقع فارغ في آخر طلب\n";
            }
        }
        
    } catch (Exception $e) {
        echo "   ❌ خطأ في قاعدة البيانات: {$e->getMessage()}\n";
    }

    // 5. فحص JobSeeker Model
    echo "\n5️⃣ فحص JobSeeker Model:\n";
    
    $fillableFields = [
        'user_id', 'job_title', 'description', 'specialization', 
        'experience', 'skills', 'location', 'whatsapp', 'phone'
    ];
    
    $jobSeeker = new JobSeeker();
    $modelFillable = $jobSeeker->getFillable();
    
    foreach ($fillableFields as $field) {
        if (in_array($field, $modelFillable)) {
            echo "   ✅ {$field} - في fillable\n";
        } else {
            echo "   ❌ {$field} - ليس في fillable\n";
        }
    }

    // 6. اختبار URLs
    echo "\n6️⃣ اختبار URLs:\n";
    
    $testUrls = [
        '/job-seekers' => 'صفحة إنشاء طلب بحث عن عمل',
        '/jobSeekers' => 'قائمة الباحثين عن عمل',
        '/my-jobs' => 'لوحة تحكم المستخدم',
        '/profile' => 'الملف الشخصي'
    ];
    
    foreach ($testUrls as $url => $description) {
        try {
            $routeName = Route::getRoutes()->match(
                \Illuminate\Http\Request::create($url, 'GET')
            )->getName();
            echo "   ✅ {$url} - {$description} (Route: {$routeName})\n";
        } catch (Exception $e) {
            echo "   ❌ {$url} - غير متاح\n";
        }
    }

    // 7. فحص التكامل مع الصور الشخصية
    echo "\n7️⃣ فحص التكامل مع الصور الشخصية:\n";
    
    $user = User::first();
    if ($user) {
        echo "   👤 المستخدم التجريبي: {$user->name}\n";
        
        try {
            $hasImage = $user->hasProfileImage();
            $imageUrl = $user->getProfileImageUrl();
            $defaultAvatar = $user->getDefaultAvatar();
            
            echo "   📸 hasProfileImage(): " . ($hasImage ? 'نعم' : 'لا') . "\n";
            echo "   🖼️ getProfileImageUrl(): " . substr($imageUrl, 0, 50) . "...\n";
            echo "   🎨 getDefaultAvatar(): " . substr($defaultAvatar, 0, 50) . "...\n";
            echo "   ✅ تكامل الصور الشخصية يعمل\n";
            
        } catch (Exception $e) {
            echo "   ❌ خطأ في تكامل الصور الشخصية: {$e->getMessage()}\n";
        }
    }

    echo "\n🎯 خلاصة الاختبار:\n";
    echo "==================\n";
    
    $allGood = true;
    
    // تحقق من Routes الأساسية
    $essentialRoutes = ['job_seekers.create', 'job_seekers.index', 'jobSeeker.edit', 'jobs.myJobs'];
    foreach ($essentialRoutes as $routeName) {
        try {
            $route = Route::getRoutes()->getByName($routeName);
            if (!$route) {
                $allGood = false;
                break;
            }
        } catch (Exception $e) {
            $allGood = false;
            break;
        }
    }
    
    // تحقق من الملفات الأساسية
    $essentialFiles = [
        'resources/views/Jobs/post_job_user.blade.php',
        'resources/views/Jobs/edit_job_user.blade.php',
        'resources/views/Jobs/my-jobs.blade.php'
    ];
    foreach ($essentialFiles as $file) {
        if (!file_exists($file)) {
            $allGood = false;
            break;
        }
    }
    
    if ($allGood) {
        echo "🎉 جميع التحديثات تعمل بشكل صحيح!\n";
        echo "✅ Routes محددة بشكل صحيح\n";
        echo "✅ الملفات محدثة ومتاحة\n";
        echo "✅ حقل الموقع يعمل\n";
        echo "✅ الأزرار الجديدة متاحة\n";
        echo "✅ التكامل مع الصور الشخصية يعمل\n";
        echo "✅ النظام جاهز للاستخدام\n\n";
    } else {
        echo "⚠️ هناك بعض المشاكل تحتاج إلى حل\n";
        echo "🔧 راجع التفاصيل أعلاه لحل المشاكل\n\n";
    }
    
    echo "📋 خطوات الاختبار اليدوي:\n";
    echo "==========================\n";
    echo "1. اذهب إلى: /my-jobs\n";
    echo "2. تحقق من وجود الأزرار الجديدة\n";
    echo "3. انقر على 'نشر بحث عن عمل'\n";
    echo "4. املأ النموذج مع حقل الموقع\n";
    echo "5. احفظ وتأكد من الانتقال لقائمة الباحثين\n";
    echo "6. عدل أي طلب وتحقق من التصميم الجديد\n";
    echo "7. اختبر جميع الأزرار في لوحة التحكم\n\n";

} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
    echo "\nتفاصيل الخطأ:\n";
    echo $e->getTraceAsString() . "\n";
}

echo "📞 للمساعدة:\n";
echo "============\n";
echo "- راجع ملف JOB_SEEKER_COMPLETE_UPDATES.md\n";
echo "- تحقق من ملف JOB_SEEKER_FORM_IMPROVEMENTS.md\n";
echo "- اختبر الصفحات مباشرة في المتصفح\n";
echo "- تأكد من تشغيل: php artisan optimize:clear\n";

?>
