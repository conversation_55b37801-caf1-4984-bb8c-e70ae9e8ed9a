<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Request;

class AdView extends Model
{
    use HasFactory;

    protected $fillable = [
        'ad_id',
        'ip_address',
        'user_agent_hash',
        'user_id',
        'session_id',
        'fingerprint',
        'viewed_at'
    ];

    protected $casts = [
        'viewed_at' => 'datetime',
    ];

    /**
     * العلاقة مع الإعلان
     */
    public function ad()
    {
        return $this->belongsTo(Ad::class);
    }

    /**
     * العلاقة مع المستخدم
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * إنشاء hash للـ user agent
     */
    public static function hashUserAgent($userAgent)
    {
        return hash('sha256', $userAgent);
    }

    /**
     * إنشاء بصمة الجهاز
     */
    public static function generateFingerprint($request)
    {
        $components = [
            $request->ip(),
            $request->header('User-Agent'),
            $request->header('Accept-Language'),
            $request->header('Accept-Encoding'),
            $request->header('Accept'),
        ];

        // إضافة معلومات إضافية إذا كانت متاحة
        if ($request->header('X-Forwarded-For')) {
            $components[] = $request->header('X-Forwarded-For');
        }

        return hash('sha256', implode('|', array_filter($components)));
    }

    /**
     * التحقق من وجود مشاهدة سابقة لنفس الجهاز
     */
    public static function hasViewed($adId, $request, $userId = null)
    {
        $ipAddress = $request->ip();
        $userAgentHash = self::hashUserAgent($request->header('User-Agent', ''));
        $sessionId = $request->session()->getId();
        $fingerprint = self::generateFingerprint($request);

        // البحث عن مشاهدة سابقة بعدة طرق
        $query = self::where('ad_id', $adId);

        // الطريقة الأولى: IP + User Agent
        $query->where(function($q) use ($ipAddress, $userAgentHash) {
            $q->where('ip_address', $ipAddress)
              ->where('user_agent_hash', $userAgentHash);
        });

        // الطريقة الثانية: نفس المستخدم المسجل
        if ($userId) {
            $query->orWhere(function($q) use ($adId, $userId) {
                $q->where('ad_id', $adId)
                  ->where('user_id', $userId);
            });
        }

        // الطريقة الثالثة: نفس الجلسة
        if ($sessionId) {
            $query->orWhere(function($q) use ($adId, $sessionId) {
                $q->where('ad_id', $adId)
                  ->where('session_id', $sessionId);
            });
        }

        // الطريقة الرابعة: نفس بصمة الجهاز
        if ($fingerprint) {
            $query->orWhere(function($q) use ($adId, $fingerprint) {
                $q->where('ad_id', $adId)
                  ->where('fingerprint', $fingerprint);
            });
        }

        return $query->exists();
    }

    /**
     * تسجيل مشاهدة جديدة
     */
    public static function recordView($adId, $request, $userId = null)
    {
        // التحقق من عدم وجود مشاهدة سابقة
        if (self::hasViewed($adId, $request, $userId)) {
            return false; // مشاهدة مكررة
        }

        try {
            // تسجيل المشاهدة الجديدة
            self::create([
                'ad_id' => $adId,
                'ip_address' => $request->ip(),
                'user_agent_hash' => self::hashUserAgent($request->header('User-Agent', '')),
                'user_id' => $userId,
                'session_id' => $request->session()->getId(),
                'fingerprint' => self::generateFingerprint($request),
                'viewed_at' => now(),
            ]);

            return true; // مشاهدة جديدة مسجلة
        } catch (\Exception $e) {
            // في حالة خطأ (مثل duplicate key)، نعتبرها مشاهدة مكررة
            return false;
        }
    }

    /**
     * حذف المشاهدات القديمة (أكثر من 30 يوم)
     */
    public static function cleanOldViews($days = 30)
    {
        return self::where('viewed_at', '<', now()->subDays($days))->delete();
    }

    /**
     * إحصائيات المشاهدات لإعلان معين
     */
    public static function getAdViewStats($adId)
    {
        return [
            'total_views' => self::where('ad_id', $adId)->count(),
            'unique_ips' => self::where('ad_id', $adId)->distinct('ip_address')->count(),
            'registered_users' => self::where('ad_id', $adId)->whereNotNull('user_id')->distinct('user_id')->count(),
            'anonymous_views' => self::where('ad_id', $adId)->whereNull('user_id')->count(),
            'today_views' => self::where('ad_id', $adId)->whereDate('viewed_at', today())->count(),
            'this_week_views' => self::where('ad_id', $adId)->whereBetween('viewed_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'this_month_views' => self::where('ad_id', $adId)->whereMonth('viewed_at', now()->month)->count(),
        ];
    }

    /**
     * الحصول على المشاهدات اليومية لإعلان معين
     */
    public static function getDailyViews($adId, $days = 30)
    {
        return self::where('ad_id', $adId)
            ->where('viewed_at', '>=', now()->subDays($days))
            ->selectRaw('DATE(viewed_at) as date, COUNT(*) as views')
            ->groupBy('date')
            ->orderBy('date')
            ->get();
    }
}
