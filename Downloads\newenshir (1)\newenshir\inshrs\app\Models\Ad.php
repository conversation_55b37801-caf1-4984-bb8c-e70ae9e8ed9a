<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class Ad extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'title', 'description', 'image', 'image_id', 'category', 'subcategory', 'location',
        'latitude', 'longitude', 'price', 'whatsapp', 'email', 'phone', 'views',
        'is_featured', 'featured_until'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_featured' => 'boolean',
        'featured_until' => 'datetime',
        'latitude' => 'decimal:7',
        'longitude' => 'decimal:7',
    ];


    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع التعليقات
     */
    public function comments()
    {
        return $this->hasMany(Comment::class);
    }

    /**
     * العلاقة مع نموذج الصورة
     */
    public function dbImage()
    {
        return $this->belongsTo(Image::class, 'image_id');
    }

    /**
     * العلاقة مع صور الإعلان المتعددة
     */
    public function images()
    {
        return $this->hasMany(AdImage::class)->orderBy('order');
    }

    /**
     * نطاق للإعلانات المميزة النشطة
     */
    public function scopeActiveFeatured($query)
    {
        return $query->where('is_featured', true)
                    ->where(function($q) {
                        $q->whereNull('featured_until')
                          ->orWhere('featured_until', '>', now());
                    });
    }

    /**
     * التحقق مما إذا كان الإعلان مميزًا ونشطًا
     */
    public function isActiveFeatured()
    {
        return $this->is_featured &&
               ($this->featured_until === null || $this->featured_until > now());
    }

    /**
     * الحصول على مسار الصورة الرئيسية مع صورة افتراضية إذا لم تكن موجودة
     */
    public function getImageUrl()
    {
        // التحقق أولاً من وجود صور متعددة
        $firstImage = $this->images()->first();
        if ($firstImage) {
            return $firstImage->getImageUrl();
        }

        // التحقق من وجود صورة في قاعدة البيانات (للتوافق مع النظام القديم)
        if ($this->image_id && $this->dbImage) {
            // إرجاع رابط الصورة من قاعدة البيانات
            return route('images.show', $this->image_id) . '?v=' . time();
        }
        // إذا كان هناك صورة مخزنة في نظام الملفات (للتوافق مع النظام القديم)
        elseif ($this->image) {
            // تحديد ما إذا كانت الصورة في المجلد الجديد (public/images)
            if (strpos($this->image, 'images/ads/') === 0) {
                // الصورة في المجلد الجديد - استخدام المسار المباشر
                $imagePath = public_path($this->image);

                if (file_exists($imagePath)) {
                    // إضافة معلمة عشوائية لتجنب التخزين المؤقت للمتصفح
                    return url($this->image) . '?v=' . time();
                } else {
                    // تسجيل خطأ إذا كان الملف غير موجود
                    Log::error('Ad image file not found in public directory: ' . $imagePath . ' for ad ID: ' . $this->id);
                }
            } else {
                // الصورة في المجلد القديم (storage/app/public) - محاولة نسخها إلى المجلد الجديد
                $storagePath = storage_path('app/public/' . $this->image);

                if (file_exists($storagePath)) {
                    // محاولة نسخ الصورة إلى المجلد الجديد
                    $fileName = basename($this->image);
                    $newRelativePath = 'images/ads/' . $fileName;
                    $newFullPath = public_path($newRelativePath);

                    // التأكد من وجود المجلد الهدف
                    $targetDir = public_path('images/ads');
                    if (!is_dir($targetDir)) {
                        mkdir($targetDir, 0755, true);
                    }

                    // نسخ الملف إذا لم يكن موجودًا بالفعل
                    if (!file_exists($newFullPath)) {
                        if (copy($storagePath, $newFullPath)) {
                            // تحديث مسار الصورة في قاعدة البيانات
                            $this->image = $newRelativePath;
                            $this->save();

                            // إنشاء سجل في جدول صور الإعلانات
                            AdImage::create([
                                'ad_id' => $this->id,
                                'image' => $newRelativePath,
                                'order' => 0
                            ]);

                            Log::info('Image copied from storage to public: ' . $storagePath . ' -> ' . $newFullPath);
                            return url($newRelativePath) . '?v=' . time();
                        } else {
                            Log::error('Failed to copy image from storage to public: ' . $storagePath . ' -> ' . $newFullPath);
                        }
                    } else {
                        // الملف موجود بالفعل في المجلد الجديد، تحديث قاعدة البيانات فقط
                        $this->image = $newRelativePath;
                        $this->save();

                        // إنشاء سجل في جدول صور الإعلانات إذا لم يكن موجودًا
                        if (!$this->images()->where('image', $newRelativePath)->exists()) {
                            AdImage::create([
                                'ad_id' => $this->id,
                                'image' => $newRelativePath,
                                'order' => 0
                            ]);
                        }

                        Log::info('Image path updated in database: ' . $newRelativePath);
                        return url($newRelativePath) . '?v=' . time();
                    }

                    // إذا فشلت عملية النسخ، استخدم المسار القديم
                    return url('storage-direct.php?file=' . urlencode($this->image) . '&v=' . time());
                } else {
                    // تسجيل خطأ إذا كان الملف غير موجود
                    Log::error('Ad image file not found in storage: ' . $storagePath . ' for ad ID: ' . $this->id);
                }
            }
        }

        // إرجاع الصورة الافتراضية
        return 'https://png.pngtree.com/element_our/20190528/ourlarge/pngtree-no-photography-image_1128321.jpg';
    }

    /**
     * الحصول على جميع صور الإعلان
     */
    public function getAllImages()
    {
        $images = $this->images;

        // إذا لم تكن هناك صور في الجدول الجديد ولكن هناك صورة قديمة
        if ($images->isEmpty() && ($this->image || $this->image_id)) {
            // إنشاء كائن مؤقت يحتوي على بيانات الصورة القديمة
            $tempImage = new \stdClass();
            $tempImage->id = 0;
            $tempImage->url = $this->getImageUrl();

            return collect([$tempImage]);
        }

        return $images->map(function($image) {
            $tempImage = new \stdClass();
            $tempImage->id = $image->id;
            $tempImage->url = $image->getImageUrl();
            return $tempImage;
        });
    }

    /**
     * العلاقة مع مشاهدات الإعلان
     */
    public function adViews()
    {
        return $this->hasMany(AdView::class);
    }

    /**
     * زيادة عدد المشاهدات (الطريقة القديمة - للتوافق)
     */
    public function incrementViews()
    {
        $this->increment('views');
    }

    /**
     * زيادة عدد المشاهدات مع التحقق من عدم التكرار
     */
    public function incrementViewsUnique($request, $userId = null)
    {
        // تسجيل المشاهدة في جدول ad_views
        $newView = AdView::recordView($this->id, $request, $userId);

        if ($newView) {
            // زيادة العداد في جدول ads فقط إذا كانت مشاهدة جديدة
            $this->increment('views');
            return true;
        }

        return false; // مشاهدة مكررة
    }

    /**
     * الحصول على عدد المشاهدات المنسق
     */
    public function getFormattedViews()
    {
        $views = $this->views ?? 0;

        if ($views >= 1000000) {
            return round($views / 1000000, 1) . 'M';
        } elseif ($views >= 1000) {
            return round($views / 1000, 1) . 'K';
        }

        return number_format($views);
    }

    /**
     * إنشاء عدد مشاهدات عشوائي واقعي للإعلانات الموجودة
     */
    public function generateRealisticViews()
    {
        // حساب عدد الأيام منذ إنشاء الإعلان
        $daysOld = $this->created_at->diffInDays(now());

        // حساب عدد المشاهدات بناءً على عمر الإعلان
        $baseViews = max(1, $daysOld * rand(5, 25)); // 5-25 مشاهدة يومياً

        // إضافة عشوائية إضافية
        $randomBoost = rand(0, 100);

        // إعلانات مميزة تحصل على مشاهدات أكثر
        $featuredMultiplier = $this->is_featured ? rand(2, 5) : 1;

        $totalViews = ($baseViews + $randomBoost) * $featuredMultiplier;

        // تحديث عدد المشاهدات
        $this->update(['views' => $totalViews]);

        return $totalViews;
    }
}
