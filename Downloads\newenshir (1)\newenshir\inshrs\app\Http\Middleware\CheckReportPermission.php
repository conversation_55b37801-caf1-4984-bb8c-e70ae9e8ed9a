<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckReportPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // التحقق من تسجيل الدخول
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        // التحقق من أن المستخدم مسؤول
        if (!Auth::user()->is_admin) {
            return redirect()->route('dashboard')->with('error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة');
        }

        // التحقق من صلاحية عرض البلاغات (إذا كان نظام الصلاحيات مفعل)
        // تعطيل التحقق من الصلاحيات مؤقتاً للتأكد من أن المشكلة ليست هنا
        /*
        if (class_exists('App\Models\Permission') && method_exists(Auth::user(), 'hasPermission')) {
            if (!Auth::user()->hasPermission('reports.view')) {
                return redirect()->route('admin.dashboard')->with('error', 'ليس لديك صلاحية للوصول إلى صفحة البلاغات');
            }
        }
        */

        return $next($request);
    }
}
