@extends('layouts.appdata')

@section('content')
<div class="container mt-5 text-end" dir="rtl">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-lg border-0 rounded-4">
                <div class="card-header bg-gradient text-white text-center py-4 rounded-top-4">
                    <h2 class="fw-bold mb-2">
                        <i class="fas fa-edit me-2"></i>
                        تعديل طلب البحث عن عمل
                    </h2>
                    <p class="mb-0 opacity-90">قم بتحديث بيانات طلبك للحصول على فرص أفضل</p>
                </div>
                <div class="card-body">

                    <!-- عرض رسائل الأخطاء -->
                    @if ($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <h6 class="fw-bold">
                                <i class="fas fa-exclamation-triangle me-2"></i>يرجى تصحيح الأخطاء التالية:
                            </h6>
                            <ul class="list-unstyled mb-0 mt-2">
                                @foreach ($errors->all() as $error)
                                    <li><i class="fas fa-times-circle me-2"></i>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <!-- عرض رسالة النجاح -->
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <form action="{{ route('jobSeeker.update', $jobSeeker->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <!-- عنوان الوظيفة المطلوبة -->
                        <div class="mb-4">
                            <label class="form-label fw-bold text-primary">
                                <i class="fas fa-briefcase me-2"></i>عنوان الوظيفة المطلوبة *
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-primary text-white"><i class="fas fa-briefcase"></i></span>
                                <input type="text" name="job_title" class="form-control form-control-lg"
                                       placeholder="مثال: مطور ويب، مصمم جرافيك، محاسب..."
                                       value="{{ old('job_title', $jobSeeker->job_title) }}" required>
                            </div>
                        </div>

                        <!-- وصف الوظيفة المطلوبة -->
                        <div class="mb-4">
                            <label class="form-label fw-bold text-primary">
                                <i class="fas fa-align-left me-2"></i>وصف الوظيفة المطلوبة *
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-success text-white"><i class="fas fa-align-left"></i></span>
                                <textarea name="description" class="form-control" rows="4"
                                          placeholder="اكتب وصفاً مفصلاً للوظيفة التي تبحث عنها..." required>{{ old('description', $jobSeeker->description) }}</textarea>
                            </div>
                        </div>

                        <!-- التخصص -->
                        <div class="mb-4">
                            <label class="form-label fw-bold text-primary">
                                <i class="fas fa-user-graduate me-2"></i>التخصص *
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-info text-white"><i class="fas fa-user-graduate"></i></span>
                                <input type="text" name="specialization" class="form-control"
                                       placeholder="مثال: هندسة حاسوب، إدارة أعمال، تسويق..."
                                       value="{{ old('specialization', $jobSeeker->specialization) }}" required>
                            </div>
                        </div>

                        <!-- سنوات الخبرة -->
                        <div class="mb-4">
                            <label class="form-label fw-bold text-primary">
                                <i class="fas fa-clock me-2"></i>سنوات الخبرة
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-warning text-white"><i class="fas fa-clock"></i></span>
                                <select name="experience" class="form-select">
                                    <option value="">اختر سنوات الخبرة</option>
                                    <option value="0" {{ old('experience', $jobSeeker->experience) == '0' ? 'selected' : '' }}>بدون خبرة (خريج جديد)</option>
                                    <option value="1" {{ old('experience', $jobSeeker->experience) == '1' ? 'selected' : '' }}>سنة واحدة</option>
                                    <option value="2" {{ old('experience', $jobSeeker->experience) == '2' ? 'selected' : '' }}>سنتان</option>
                                    <option value="3" {{ old('experience', $jobSeeker->experience) == '3' ? 'selected' : '' }}>3 سنوات</option>
                                    <option value="4" {{ old('experience', $jobSeeker->experience) == '4' ? 'selected' : '' }}>4 سنوات</option>
                                    <option value="5" {{ old('experience', $jobSeeker->experience) == '5' ? 'selected' : '' }}>5 سنوات</option>
                                    <option value="6" {{ old('experience', $jobSeeker->experience) == '6' ? 'selected' : '' }}>6-10 سنوات</option>
                                    <option value="10" {{ old('experience', $jobSeeker->experience) == '10' ? 'selected' : '' }}>أكثر من 10 سنوات</option>
                                </select>
                            </div>
                        </div>

                        <!-- المهارات -->
                        <div class="mb-4">
                            <label class="form-label fw-bold text-primary">
                                <i class="fas fa-lightbulb me-2"></i>المهارات والخبرات
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-secondary text-white"><i class="fas fa-lightbulb"></i></span>
                                <textarea name="skills" class="form-control" rows="3"
                                          placeholder="اذكر مهاراتك التقنية والشخصية، البرامج التي تجيدها...">{{ old('skills', $jobSeeker->skills) }}</textarea>
                            </div>
                        </div>

                        <!-- الموقع المفضل -->
                        <div class="mb-4">
                            <label class="form-label fw-bold text-primary">
                                <i class="fas fa-map-marker-alt me-2"></i>الموقع المفضل للعمل *
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-danger text-white"><i class="fas fa-map-marker-alt"></i></span>
                                <input type="text" name="location" class="form-control"
                                       placeholder="مثال: الرياض، جدة، عن بُعد، أي مكان..."
                                       value="{{ old('location', $jobSeeker->location) }}" required>
                            </div>
                            <small class="text-muted">حدد المدينة أو المنطقة المفضلة للعمل</small>
                        </div>

                        <!-- معلومات التواصل -->
                        <div class="mb-4">
                            <h5 class="fw-bold text-primary mb-3">
                                <i class="fas fa-phone me-2"></i>معلومات التواصل
                            </h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">
                                        <i class="fab fa-whatsapp me-2 text-success"></i>رقم الواتساب
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-success text-white"><i class="fab fa-whatsapp"></i></span>
                                        <input type="text" name="whatsapp" class="form-control"
                                               placeholder="مثال: 966501234567"
                                               value="{{ old('whatsapp', $jobSeeker->whatsapp) }}">
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-phone me-2 text-info"></i>رقم الهاتف
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-info text-white"><i class="fas fa-phone"></i></span>
                                        <input type="text" name="phone" class="form-control"
                                               placeholder="مثال: 966501234567"
                                               value="{{ old('phone', $jobSeeker->phone) }}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <button type="submit" class="btn btn-primary btn-lg px-5 rounded-pill shadow-lg">
                                <i class="fas fa-save me-2"></i>
                                تحديث البيانات
                            </button>
                            <a href="{{ route('jobs.myJobs') }}" class="btn btn-outline-secondary btn-lg px-4 rounded-pill">
                                <i class="fas fa-arrow-left me-2"></i>
                                العودة لإدارة المحتوى
                            </a>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="mt-4 p-3 bg-light rounded-3">
                            <h6 class="fw-bold text-primary mb-2">
                                <i class="fas fa-info-circle me-2"></i>ملاحظات مهمة:
                            </h6>
                            <ul class="list-unstyled mb-0 small text-muted">
                                <li><i class="fas fa-check text-success me-2"></i>تأكد من دقة جميع المعلومات قبل الحفظ</li>
                                <li><i class="fas fa-check text-success me-2"></i>الحقول المميزة بـ (*) مطلوبة</li>
                                <li><i class="fas fa-check text-success me-2"></i>سيتم عرض التحديثات فوراً في قائمة الباحثين</li>
                                <li><i class="fas fa-check text-success me-2"></i>يمكنك تعديل البيانات في أي وقت</li>
                            </ul>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .bg-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .input-group-text {
        border: none;
        font-weight: 600;
    }

    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:hover, .form-select:hover {
        border-color: #667eea;
        transform: translateY(-1px);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .btn-outline-secondary {
        border: 2px solid #6c757d;
        transition: all 0.3s ease;
    }

    .btn-outline-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
    }

    .card {
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .text-primary {
        color: #667eea !important;
    }

    @media (max-width: 768px) {
        .container {
            padding: 0 15px;
        }

        .card-body {
            padding: 1.5rem;
        }

        .btn-lg {
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
        }
    }
</style>
@endsection
