@extends('layouts.dashboard')

@section('title', 'لوحة التحكم')

@section('content')
            <!-- إحصائيات المستخدم -->
            @php
                $userAdsCount = \App\Models\Ad::where('user_id', auth()->id())->count();
                // استخدام JobPosting بدلاً من Job
                $userJobsCount = 0;
                if (class_exists('\App\Models\JobPosting')) {
                    $userJobsCount = \App\Models\JobPosting::where('user_id', auth()->id())->count();
                }
                // إضافة عدد الباحثين عن عمل
                $userJobSeekersCount = 0;
                if (class_exists('\App\Models\JobSeeker')) {
                    $userJobSeekersCount = \App\Models\JobSeeker::where('user_id', auth()->id())->count();
                }
                $maxAdsAllowed = config('ads.max_ads_per_user', 2);
                $remainingAds = $maxAdsAllowed - $userAdsCount;
            @endphp

            <div class="user-stats-section">
                <div class="stats-grid">
                    <div class="stat-card ads-stat">
                        <div class="stat-icon">
                            <i class="fas fa-ad"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">{{ $userAdsCount }}/{{ $maxAdsAllowed }}</div>
                            <div class="stat-label">إعلاناتي</div>
                            @if($remainingAds > 0)
                                <div class="stat-note success">يمكنك إضافة {{ $remainingAds }} إعلان</div>
                            @else
                                <div class="stat-note warning">وصلت للحد الأقصى</div>
                            @endif
                        </div>
                    </div>

                    <div class="stat-card jobs-stat">
                        <div class="stat-icon">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">{{ $userJobsCount }}</div>
                            <div class="stat-label">وظائفي</div>
                            <div class="stat-note">الوظائف المنشورة</div>
                        </div>
                    </div>

                    <div class="stat-card job-seekers-stat">
                        <div class="stat-icon">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">{{ $userJobSeekersCount }}</div>
                            <div class="stat-label">طلبات العمل</div>
                            <div class="stat-note">إعلانات البحث عن عمل</div>
                        </div>
                    </div>

                    <div class="stat-card points-stat">
                        <div class="stat-icon">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">{{ auth()->user()->points ?? 0 }}</div>
                            <div class="stat-label">نقاطي</div>
                            <div class="stat-note">للإعلانات المميزة</div>
                        </div>
                    </div>
                </div>

                <!-- أزرار الوصول السريع -->
                <div class="quick-actions-section">
                    <div class="quick-actions-grid">
                        <a href="{{ route('job_seekers.index') }}" class="quick-action-btn job-seekers-btn">
                            <div class="action-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">الباحثين عن عمل</div>
                                <div class="action-subtitle">تصفح طلبات العمل</div>
                            </div>
                            <div class="action-arrow">
                                <i class="fas fa-chevron-left"></i>
                            </div>
                        </a>

                        <a href="{{ route('reports.comprehensive') }}" class="quick-action-btn reports-btn">
                            <div class="action-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">التقارير الشاملة</div>
                                <div class="action-subtitle">إحصائيات وتحليلات</div>
                            </div>
                            <div class="action-arrow">
                                <i class="fas fa-chevron-left"></i>
                            </div>
                        </a>

                        <a href="{{ route('ads.create') }}" class="quick-action-btn create-ad-btn @if($remainingAds <= 0) disabled @endif">
                            <div class="action-icon">
                                <i class="fas fa-plus-circle"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">إضافة إعلان</div>
                                <div class="action-subtitle">
                                    @if($remainingAds > 0)
                                        متبقي {{ $remainingAds }} إعلان
                                    @else
                                        وصلت للحد الأقصى
                                    @endif
                                </div>
                            </div>
                            <div class="action-arrow">
                                <i class="fas fa-chevron-left"></i>
                            </div>
                        </a>

                        <a href="{{ route('jobs.create') }}" class="quick-action-btn create-job-btn">
                            <div class="action-icon">
                                <i class="fas fa-briefcase"></i>
                            </div>
                            <div class="action-content">
                                <div class="action-title">إضافة وظيفة</div>
                                <div class="action-subtitle">انشر وظيفة جديدة</div>
                            </div>
                            <div class="action-arrow">
                                <i class="fas fa-chevron-left"></i>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- شريط البحث -->
            <div class="search-bar">
                <i class="fas fa-search search-icon"></i>
                <input type="text" placeholder="اكتب هنا للبحث" class="search-input">
            </div>

            <!-- الخدمات الرئيسية -->
            <div class="services-grid">
                @if(Auth::check() && Auth::user()->is_admin)
                <a href="{{ url('/admin') }}" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="service-title">لوحة تحكم المسؤول</div>
                </a>
                @endif

                <a href="{{ url('/jobs') }}" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <div class="service-title">الوظائف</div>
                </a>

                <a href="{{ url('/ads') }}" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-ad"></i>
                    </div>
                    <div class="service-title">الإعلانات</div>
                </a>

                <a href="{{ route('profile.edit') }}" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="service-title">الملف الشخصي</div>
                </a>

                <a href="{{ url('/my-jobs') }}" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="service-title">إدارة الوظائف</div>
                </a>

                <a href="{{ url('/points/buy') }}" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="service-title">الاشتراكات</div>
                </a>

                <a href="{{ route('resumes.index') }}" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="service-title">منشئ السيرة الذاتية</div>
                </a>
            </div>

            <!-- خدمات أخرى -->
            <div class="other-services-section">
                <h3 class="section-title">خدمات أخرى</h3>

                <div class="services-slider">
                    <div class="slider-controls">
                        <button class="slider-arrow prev-arrow"><i class="fas fa-chevron-right"></i></button>
                        <button class="slider-arrow next-arrow"><i class="fas fa-chevron-left"></i></button>
                    </div>

                    <div class="slider-container">
                        <div class="slides-wrapper">
                            <div class="service-slide active" data-slide-index="0" onclick="window.location='{{ route('jobs.create') }}';" style="cursor: pointer;">
                                <div class="new-badge">جديد</div>
                                <div class="slide-icon">
                                    <i class="fas fa-plus-circle"></i>
                                </div>
                                <div class="slide-title">نشر وظيفة جديدة</div>
                                <div class="slide-description">أضف وظيفة جديدة للباحثين عن عمل</div>
                            </div>

                            <div class="service-slide" data-slide-index="1" onclick="window.location='{{ route('resumes.index') }}';" style="cursor: pointer;">
                                <div class="slide-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div class="slide-title">إنشاء سيرة ذاتية</div>
                                <div class="slide-description">أنشئ سيرة ذاتية احترافية بسهولة</div>
                            </div>

                            <div class="service-slide" data-slide-index="2" onclick="window.location='{{ route('jobs.index') }}';" style="cursor: pointer;">
                                <div class="slide-icon">
                                    <i class="fas fa-search"></i>
                                </div>
                                <div class="slide-title">البحث عن وظائف</div>
                                <div class="slide-description">ابحث عن وظائف تناسب مهاراتك</div>
                            </div>

                            <div class="service-slide" data-slide-index="3" onclick="window.location='{{ route('ads.create') }}';" style="cursor: pointer;">
                                <div class="slide-icon">
                                    <i class="fas fa-ad"></i>
                                </div>
                                <div class="slide-title">نشر إعلان</div>
                                <div class="slide-description">أضف إعلاناً للوصول إلى جمهور أكبر</div>
                            </div>

                            <div class="service-slide" data-slide-index="4" onclick="window.location='{{ url('/points/buy') }}';" style="cursor: pointer;">
                                <div class="slide-icon">
                                    <i class="fas fa-tags"></i>
                                </div>
                                <div class="slide-title">شراء النقاط</div>
                                <div class="slide-description">اشترِ نقاط لتعزيز وصول إعلاناتك ووظائفك</div>
                            </div>

                            <div class="service-slide" data-slide-index="5" onclick="window.location='{{ route('job_seekers.index') }}';" style="cursor: pointer;">
                                <div class="slide-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="slide-title">الباحثين عن عمل</div>
                                <div class="slide-description">تصفح قائمة الباحثين عن عمل</div>
                            </div>

                            <div class="service-slide" data-slide-index="6" onclick="window.location='{{ url('/support') }}';" style="cursor: pointer;">
                                <div class="slide-icon">
                                    <i class="fas fa-headset"></i>
                                </div>
                                <div class="slide-title">الدعم الفني</div>
                                <div class="slide-description">تواصل مع فريق الدعم الفني للمساعدة</div>
                            </div>

                            <div class="service-slide" data-slide-index="7" onclick="window.location='{{ url('/ads') }}';" style="cursor: pointer;">
                                <div class="slide-icon">
                                    <i class="fas fa-bullhorn"></i>
                                </div>
                                <div class="slide-title">تصفح الإعلانات</div>
                                <div class="slide-description">استعرض جميع الإعلانات المتاحة</div>
                            </div>

                            <div class="service-slide" data-slide-index="8" onclick="window.location='{{ url('/chat') }}';" style="cursor: pointer;">
                                <div class="slide-icon">
                                    <i class="fas fa-comments"></i>
                                </div>
                                <div class="slide-title">المحادثات</div>
                                <div class="slide-description">تواصل مع المستخدمين الآخرين</div>
                            </div>
                        </div>
                    </div>

                    <div class="slider-dots">
                        <span class="dot active" data-slide-index="0"></span>
                        <span class="dot" data-slide-index="1"></span>
                        <span class="dot" data-slide-index="2"></span>
                        <span class="dot" data-slide-index="3"></span>
                        <span class="dot" data-slide-index="4"></span>
                        <span class="dot" data-slide-index="5"></span>
                        <span class="dot" data-slide-index="6"></span>
                        <span class="dot" data-slide-index="7"></span>
                        <span class="dot" data-slide-index="8"></span>
                    </div>
                </div>
<style>
    /* إحصائيات المستخدم */
    .user-stats-section {
        margin-bottom: 2rem;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .stat-card {
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        border-radius: 12px;
        padding: 1.5rem;
        display: flex;
        align-items: center;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #E67E22, #F39C12);
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    }

    .ads-stat::before {
        background: linear-gradient(90deg, #E67E22, #F39C12);
    }

    .jobs-stat::before {
        background: linear-gradient(90deg, #3498DB, #2980B9);
    }

    .points-stat::before {
        background: linear-gradient(90deg, #F1C40F, #F39C12);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        background: rgba(230, 126, 34, 0.1);
    }

    .ads-stat .stat-icon {
        background: rgba(230, 126, 34, 0.1);
    }

    .jobs-stat .stat-icon {
        background: rgba(52, 152, 219, 0.1);
    }

    .points-stat .stat-icon {
        background: rgba(241, 196, 15, 0.1);
    }

    .stat-icon i {
        font-size: 1.5rem;
        color: #E67E22;
    }

    .jobs-stat .stat-icon i {
        color: #3498DB;
    }

    .points-stat .stat-icon i {
        color: #F1C40F;
    }

    .job-seekers-stat .stat-icon {
        background: rgba(155, 89, 182, 0.1);
    }

    .job-seekers-stat .stat-icon i {
        color: #9B59B6;
    }

    .job-seekers-stat::before {
        background: linear-gradient(90deg, #9B59B6, #8E44AD);
    }

    .stat-content {
        flex: 1;
    }

    .stat-number {
        font-size: 1.75rem;
        font-weight: 700;
        color: #2c3e50;
        line-height: 1;
        margin-bottom: 0.25rem;
    }

    .stat-label {
        font-size: 1rem;
        font-weight: 600;
        color: #34495e;
        margin-bottom: 0.25rem;
    }

    .stat-note {
        font-size: 0.8rem;
        font-weight: 500;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        display: inline-block;
    }

    .stat-note.success {
        background: rgba(39, 174, 96, 0.1);
        color: #27ae60;
    }

    .stat-note.warning {
        background: rgba(231, 76, 60, 0.1);
        color: #e74c3c;
    }

    .stat-note:not(.success):not(.warning) {
        background: rgba(149, 165, 166, 0.1);
        color: #95a5a6;
    }

    /* أزرار الوصول السريع */
    .quick-actions-section {
        margin-top: 2rem;
    }

    .quick-actions-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .quick-action-btn {
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        border: 1px solid rgba(0, 0, 0, 0.08);
        border-radius: 12px;
        padding: 1.25rem;
        display: flex;
        align-items: center;
        text-decoration: none;
        color: inherit;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .quick-action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #E67E22, #F39C12);
    }

    .quick-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
        text-decoration: none;
        color: inherit;
    }

    .quick-action-btn.disabled {
        opacity: 0.6;
        cursor: not-allowed;
        pointer-events: none;
    }

    .job-seekers-btn::before {
        background: linear-gradient(90deg, #9B59B6, #8E44AD);
    }

    .reports-btn::before {
        background: linear-gradient(90deg, #E74C3C, #C0392B);
    }

    .create-ad-btn::before {
        background: linear-gradient(90deg, #E67E22, #F39C12);
    }

    .create-job-btn::before {
        background: linear-gradient(90deg, #3498DB, #2980B9);
    }

    .action-icon {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        background: rgba(230, 126, 34, 0.1);
    }

    .job-seekers-btn .action-icon {
        background: rgba(155, 89, 182, 0.1);
    }

    .reports-btn .action-icon {
        background: rgba(231, 76, 60, 0.1);
    }

    .create-ad-btn .action-icon {
        background: rgba(230, 126, 34, 0.1);
    }

    .create-job-btn .action-icon {
        background: rgba(52, 152, 219, 0.1);
    }

    .action-icon i {
        font-size: 1.25rem;
        color: #E67E22;
    }

    .job-seekers-btn .action-icon i {
        color: #9B59B6;
    }

    .reports-btn .action-icon i {
        color: #E74C3C;
    }

    .create-ad-btn .action-icon i {
        color: #E67E22;
    }

    .create-job-btn .action-icon i {
        color: #3498DB;
    }

    .action-content {
        flex: 1;
    }

    .action-title {
        font-size: 1rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.25rem;
    }

    .action-subtitle {
        font-size: 0.85rem;
        color: #7f8c8d;
    }

    .action-arrow {
        margin-right: 0.75rem;
        opacity: 0.6;
        transition: all 0.3s ease;
    }

    .quick-action-btn:hover .action-arrow {
        opacity: 1;
        transform: translateX(-3px);
    }

    .action-arrow i {
        font-size: 0.9rem;
        color: #95a5a6;
    }

    /* تنسيقات متجاوبة للإحصائيات */
    @media (max-width: 767px) {
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 0.75rem;
        }

        .quick-actions-grid {
            grid-template-columns: 1fr;
            gap: 0.75rem;
        }

        .stat-card {
            padding: 1rem;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            margin-left: 0.75rem;
        }

        .stat-icon i {
            font-size: 1.25rem;
        }

        .stat-number {
            font-size: 1.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
        }
    }

    @media (max-width: 480px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }

        .stat-card {
            flex-direction: column;
            text-align: center;
        }

        .stat-icon {
            margin-left: 0;
            margin-bottom: 0.75rem;
        }

        .quick-action-btn {
            padding: 1rem;
        }

        .action-icon {
            width: 40px;
            height: 40px;
            margin-left: 0.75rem;
        }

        .action-icon i {
            font-size: 1rem;
        }

        .action-title {
            font-size: 0.9rem;
        }

        .action-subtitle {
            font-size: 0.8rem;
        }
    }

    /* شريط البحث */
    .search-bar {
        background-color: #fff;
        border-radius: 8px;
        padding: 0.75rem 1rem;
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .search-icon {
        color: #999;
        margin-left: 0.75rem;
    }

    .search-input {
        border: none;
        width: 100%;
        font-size: 1rem;
        color: #333;
        text-align: right;
    }

    .search-input:focus {
        outline: none;
    }

    .search-input::placeholder {
        color: #999;
    }

    /* شبكة الخدمات */
    .services-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .service-card {
        background-color: #fff;
        border-radius: 8px;
        padding: 1.5rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        color: #333;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .service-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .service-icon {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
    }

    .service-icon i {
        font-size: 2rem;
        color: #E67E22;
    }

    .service-title {
        font-weight: 600;
        font-size: 1rem;
        text-align: center;
    }

    /* قسم الخدمات الأخرى */
    .other-services-section {
        margin-top: 2rem;
    }

    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #666;
        margin-bottom: 1rem;
        position: relative;
        text-align: center;
    }

    .section-title::before,
    .section-title::after {
        content: '';
        position: absolute;
        top: 50%;
        height: 1px;
        background-color: #ddd;
        width: 35%;
    }

    .section-title::before {
        right: 0;
    }

    .section-title::after {
        left: 0;
    }

    /* سلايدر الخدمات */
    .services-slider {
        position: relative;
        margin-top: 1.5rem;
    }

    .slider-controls {
        display: flex;
        justify-content: space-between;
        position: absolute;
        width: 100%;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
    }

    .slider-arrow {
        background-color: #fff;
        border: none;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        cursor: pointer;
        color: #666;
    }

    .slider-container {
        margin: 0 2rem;
    }

    .slides-wrapper {
        display: flex;
        overflow: hidden;
        position: relative;
        width: 100%;
    }

    .service-slide {
        background-color: #fff;
        border-radius: 8px;
        padding: 1.5rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        min-width: 100%;
        transition: transform 0.5s ease;
        opacity: 0;
        visibility: hidden;
        position: absolute;
        top: 0;
        right: 0;
    }

    .service-slide.active {
        opacity: 1;
        visibility: visible;
        position: relative;
    }

    .new-badge {
        position: absolute;
        top: -10px;
        right: -10px;
        background-color: #e53e3e;
        color: white;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        transform: rotate(15deg);
    }

    .slide-icon {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
    }

    .slide-icon i {
        font-size: 1.75rem;
        color: #E67E22;
    }

    .slide-title {
        font-weight: 600;
        font-size: 1rem;
        text-align: center;
        margin-bottom: 0.5rem;
    }

    .slide-description {
        font-size: 0.875rem;
        color: #666;
        text-align: center;
        line-height: 1.4;
    }

    .slider-dots {
        display: flex;
        justify-content: center;
        margin-top: 1rem;
    }

    .dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #ddd;
        margin: 0 0.25rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .dot:hover {
        background-color: #ccc;
    }

    .dot.active {
        background-color: #E67E22;
        transform: scale(1.2);
    }

    /* تنسيقات متجاوبة للجوال */
    @media (max-width: 767px) {
        .services-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 0.75rem;
        }

        .service-card {
            padding: 1rem;
        }

        .service-icon {
            width: 50px;
            height: 50px;
            margin-bottom: 0.75rem;
        }

        .service-icon i {
            font-size: 1.5rem;
        }

        .service-title {
            font-size: 0.875rem;
        }

        .section-title {
            font-size: 1.125rem;
        }

        .section-title::before,
        .section-title::after {
            width: 30%;
        }

        .slider-arrow {
            width: 35px;
            height: 35px;
        }

        .service-slide {
            padding: 1.25rem;
        }

        .slide-icon {
            width: 40px;
            height: 40px;
        }

        .slide-icon i {
            font-size: 1.5rem;
        }

        .slide-title {
            font-size: 0.875rem;
        }
    }

    /* تنسيقات للشاشات الأكبر */
    @media (min-width: 1024px) {
        .services-grid {
            grid-template-columns: repeat(5, 1fr);
        }
    }


</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تفعيل سلايدر الخدمات
        const slides = document.querySelectorAll('.service-slide');
        const prevArrow = document.querySelector('.prev-arrow');
        const nextArrow = document.querySelector('.next-arrow');
        const dots = document.querySelectorAll('.dot');
        let currentSlide = 0;
        let slideInterval;
        const totalSlides = slides.length; // سيتم تحديثه تلقائيًا ليكون 9 بناءً على عدد العناصر

        // وظيفة عرض الشريحة المحددة
        function showSlide(index) {
            // التأكد من أن الرقم ضمن النطاق
            if (index < 0) index = totalSlides - 1;
            if (index >= totalSlides) index = 0;

            // إزالة الفئة النشطة من جميع الشرائح
            slides.forEach(slide => slide.classList.remove('active'));
            dots.forEach(dot => dot.classList.remove('active'));

            // إضافة الفئة النشطة للشريحة المحددة
            slides[index].classList.add('active');
            dots[index].classList.add('active');

            // تحديث الشريحة الحالية
            currentSlide = index;
        }

        // وظيفة الانتقال للشريحة التالية
        function nextSlide() {
            showSlide(currentSlide + 1);
        }

        // وظيفة الانتقال للشريحة السابقة
        function prevSlide() {
            showSlide(currentSlide - 1);
        }

        // تفعيل أزرار التنقل
        if (prevArrow && nextArrow) {
            prevArrow.addEventListener('click', function() {
                prevSlide();
                resetInterval();
            });

            nextArrow.addEventListener('click', function() {
                nextSlide();
                resetInterval();
            });
        }

        // تفعيل النقاط
        dots.forEach((dot, index) => {
            dot.addEventListener('click', function() {
                showSlide(index);
                resetInterval();
            });
        });

        // بدء التنقل التلقائي
        function startInterval() {
            slideInterval = setInterval(nextSlide, 5000); // تغيير الشريحة كل 5 ثوانٍ
        }

        // إعادة تعيين الفاصل الزمني
        function resetInterval() {
            clearInterval(slideInterval);
            startInterval();
        }

        // بدء العرض التلقائي
        startInterval();
    });
</script>

@endsection

