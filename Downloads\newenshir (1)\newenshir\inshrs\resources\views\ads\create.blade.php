<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>نشر إعلان جديد</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('images/enshir.ico') }}" />
    <link rel="shortcut icon" type="image/x-icon" href="{{ asset('images/enshir.ico') }}" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <!-- إضافة مكتبة Leaflet للخرائط -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    <!-- إضافة ملف الألوان المخصص -->
    <link href="{{ asset('css/colors.css') }}" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Tajawal', sans-serif;
            direction: rtl;
            text-align: right;
        }

        .card {
            border-radius: 1rem;
        }

        label {
            font-weight: 600;
        }

        .form-control:focus {
            box-shadow: 0 0 0 0.15rem rgba(13,110,253,.25);
        }

        button[type="submit"]:hover {
            transform: translateY(-2px);
            transition: all 0.3s ease-in-out;
        }

        /* أنماط الخريطة */
        #map-container {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 9999;
            justify-content: center;
            align-items: center;
            padding: 20px;
            box-sizing: border-box;
        }

        #map-modal {
            width: 95%;
            max-width: 900px;
            height: 85%;
            max-height: 700px;
            min-height: 500px;
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        #map-header {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid #eee;
            background-color: #f8f9fa;
        }

        #map-header h3 {
            margin: 0;
            color: #333;
            font-size: 1.2rem;
        }

        #map-close {
            background: #dc3545;
            color: white;
            border: none;
            font-size: 20px;
            cursor: pointer;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        #map-close:hover {
            background: #c82333;
            transform: scale(1.1);
        }

        #map {
            flex: 1;
            width: 100%;
            min-height: 300px;
            position: relative;
        }

        /* إصلاح مشكلة عرض الخريطة */
        .leaflet-container {
            height: 100% !important;
            width: 100% !important;
        }

        #map-footer {
            padding: 20px;
            text-align: center;
            border-top: 2px solid #eee;
            background-color: #f8f9fa;
        }

        #map-coordinates {
            margin-bottom: 15px;
            font-weight: bold;
            color: #333;
        }

        #map-confirm {
            padding: 12px 30px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        #map-confirm:hover {
            background-color: #218838;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        #map-confirm:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .map-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 8px 15px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
        }

        .map-button:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
        }

        /* تحسينات إضافية للصور */
        #images-preview-container {
            min-height: 80px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #f8f9fa;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
            justify-content: flex-start;
        }

        #images-preview-container:empty::before {
            content: "لم يتم اختيار صور بعد - انقر على 'اختيار ملفات' لإضافة صور (يمكنك إضافة حتى 5 صور)";
            color: #6c757d;
            font-style: italic;
            display: block;
            text-align: center;
            width: 100%;
            padding: 20px;
        }

        #images-preview-container.has-images::before {
            display: none;
        }

        .image-preview-item {
            position: relative;
            display: inline-block;
            margin: 5px;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 5px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }

        .image-preview-item:hover {
            transform: scale(1.05);
            border-color: var(--primary-color);
        }

        .image-preview-item img {
            display: block;
            max-height: 120px;
            max-width: 120px;
            object-fit: cover;
            border-radius: 4px;
        }

        .image-delete-btn {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #dc3545;
            color: white;
            border: 2px solid white;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            transition: background-color 0.2s ease;
        }

        .image-delete-btn:hover {
            background: #c82333;
        }

        /* تحسين أزرار الموقع */
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* تحسين رسائل الحالة */
        .form-text {
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        .text-success {
            color: #198754 !important;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        .text-warning {
            color: #fd7e14 !important;
        }

        .text-info {
            color: #0dcaf0 !important;
        }

        .text-primary {
            color: #0d6efd !important;
        }
    </style>
</head>
<body>

    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow-sm p-4 bg-white">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <a href="{{ url()->previous() }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right ml-1"></i> رجوع
                        </a>
                        <h2 class="text-center text-primary fw-bold mb-0">
                            <i class="fas fa-plus-circle ms-2"></i>نشر إعلان جديد
                        </h2>
                        <a href="{{ url('/dashboard') }}" class="btn btn-outline-primary">
                            <i class="fas fa-tachometer-alt ml-1"></i> لوحة التحكم
                        </a>
                    </div>

                    <!-- عرض رسائل النجاح والخطأ -->
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>يرجى تصحيح الأخطاء التالية:</strong>
                            <ul class="mb-0 mt-2">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @php
                        // حساب عدد الإعلانات النشطة للمستخدم
                        $userAdsCount = \App\Models\Ad::where('user_id', auth()->id())->count();
                        $maxAdsAllowed = config('ads.max_ads_per_user', 2);
                        $remainingAds = $maxAdsAllowed - $userAdsCount;
                    @endphp

                    <!-- تحذير حول الحد الأقصى للإعلانات -->
                    @if($userAdsCount >= $maxAdsAllowed)
                        <div class="alert alert-warning alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> لقد وصلت للحد الأقصى المسموح ({{ $maxAdsAllowed }} إعلانات).
                            يرجى <a href="{{ route('ads.index') }}" class="alert-link">حذف أحد إعلاناتك الحالية</a> لإضافة إعلان جديد.
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @elseif($remainingAds == 1)
                        <div class="alert alert-info alert-dismissible fade show" role="alert">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>معلومة:</strong> يمكنك إضافة إعلان واحد فقط بعد هذا الإعلان.
                            الحد الأقصى المسموح هو {{ $maxAdsAllowed }} إعلانات لكل مستخدم.
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @else
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>ممتاز!</strong> يمكنك إضافة {{ $remainingAds }} إعلان إضافي.
                            الحد الأقصى المسموح هو {{ $maxAdsAllowed }} إعلانات لكل مستخدم.
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <form action="{{ route('ads.store') }}" method="POST" enctype="multipart/form-data" id="ad-form" @if($userAdsCount >= $maxAdsAllowed) style="pointer-events: none; opacity: 0.6;" @endif>
                        @csrf

                        <div class="mb-3">
                            <label for="title" class="form-label">  نوع الاعلان </label>
                            <input type="text" name="title" id="title" class="form-control @error('title') is-invalid @enderror" value="{{ old('title') }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea name="description" id="description" class="form-control @error('description') is-invalid @enderror" rows="4" required>{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="images" class="form-label">صور الإعلان (يمكنك اختيار حتى 5 صور)</label>
                            <input type="file" name="images[]" id="images" class="form-control @error('images') is-invalid @enderror @error('images.*') is-invalid @enderror" accept="image/jpeg,image/jpg,image/png,image/gif,image/webp" multiple>
                            <div class="form-text">يمكنك رفع حتى 5 صور بحجم أقصى 2 ميجابايت للصورة الواحدة. سيتم ضغط الصور الكبيرة تلقائياً. يمكنك إضافة صور متعددة في مرات منفصلة.</div>
                            @error('images')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            @error('images.*')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="mt-3" id="images-preview-container"></div>
                        </div>

                        @php
                            // التصنيفات الرئيسية والفرعية
                            $mainCategories = [
                                'vehicles' => [
                                    'name' => 'مركبات',
                                    'icon' => '🚗',
                                    'subcategories' => [
                                        'new_cars' => 'سيارات جديدة',
                                        'used_cars' => 'سيارات مستعملة',
                                        'motorcycles' => 'دراجات نارية',
                                        'car_parts' => 'قطع غيار وإكسسوارات'
                                    ]
                                ],
                                'realestate' => [
                                    'name' => 'عقارات',
                                    'icon' => '🏠',
                                    'subcategories' => [
                                        'apartments_sale' => 'شقق للبيع',
                                        'apartments_rent' => 'شقق للإيجار',
                                        'villas' => 'فلل ومنازل',
                                        'lands' => 'أراضي للبيع'
                                    ]
                                ],
                                'animals' => [
                                    'name' => 'مواشي وحيوانات',
                                    'icon' => '🐾',
                                    'subcategories' => [
                                        'sheep' => 'أغنام',
                                        'cows' => 'أبقار',
                                        'camels' => 'إبل',
                                        'birds' => 'طيور زينة',
                                        'pets' => 'كلاب وقطط'
                                    ]
                                ],
                                'electronics' => [
                                    'name' => 'الكترونيات وتقنية',
                                    'icon' => '💻',
                                    'subcategories' => [
                                        'phones' => 'جوالات',
                                        'computers' => 'كمبيوترات',
                                        'tvs' => 'شاشات وتلفزيونات',
                                        'cameras' => 'كاميرات مراقبة'
                                    ]
                                ],
                                'jobs' => [
                                    'name' => 'وظائف وأعمال',
                                    'icon' => '💼',
                                    'subcategories' => [
                                        'admin_jobs' => 'وظائف إدارية',
                                        'tech_jobs' => 'وظائف فنية وهندسية',
                                        'sales_jobs' => 'وظائف مبيعات وتسويق',
                                        'freelance' => 'أعمال حرة ومقاولات'
                                    ]
                                ],
                                'services' => [
                                    'name' => 'خدمات عامة',
                                    'icon' => '🛠',
                                    'subcategories' => [
                                        'moving' => 'خدمات نقل عفش',
                                        'car_maintenance' => 'صيانة سيارات',
                                        'cleaning' => 'تنظيف منازل',
                                        'construction' => 'خدمات بناء وصيانة مباني'
                                    ]
                                ],
                                'furniture' => [
                                    'name' => 'أثاث ومستلزمات المنزل',
                                    'icon' => '🛋',
                                    'subcategories' => [
                                        'bedrooms' => 'غرف نوم',
                                        'kitchens' => 'مطابخ',
                                        'office_furniture' => 'أثاث مكتبي',
                                        'home_appliances' => 'أدوات منزلية وكهربائية'
                                    ]
                                ],
                                'hobbies' => [
                                    'name' => 'هوايات وترفيه',
                                    'icon' => '🎮',
                                    'subcategories' => [
                                        'sports' => 'أدوات رياضية',
                                        'games' => 'ألعاب إلكترونية',
                                        'books' => 'كتب ومجلات',
                                        'music' => 'آلات موسيقية'
                                    ]
                                ],
                                'fashion' => [
                                    'name' => 'ملابس وأزياء',
                                    'icon' => '👕',
                                    'subcategories' => [
                                        'men_clothes' => 'ملابس رجالية',
                                        'women_clothes' => 'ملابس نسائية',
                                        'shoes_bags' => 'أحذية وحقائب',
                                        'watches' => 'ساعات واكسسوارات'
                                    ]
                                ],
                                'other' => [
                                    'name' => 'أخرى',
                                    'icon' => '📦',
                                    'subcategories' => [
                                        'medical' => 'مستلزمات طبية',
                                        'gifts' => 'تحف وهدايا',
                                        'events' => 'مستلزمات مناسبات'
                                    ]
                                ]
                            ];
                        @endphp

                        <div class="mb-3">
                            <label for="category" class="form-label">الفئة الرئيسية</label>
                            <select name="category" id="category" class="form-select" required onchange="updateSubcategories()">
                                <option value="" disabled selected>اختر الفئة الرئيسية</option>
                                @foreach($mainCategories as $key => $category)
                                    <option value="{{ $key }}" {{ old('category') == $key ? 'selected' : '' }}>
                                        {{ $category['icon'] }} {{ $category['name'] }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="subcategory" class="form-label">الفئة الفرعية</label>
                            <select name="subcategory" id="subcategory" class="form-select">
                                <option value="" disabled selected>اختر الفئة الفرعية</option>
                                <!-- سيتم تحديث الخيارات بواسطة JavaScript -->
                            </select>
                        </div>

                        <script>
                            // تعريف التصنيفات الفرعية كمتغير JavaScript
                            const subcategories = @json($mainCategories);

                            // تحديث التصنيفات الفرعية عند تغيير التصنيف الرئيسي
                            function updateSubcategories() {
                                const categorySelect = document.getElementById('category');
                                const subcategorySelect = document.getElementById('subcategory');
                                const selectedCategory = categorySelect.value;

                                // إفراغ قائمة التصنيفات الفرعية
                                subcategorySelect.innerHTML = '<option value="" disabled selected>اختر الفئة الفرعية</option>';

                                // إذا تم اختيار تصنيف رئيسي
                                if (selectedCategory && subcategories[selectedCategory]) {
                                    // إضافة التصنيفات الفرعية للتصنيف المختار
                                    const subCats = subcategories[selectedCategory].subcategories;
                                    for (const [key, value] of Object.entries(subCats)) {
                                        const option = document.createElement('option');
                                        option.value = key;
                                        option.textContent = value;

                                        // تحديد القيمة المحفوظة مسبقًا إن وجدت
                                        if (key === '{{ old('subcategory') }}') {
                                            option.selected = true;
                                        }

                                        subcategorySelect.appendChild(option);
                                    }
                                }
                            }

                            // تنفيذ الدالة عند تحميل الصفحة لتحديث التصنيفات الفرعية بناءً على القيمة المحفوظة
                            document.addEventListener('DOMContentLoaded', updateSubcategories);
                        </script>

                        <div class="mb-3">
                            <label for="location" class="form-label">الموقع</label>
                            <div class="input-group">
                                <input type="text" name="location" id="location" class="form-control @error('location') is-invalid @enderror" value="{{ old('location') }}">
                                <button type="button" class="btn btn-primary" id="get-location-btn">
                                    <i class="fas fa-map-marker-alt"></i> تحديد موقعي
                                </button>
                                <button type="button" class="btn btn-outline-primary" id="open-map-btn">
                                    <i class="fas fa-map"></i> اختيار من الخريطة
                                </button>
                            </div>
                            <div class="form-text" id="location-status"></div>
                            @error('location')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                            @error('latitude')
                                <div class="invalid-feedback d-block">خطأ في الإحداثيات: {{ $message }}</div>
                            @enderror
                            @error('longitude')
                                <div class="invalid-feedback d-block">خطأ في الإحداثيات: {{ $message }}</div>
                            @enderror
                            <!-- حقول مخفية للإحداثيات -->
                            <input type="hidden" name="latitude" id="latitude" value="{{ old('latitude') }}">
                            <input type="hidden" name="longitude" id="longitude" value="{{ old('longitude') }}">
                        </div>

                        <div class="mb-3">
                            <label for="price" class="form-label">السعر</label>
                            <input type="number" name="price" id="price" class="form-control @error('price') is-invalid @enderror" value="{{ old('price') }}" step="0.01" min="0">
                            @error('price')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="text" name="phone" id="phone" class="form-control @error('phone') is-invalid @enderror" value="{{ old('phone') }}" placeholder="مثال: 0501234567">
                            @error('phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="whatsapp" class="form-label">رقم الواتساب</label>
                            <input type="text" name="whatsapp" id="whatsapp" class="form-control @error('whatsapp') is-invalid @enderror" value="{{ old('whatsapp') }}" placeholder="مثال: 0501234567">
                            @error('whatsapp')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" name="email" id="email" class="form-control @error('email') is-invalid @enderror" value="{{ old('email') }}" placeholder="مثال: <EMAIL>">
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" name="terms" id="terms" class="form-check-input" required>
                            <label for="terms" class="form-check-label">أوافق على الشروط والأحكام</label>
                        </div>

                        @php
    $userPoints = auth()->user()->points ?? 0; // أو استبدل `points` باسم العمود الصحيح

    // حساب عدد الإعلانات المميزة النشطة للمستخدم
    $activeFeaturedAdsCount = \App\Models\Ad::where('user_id', auth()->id())
        ->where('is_featured', true)
        ->where(function($query) {
            $query->whereNull('featured_until')
                ->orWhere('featured_until', '>', now());
        })
        ->count();

    // الحد الأقصى للإعلانات المميزة (يمكن تعديله حسب الحاجة)
    $maxFeaturedAds = 3;

    // التحقق مما إذا وصل المستخدم للحد الأقصى
    $maxFeaturedReached = $activeFeaturedAdsCount >= $maxFeaturedAds;
@endphp

@if ($userPoints > 0)
    <div class="form-group mt-3">
        <label>
            <input type="checkbox" name="is_featured" id="is_featured" {{ old('is_featured') ? 'checked' : '' }} onchange="toggleDaysInput(this)" {{ $maxFeaturedReached ? 'disabled' : '' }}>
            إعلان مميز
        </label>

        <div id="days-input" class="mt-2" style="display: none;">
            <label for="featured_days">مدة التثبيت (أيام):</label>
            <input type="number" name="featured_days" id="featured_days" min="1" max="{{ $userPoints }}" value="{{ old('featured_days', 1) }}">
            <small class="text-muted">لديك {{ $userPoints }} نقطة</small>
        </div>

        @if($maxFeaturedReached)
            <div class="alert alert-warning mt-2">
                <i class="fas fa-exclamation-triangle me-1"></i> لقد وصلت للحد الأقصى من الإعلانات المميزة ({{ $maxFeaturedAds }}). يرجى إلغاء تمييز أحد إعلاناتك الأخرى أولاً أو الانتظار حتى انتهاء فترة التمييز.
            </div>
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const featuredCheckbox = document.getElementById('is_featured');
                    if (featuredCheckbox) {
                        featuredCheckbox.addEventListener('change', function() {
                            if (this.checked) {
                                alert('لقد وصلت للحد الأقصى من الإعلانات المميزة ({{ $maxFeaturedAds }}). يرجى إلغاء تمييز أحد إعلاناتك الأخرى أولاً أو الانتظار حتى انتهاء فترة التمييز.');
                                this.checked = false;
                                document.getElementById('days-input').style.display = 'none';
                            }
                        });
                    }
                });
            </script>
        @endif
    </div>

    <script>
        function toggleDaysInput(checkbox) {
            const daysInput = document.getElementById('days-input');
            daysInput.style.display = checkbox.checked ? 'block' : 'none';
        }
    </script>
@else
    <div class="alert alert-warning mt-3">
        ليس لديك نقاط كافية لتثبيت الإعلان كـ "مميز". <a href="{{ route('points.buy') }}">اشتر نقاط</a>
    </div>
@endif

    <script>
        // معاينة الصور قبل الرفع
        document.addEventListener('DOMContentLoaded', function() {
            // معاينة الصور المتعددة
            const imagesInput = document.getElementById('images');
            const imagesPreviewContainer = document.getElementById('images-preview-container');
            const adForm = document.getElementById('ad-form');

            // متغير لتخزين الصور المختارة
            let selectedFiles = [];

            // مستمع حدث إرسال النموذج
            if (adForm) {
                adForm.addEventListener('submit', function(e) {
                    console.log('🚀 بدء إرسال النموذج');
                    console.log('📸 عدد الصور المختارة:', selectedFiles.length);
                    console.log('📁 ملفات في input:', imagesInput.files.length);

                    // التأكد من تحديث حقل الإدخال قبل الإرسال
                    if (selectedFiles.length > 0) {
                        updateFileInput();
                        console.log('✅ تم تحديث حقل الإدخال قبل الإرسال');
                        console.log('📊 ملفات نهائية في input:', imagesInput.files.length);

                        // طباعة تفاصيل كل ملف
                        for (let i = 0; i < imagesInput.files.length; i++) {
                            const file = imagesInput.files[i];
                            console.log(`📷 ملف ${i + 1}:`, {
                                name: file.name,
                                size: file.size,
                                type: file.type
                            });
                        }
                    } else {
                        console.log('⚠️ لا توجد صور مختارة');
                    }
                });
            }

            if (imagesInput) {
                imagesInput.addEventListener('change', function() {
                    // إضافة الصور الجديدة إلى المصفوفة الموجودة
                    const newFiles = Array.from(this.files);

                    // التحقق من العدد الإجمالي للصور
                    const totalFiles = selectedFiles.length + newFiles.length;
                    if (totalFiles > 5) {
                        alert(`يمكنك اختيار 5 صور كحد أقصى. لديك حالياً ${selectedFiles.length} صورة، وتحاول إضافة ${newFiles.length} صورة.`);
                        this.value = ''; // إفراغ حقل الإدخال
                        return;
                    }

                    // التحقق من كل ملف جديد
                    for (let i = 0; i < newFiles.length; i++) {
                        const file = newFiles[i];

                        // قائمة تنسيقات الصور المدعومة
                        const supportedFormats = [
                            'image/jpeg',
                            'image/jpg',
                            'image/png',
                            'image/gif',
                            'image/webp'
                            // ملاحظة: تم استبعاد image/webm لأنه غير مدعوم في معالجة الصور
                        ];

                        // التحقق من نوع الملف
                        if (!supportedFormats.includes(file.type.toLowerCase())) {
                            alert('تنسيق الصورة غير مدعوم. الرجاء استخدام تنسيقات: JPEG, PNG, GIF, WebP فقط');
                            this.value = '';
                            return;
                        }

                        // التحقق من حجم الملف (2MB)
                        if (file.size > 2 * 1024 * 1024) {
                            alert('حجم الصورة يجب أن يكون أقل من 2 ميجابايت');
                            this.value = '';
                            return;
                        }
                    }

                    // إضافة الصور الجديدة إلى المصفوفة
                    selectedFiles = selectedFiles.concat(newFiles);

                    // إعادة عرض جميع الصور
                    displaySelectedImages();

                    // إفراغ حقل الإدخال للسماح بإضافة صور أخرى
                    this.value = '';
                });
            }

            // دالة لعرض الصور المختارة
            function displaySelectedImages() {
                // إزالة الرسائل الإرشادية السابقة
                const existingInfos = imagesPreviewContainer.querySelectorAll('.alert');
                existingInfos.forEach(info => info.remove());

                // إزالة جميع الصور الموجودة
                const existingImages = imagesPreviewContainer.querySelectorAll('.image-preview-item');
                existingImages.forEach(img => img.remove());

                // إضافة أو إزالة class للحاوية
                if (selectedFiles.length > 0) {
                    imagesPreviewContainer.classList.add('has-images');
                } else {
                    imagesPreviewContainer.classList.remove('has-images');
                }

                // عرض كل صورة
                selectedFiles.forEach((file, index) => {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        const imgContainer = document.createElement('div');
                        imgContainer.className = 'image-preview-item';
                        imgContainer.dataset.index = index;

                        const img = document.createElement('img');
                        img.src = e.target.result;
                        img.alt = `معاينة الصورة ${index + 1}`;

                        // إضافة زر حذف
                        const deleteBtn = document.createElement('button');
                        deleteBtn.type = 'button';
                        deleteBtn.className = 'image-delete-btn';
                        deleteBtn.innerHTML = '×';
                        deleteBtn.title = 'حذف الصورة';
                        deleteBtn.onclick = function() {
                            removeImage(index);
                        };

                        imgContainer.appendChild(img);
                        imgContainer.appendChild(deleteBtn);
                        imagesPreviewContainer.appendChild(imgContainer);
                    }

                    reader.readAsDataURL(file);
                });

                // إضافة رسالة إرشادية
                if (selectedFiles.length > 0) {
                    const infoDiv = document.createElement('div');
                    infoDiv.className = 'alert alert-info mt-2';
                    infoDiv.innerHTML = `<i class="fas fa-info-circle"></i> تم اختيار ${selectedFiles.length} صورة من أصل 5. يمكنك إضافة ${5 - selectedFiles.length} صورة أخرى.`;
                    imagesPreviewContainer.appendChild(infoDiv);
                }

                // تحديث حقل الإدخال بالصور المختارة
                updateFileInput();
            }

            // دالة لحذف صورة معينة
            function removeImage(index) {
                selectedFiles.splice(index, 1);
                displaySelectedImages();
            }

            // دالة لتحديث حقل الإدخال بالصور المختارة
            function updateFileInput() {
                if (selectedFiles.length === 0) {
                    return;
                }

                try {
                    // إنشاء DataTransfer جديد لتحديث files
                    const dt = new DataTransfer();
                    selectedFiles.forEach(file => {
                        dt.items.add(file);
                    });
                    imagesInput.files = dt.files;

                    console.log('تم تحديث حقل الإدخال بـ', selectedFiles.length, 'صورة');
                    console.log('حقل الإدخال يحتوي على', imagesInput.files.length, 'ملف');
                } catch (error) {
                    console.error('خطأ في تحديث حقل الإدخال:', error);
                }
            }

            // تحديد الموقع الجغرافي
            const getLocationBtn = document.getElementById('get-location-btn');
            const locationStatus = document.getElementById('location-status');
            const locationInput = document.getElementById('location');
            const latitudeInput = document.getElementById('latitude');
            const longitudeInput = document.getElementById('longitude');

            if (getLocationBtn) {
                getLocationBtn.addEventListener('click', function() {
                    console.log('بدء تحديد الموقع...');

                    // تعطيل الزر أثناء التحميل
                    getLocationBtn.disabled = true;
                    getLocationBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديد...';

                    // التحقق من دعم المتصفح لتحديد الموقع
                    if (!navigator.geolocation) {
                        locationStatus.textContent = 'المتصفح لا يدعم تحديد الموقع الجغرافي.';
                        locationStatus.className = 'form-text text-danger';

                        // إعادة تفعيل الزر
                        getLocationBtn.disabled = false;
                        getLocationBtn.innerHTML = '<i class="fas fa-map-marker-alt"></i> تحديد موقعي';
                        return;
                    }

                    if (navigator.geolocation) {
                        locationStatus.textContent = 'جاري تحديد الموقع...';
                        locationStatus.className = 'form-text text-primary';

                        navigator.geolocation.getCurrentPosition(
                            // نجاح
                            function(position) {
                                const latitude = position.coords.latitude;
                                const longitude = position.coords.longitude;

                                // تخزين الإحداثيات في الحقول المخفية
                                latitudeInput.value = latitude.toFixed(6);
                                longitudeInput.value = longitude.toFixed(6);

                                // الحصول على اسم الموقع باستخدام Reverse Geocoding
                                reverseGeocode(latitude, longitude);
                            },
                            // فشل
                            function(error) {
                                let errorMessage = '';
                                switch(error.code) {
                                    case error.PERMISSION_DENIED:
                                        errorMessage = 'تم رفض الوصول إلى الموقع الجغرافي. يرجى السماح بالوصول للموقع في إعدادات المتصفح.';
                                        break;
                                    case error.POSITION_UNAVAILABLE:
                                        errorMessage = 'معلومات الموقع غير متاحة. تأكد من تشغيل GPS.';
                                        break;
                                    case error.TIMEOUT:
                                        errorMessage = 'انتهت مهلة طلب الموقع. يرجى المحاولة مرة أخرى.';
                                        break;
                                    default:
                                        errorMessage = 'حدث خطأ في تحديد الموقع. يرجى المحاولة مرة أخرى.';
                                        break;
                                }
                                locationStatus.textContent = errorMessage;
                                locationStatus.className = 'form-text text-danger';

                                // إعادة تفعيل الزر
                                getLocationBtn.disabled = false;
                                getLocationBtn.innerHTML = '<i class="fas fa-map-marker-alt"></i> تحديد موقعي';
                            },
                            // خيارات
                            {
                                enableHighAccuracy: true,
                                timeout: 15000,
                                maximumAge: 300000 // 5 دقائق
                            }
                        );
                    } else {
                        locationStatus.textContent = 'المتصفح لا يدعم تحديد الموقع الجغرافي.';
                        locationStatus.className = 'form-text text-danger';

                        // إعادة تفعيل الزر
                        getLocationBtn.disabled = false;
                        getLocationBtn.innerHTML = '<i class="fas fa-map-marker-alt"></i> تحديد موقعي';
                    }
                });
            }

            // دالة للحصول على اسم الموقع من الإحداثيات
            function reverseGeocode(lat, lng) {
                // استخدام خدمة Nominatim OpenStreetMap للحصول على اسم الموقع
                fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&accept-language=ar&zoom=10`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data && data.address) {
                            // محاولة الحصول على اسم المدينة أو المنطقة
                            const city = data.address.city || data.address.town || data.address.village || data.address.county || data.address.state || '';
                            const country = data.address.country || '';

                            if (city && country) {
                                locationInput.value = `${city}, ${country}`;
                                locationStatus.textContent = 'تم تحديد الموقع بنجاح!';
                                locationStatus.className = 'form-text text-success';
                            } else if (country) {
                                locationInput.value = country;
                                locationStatus.textContent = 'تم تحديد البلد فقط.';
                                locationStatus.className = 'form-text text-warning';
                            } else {
                                locationInput.value = `${lat.toFixed(4)}, ${lng.toFixed(4)}`;
                                locationStatus.textContent = 'تم حفظ الإحداثيات. يمكنك كتابة اسم الموقع يدوياً.';
                                locationStatus.className = 'form-text text-info';
                            }
                        } else {
                            locationInput.value = `${lat.toFixed(4)}, ${lng.toFixed(4)}`;
                            locationStatus.textContent = 'تم حفظ الإحداثيات. يمكنك كتابة اسم الموقع يدوياً.';
                            locationStatus.className = 'form-text text-info';
                        }

                        // إعادة تفعيل الزر
                        getLocationBtn.disabled = false;
                        getLocationBtn.innerHTML = '<i class="fas fa-map-marker-alt"></i> تحديد موقعي';
                    })
                    .catch(error => {
                        console.error('Error in reverse geocoding:', error);
                        locationInput.value = `${lat.toFixed(4)}, ${lng.toFixed(4)}`;
                        locationStatus.textContent = 'تم حفظ الإحداثيات. حدث خطأ في الحصول على اسم الموقع.';
                        locationStatus.className = 'form-text text-warning';

                        // إعادة تفعيل الزر
                        getLocationBtn.disabled = false;
                        getLocationBtn.innerHTML = '<i class="fas fa-map-marker-alt"></i> تحديد موقعي';
                    });
            }

            // تهيئة الخريطة
            let map = null;
            let marker = null;
            let selectedLat = null;
            let selectedLng = null;

            // عناصر نافذة الخريطة
            const mapContainer = document.getElementById('map-container');
            const mapCloseBtn = document.getElementById('map-close');
            const mapConfirmBtn = document.getElementById('map-confirm');
            const openMapBtn = document.getElementById('open-map-btn');
            const selectedLatSpan = document.getElementById('selected-lat');
            const selectedLngSpan = document.getElementById('selected-lng');

            // فتح نافذة الخريطة
            if (openMapBtn) {
                openMapBtn.addEventListener('click', function() {
                    console.log('فتح الخريطة...');

                    // التحقق من وجود مكتبة Leaflet
                    if (typeof L === 'undefined') {
                        alert('خطأ: مكتبة الخرائط غير محملة. يرجى إعادة تحميل الصفحة.');
                        return;
                    }

                    mapContainer.style.display = 'flex';

                    // تهيئة الخريطة إذا لم تكن موجودة بالفعل
                    if (!map) {
                        try {
                            // تحديد مركز الخريطة (الرياض كمركز افتراضي)
                            const defaultLat = 24.7136;
                            const defaultLng = 46.6753;

                            // استخدام الإحداثيات المخزنة إذا كانت موجودة
                            const initialLat = latitudeInput.value ? parseFloat(latitudeInput.value) : defaultLat;
                            const initialLng = longitudeInput.value ? parseFloat(longitudeInput.value) : defaultLng;

                            console.log('إنشاء الخريطة في الإحداثيات:', initialLat, initialLng);

                            // إنشاء الخريطة
                            map = L.map('map', {
                                center: [initialLat, initialLng],
                                zoom: 13,
                                zoomControl: true,
                                scrollWheelZoom: true
                            });

                            // إضافة طبقة الخريطة
                            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                                maxZoom: 19,
                                minZoom: 3
                            }).addTo(map);

                            // التأكد من تحميل الخريطة بشكل صحيح
                            map.whenReady(function() {
                                console.log('الخريطة جاهزة للاستخدام');
                                setTimeout(() => {
                                    map.invalidateSize();
                                }, 100);
                            });

                            // إضافة علامة إذا كانت الإحداثيات موجودة
                            if (latitudeInput.value && longitudeInput.value) {
                                marker = L.marker([initialLat, initialLng], {draggable: true}).addTo(map);
                                selectedLat = initialLat;
                                selectedLng = initialLng;
                                if (selectedLatSpan) selectedLatSpan.textContent = initialLat.toFixed(6);
                                if (selectedLngSpan) selectedLngSpan.textContent = initialLng.toFixed(6);

                                // تحديث الإحداثيات عند سحب العلامة
                                marker.on('dragend', function(event) {
                                    const position = marker.getLatLng();
                                    selectedLat = position.lat;
                                    selectedLng = position.lng;
                                    if (selectedLatSpan) selectedLatSpan.textContent = selectedLat.toFixed(6);
                                    if (selectedLngSpan) selectedLngSpan.textContent = selectedLng.toFixed(6);
                                });
                            }

                            // إضافة حدث النقر على الخريطة
                            map.on('click', function(e) {
                                console.log('تم النقر على الخريطة:', e.latlng);

                                // إزالة العلامة السابقة إذا وجدت
                                if (marker) {
                                    map.removeLayer(marker);
                                }

                                // إضافة علامة جديدة
                                marker = L.marker(e.latlng, {draggable: true}).addTo(map);
                                selectedLat = e.latlng.lat;
                                selectedLng = e.latlng.lng;
                                if (selectedLatSpan) selectedLatSpan.textContent = selectedLat.toFixed(6);
                                if (selectedLngSpan) selectedLngSpan.textContent = selectedLng.toFixed(6);

                                // تحديث الإحداثيات عند سحب العلامة
                                marker.on('dragend', function(event) {
                                    const position = marker.getLatLng();
                                    selectedLat = position.lat;
                                    selectedLng = position.lng;
                                    if (selectedLatSpan) selectedLatSpan.textContent = selectedLat.toFixed(6);
                                    if (selectedLngSpan) selectedLngSpan.textContent = selectedLng.toFixed(6);
                                });
                            });

                            console.log('تم إنشاء الخريطة بنجاح');
                        } catch (error) {
                            console.error('خطأ في إنشاء الخريطة:', error);
                            alert('حدث خطأ في تحميل الخريطة. يرجى المحاولة مرة أخرى.');
                            mapContainer.style.display = 'none';
                            return;
                        }
                    } else {
                        // تحديث حجم الخريطة عند إعادة فتحها
                        setTimeout(() => {
                            try {
                                map.invalidateSize();
                                console.log('تم تحديث حجم الخريطة');
                            } catch (error) {
                                console.error('خطأ في تحديث حجم الخريطة:', error);
                            }
                        }, 200);
                    }
                });
            }

            // إغلاق نافذة الخريطة
            if (mapCloseBtn) {
                mapCloseBtn.addEventListener('click', function() {
                    console.log('إغلاق الخريطة');
                    mapContainer.style.display = 'none';
                });
            }

            // تأكيد اختيار الموقع
            if (mapConfirmBtn) {
                mapConfirmBtn.addEventListener('click', function() {
                    console.log('تأكيد الموقع:', selectedLat, selectedLng);

                    if (selectedLat && selectedLng) {
                        // تخزين الإحداثيات في الحقول المخفية
                        latitudeInput.value = selectedLat.toFixed(6);
                        longitudeInput.value = selectedLng.toFixed(6);

                        // الحصول على اسم الموقع
                        reverseGeocode(selectedLat, selectedLng);

                        // إغلاق نافذة الخريطة
                        mapContainer.style.display = 'none';

                        // تحديث حالة الموقع
                        locationStatus.textContent = 'تم اختيار الموقع من الخريطة!';
                        locationStatus.className = 'form-text text-success';

                        console.log('تم حفظ الموقع بنجاح');
                    } else {
                        alert('الرجاء اختيار موقع على الخريطة أولاً');
                    }
                });
            }

            // إغلاق نافذة الخريطة عند النقر خارجها
            if (mapContainer) {
                mapContainer.addEventListener('click', function(e) {
                    if (e.target === mapContainer) {
                        console.log('إغلاق الخريطة بالنقر خارجها');
                        mapContainer.style.display = 'none';
                    }
                });
            }
        });
    </script>

                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary btn-lg px-5" @if($userAdsCount >= $maxAdsAllowed) disabled @endif>
                                <i class="fas fa-check ms-2"></i>
                                @if($userAdsCount >= $maxAdsAllowed)
                                    وصلت للحد الأقصى
                                @else
                                    نشر الإعلان
                                @endif
                            </button>
                            @if($userAdsCount >= $maxAdsAllowed)
                                <div class="mt-2">
                                    <small class="text-muted">يجب حذف أحد إعلاناتك الحالية لإضافة إعلان جديد</small>
                                </div>
                            @endif
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة الخريطة -->
    <div id="map-container">
        <div id="map-modal">
            <div id="map-header">
                <h3>اختر موقعًا على الخريطة</h3>
                <button id="map-close">&times;</button>
            </div>
            <div id="map"></div>
            <div id="map-footer">
                <p id="map-coordinates">الإحداثيات: <span id="selected-lat">-</span>, <span id="selected-lng">-</span></p>
                <button id="map-confirm">تأكيد الموقع</button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Smart Contact Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const phoneInput = document.getElementById('phone');
            const whatsappInput = document.getElementById('whatsapp');

            // دالة للتحقق من الرقم وإظهار تنبيه واتساب
            function checkForWhatsAppRedirect(input, inputType) {
                if (!input) return;

                input.addEventListener('input', function() {
                    const value = this.value.trim();

                    // التحقق من بداية الرقم بـ 05 أو 966
                    if (value.startsWith('05') || value.startsWith('966')) {
                        // إنشاء تنبيه ديناميكي
                        showWhatsAppAlert(value, inputType, this);
                    }
                });
            }

            // دالة إظهار تنبيه واتساب
            function showWhatsAppAlert(phoneNumber, inputType, inputElement) {
                // إزالة أي تنبيه سابق
                const existingAlert = document.querySelector('.whatsapp-alert');
                if (existingAlert) {
                    existingAlert.remove();
                }

                // تنسيق الرقم للواتساب
                let whatsappNumber = phoneNumber;
                if (whatsappNumber.startsWith('05')) {
                    whatsappNumber = '966' + whatsappNumber.substring(1);
                }

                // إنشاء عنصر التنبيه
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-info whatsapp-alert mt-2';
                alertDiv.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="fab fa-whatsapp text-success me-2" style="font-size: 1.2rem;"></i>
                        <div class="flex-grow-1">
                            <strong>تم اكتشاف رقم سعودي!</strong>
                            <br>
                            <small>هل تريد فتح واتساب مباشرة؟</small>
                        </div>
                        <div>
                            <button type="button" class="btn btn-success btn-sm me-2" onclick="openWhatsApp('${whatsappNumber}')">
                                <i class="fab fa-whatsapp me-1"></i>
                                فتح واتساب
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="dismissAlert(this)">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                `;

                // إدراج التنبيه بعد حقل الإدخال
                inputElement.parentNode.insertBefore(alertDiv, inputElement.nextSibling);

                // إخفاء التنبيه تلقائياً بعد 10 ثوان
                setTimeout(() => {
                    if (alertDiv && alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 10000);
            }

            // تطبيق التحقق على حقول الهاتف والواتساب
            checkForWhatsAppRedirect(phoneInput, 'phone');
            checkForWhatsAppRedirect(whatsappInput, 'whatsapp');
        });

        // دالة فتح واتساب
        function openWhatsApp(phoneNumber) {
            const whatsappUrl = `https://wa.me/${phoneNumber}`;
            window.open(whatsappUrl, '_blank');
        }

        // دالة إغلاق التنبيه
        function dismissAlert(button) {
            const alert = button.closest('.whatsapp-alert');
            if (alert) {
                alert.remove();
            }
        }
    </script>
</body>
</html>
