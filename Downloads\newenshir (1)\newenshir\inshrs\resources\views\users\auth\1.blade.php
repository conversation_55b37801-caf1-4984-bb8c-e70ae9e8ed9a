<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Login Page for User Access" />
    <meta name="keywords" content="login, sign in, authentication, user access" />
    <title>Login - AI Job Matcher</title>
    <style>
        /* إعدادات عامة للصفحة */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #4e73df, #1c6cd7);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: #fff;
        }

        /* الحاوية الرئيسية */
        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
        }

        /* صندوق تسجيل الدخول */
        .login-box {
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        /* عنوان الصفحة */
        .login-box h2 {
            font-size: 28px;
            margin-bottom: 20px;
            color: #333;
        }

        /* الحقول */
        .textbox {
            margin-bottom: 20px;
            position: relative;
        }

        .textbox input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ccc;
            border-radius: 4px;
            font-size: 16px;
            outline: none;
            transition: border 0.3s;
        }

        .textbox input:focus {
            border-color: #4e73df;
        }

        /* تفعيل تذكرني */
        .remember-me {
            text-align: left;
            margin-bottom: 20px;
        }

        .remember-me label {
            font-size: 14px;
            color: #555;
        }

        /* زر تسجيل الدخول */
        .btn-login {
            width: 100%;
            padding: 15px;
            background: #4e73df;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 18px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .btn-login:hover {
            background: #365bb2;
        }

        /* الروابط أسفل النموذج */
        .links {
            margin-top: 20px;
        }

        .links a {
            font-size: 14px;
            color: #4e73df;
            text-decoration: none;
            margin: 5px;
            display: inline-block;
        }

        .links a:hover {
            text-decoration: underline;
        }
    </style>
</head>

<body>
    <div class="login-container">
        <div class="login-box">
            <h2>Login</h2>
            <form   action="{{ route('main.index') }}" method="POST">
                <div class="textbox">
                    <input type="email" placeholder="Email" name="email" required />
                </div>
                <div class="textbox">
                    <input type="password" placeholder="Password" name="password" required />
                </div>
                <div class="remember-me">
                    <input type="checkbox" name="remember" id="remember">
                    <label for="remember">Remember me</label>
                </div>
                <button type="submit" class="btn-login">Login</button>
                <div class="links">
                    <a href="/forgot-password">Forgot Password?</a>
                    <a href="/signup">Sign Up</a>
                </div>
            </form>
        </div>
    </div>
</body>

</html>
