<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات المنشورات المحفوظة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --light-bg: #f3f4f6;
            --border-color: #e5e7eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
        }

        body {
            background-color: var(--light-bg);
            padding: 10px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background-color: var(--primary-color);
            color: white;
            padding: 20px;
            text-align: right;
        }

        .header h1 {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .settings-nav {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            padding: 15px;
            background: #f8fafc;
            border-bottom: 1px solid var(--border-color);
        }

        .nav-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            background: white;
            color: var(--primary-color);
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-btn.active {
            background: var(--primary-color);
            color: white;
        }

        .settings-section {
            padding: 20px;
            display: none;
        }

        .settings-section.active {
            display: block;
        }

        .setting-group {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .setting-group h3 {
            color: var(--secondary-color);
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .setting-item {
            margin-bottom: 20px;
        }

        .setting-item label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .setting-item select,
        .setting-item input[type="text"],
        .setting-item textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            margin-bottom: 10px;
        }

        .tag-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }

        .tag {
            background: var(--light-bg);
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .tag button {
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            font-size: 1rem;
        }

        .notification-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            right: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--success-color);
        }

        input:checked + .slider:before {
            transform: translateX(-26px);
        }

        .btn-save {
            background-color: var(--success-color);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            width: 100%;
            margin-top: 20px;
        }

        /* تصميم متجاوب للجوال */
        @media (max-width: 768px) {
            .container {
                margin: 0;
                border-radius: 0;
            }

            .header h1 {
                font-size: 1.2rem;
            }

            .settings-nav {
                flex-direction: column;
            }

            .nav-btn {
                width: 100%;
                text-align: right;
            }

            .setting-group {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>إعدادات المنشورات المحفوظة</h1>
            <p>تخصيص طريقة عرض وتنظيم المنشورات المحفوظة</p>
        </header>

        <nav class="settings-nav">
            <button class="nav-btn active" onclick="showSection('general')">إعدادات عامة</button>
            <button class="nav-btn" onclick="showSection('organization')">التنظيم والتصنيف</button>
            <button class="nav-btn" onclick="showSection('notifications')">الإشعارات والتحديثات</button>
            <button class="nav-btn" onclick="showSection('sharing')">المشاركة والتعاون</button>
        </nav>

        <div id="general" class="settings-section active">
            <div class="setting-group">
                <h3>إعدادات العرض</h3>
                <div class="setting-item">
                    <label>طريقة عرض المنشورات</label>
                    <select>
                        <option>عرض القائمة</option>
                        <option>عرض البطاقات</option>
                        <option>عرض مفصل</option>
                    </select>
                </div>
                <div class="setting-item">
                    <label>ترتيب حسب</label>
                    <select>
                        <option>تاريخ الحفظ (الأحدث أولاً)</option>
                        <option>تاريخ النشر</option>
                        <option>الأهمية</option>
                        <option>العنوان</option>
                    </select>
                </div>
            </div>
        </div>

        <div id="organization" class="settings-section">
            <div class="setting-group">
                <h3>التصنيفات والوسوم</h3>
                <div class="setting-item">
                    <label>إضافة وسم جديد</label>
                    <input type="text" placeholder="أدخل الوسم هنا">
                    <div class="tag-container">
                        <span class="tag">تقنية المعلومات <button>×</button></span>
                        <span class="tag">تطوير الويب <button>×</button></span>
                        <span class="tag">إدارة المشاريع <button>×</button></span>
                    </div>
                </div>
                <div class="setting-item">
                    <label>ملاحظات افتراضية</label>
                    <textarea rows="3" placeholder="أدخل الملاحظات الافتراضية هنا"></textarea>
                </div>
            </div>
        </div>

        <div id="notifications" class="settings-section">
            <div class="setting-group">
                <h3>إعدادات الإشعارات</h3>
                <div class="notification-item">
                    <label class="switch">
                        <input type="checkbox" checked>
                        <span class="slider"></span>
                    </label>
                    <span>تحديثات المنشورات المحفوظة</span>
                </div>
                <div class="notification-item">
                    <label class="switch">
                        <input type="checkbox">
                        <span class="slider"></span>
                    </label>
                    <span>إشعارات المشاركة والتعليقات</span>
                </div>
                <div class="notification-item">
                    <label class="switch">
                        <input type="checkbox" checked>
                        <span class="slider"></span>
                    </label>
                    <span>منشورات مشابهة</span>
                </div>
            </div>
        </div>

        <div id="sharing" class="settings-section">
            <div class="setting-group">
                <h3>إعدادات المشاركة</h3>
                <div class="setting-item">
                    <label>خيارات المشاركة الافتراضية</label>
                    <select>
                        <option>خاص</option>
                        <option>مشاركة مع محددين</option>
                        <option>عام</option>
                    </select>
                </div>
                <div class="setting-item">
                    <label>السماح بالتعليقات</label>
                    <select>
                        <option>نعم، للجميع</option>
                        <option>للمشاركين فقط</option>
                        <option>لا</option>
                    </select>
                </div>
            </div>
        </div>

        <div style="padding: 20px;">
            <button class="btn-save" onclick="saveSettings()">حفظ الإعدادات</button>
        </div>
    </div>

    <script>
        function showSection(sectionId) {
            // إخفاء جميع الأقسام
            document.querySelectorAll('.settings-section').forEach(section => {
                section.classList.remove('active');
            });
            
            // إظهار القسم المحدد
            document.getElementById(sectionId).classList.add('active');
            
            // تحديث حالة الأزرار
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        function saveSettings() {
            // محاكاة حفظ الإعدادات
            alert('تم حفظ الإعدادات بنجاح');
        }
    </script>
</body>
</html>