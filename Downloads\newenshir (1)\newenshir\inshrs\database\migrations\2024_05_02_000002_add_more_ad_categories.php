<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class AddMoreAdCategories extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // إنشاء جدول للفئات إذا لم يكن موجودًا
        if (!Schema::hasTable('categories')) {
            Schema::create('categories', function (Blueprint $table) {
                $table->id();
                $table->string('slug')->unique();
                $table->string('name');
                $table->string('icon')->nullable();
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // إنشاء جدول للفئات الفرعية إذا لم يكن موجودًا
        if (!Schema::hasTable('subcategories')) {
            Schema::create('subcategories', function (Blueprint $table) {
                $table->id();
                $table->foreignId('category_id')->constrained()->onDelete('cascade');
                $table->string('slug')->unique();
                $table->string('name');
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        }

        // إضافة الفئات الرئيسية
        $categories = [
            // الفئات الموجودة مسبقًا
            [
                'slug' => 'vehicles',
                'name' => 'مركبات',
                'icon' => 'fa-car',
            ],
            [
                'slug' => 'realestate',
                'name' => 'عقارات',
                'icon' => 'fa-building',
            ],
            [
                'slug' => 'animals',
                'name' => 'مواشي وحيوانات',
                'icon' => 'fa-paw',
            ],
            // فئات جديدة
            [
                'slug' => 'electronics',
                'name' => 'إلكترونيات',
                'icon' => 'fa-laptop',
            ],
            [
                'slug' => 'furniture',
                'name' => 'أثاث منزلي',
                'icon' => 'fa-couch',
            ],
            [
                'slug' => 'fashion',
                'name' => 'أزياء وملابس',
                'icon' => 'fa-tshirt',
            ],
            [
                'slug' => 'services',
                'name' => 'خدمات',
                'icon' => 'fa-concierge-bell',
            ],
            [
                'slug' => 'jobs',
                'name' => 'وظائف',
                'icon' => 'fa-briefcase',
            ],
            [
                'slug' => 'education',
                'name' => 'تعليم وتدريب',
                'icon' => 'fa-graduation-cap',
            ],
            [
                'slug' => 'health',
                'name' => 'صحة وجمال',
                'icon' => 'fa-heartbeat',
            ],
            [
                'slug' => 'sports',
                'name' => 'رياضة وهوايات',
                'icon' => 'fa-futbol',
            ],
            [
                'slug' => 'food',
                'name' => 'طعام ومأكولات',
                'icon' => 'fa-utensils',
            ],
            [
                'slug' => 'travel',
                'name' => 'سفر وسياحة',
                'icon' => 'fa-plane',
            ],
            [
                'slug' => 'books',
                'name' => 'كتب ومجلات',
                'icon' => 'fa-book',
            ],
            [
                'slug' => 'art',
                'name' => 'فن وتحف',
                'icon' => 'fa-palette',
            ],
            [
                'slug' => 'agriculture',
                'name' => 'زراعة',
                'icon' => 'fa-leaf',
            ],
            [
                'slug' => 'industrial',
                'name' => 'معدات صناعية',
                'icon' => 'fa-industry',
            ],
            [
                'slug' => 'events',
                'name' => 'مناسبات وفعاليات',
                'icon' => 'fa-calendar-alt',
            ],
        ];

        // إدخال الفئات
        foreach ($categories as $category) {
            DB::table('categories')->insertOrIgnore([
                'slug' => $category['slug'],
                'name' => $category['name'],
                'icon' => $category['icon'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // إضافة الفئات الفرعية
        $subcategories = [
            // مركبات
            ['category_slug' => 'vehicles', 'slug' => 'new_cars', 'name' => 'سيارات جديدة'],
            ['category_slug' => 'vehicles', 'slug' => 'used_cars', 'name' => 'سيارات مستعملة'],
            ['category_slug' => 'vehicles', 'slug' => 'motorcycles', 'name' => 'دراجات نارية'],
            ['category_slug' => 'vehicles', 'slug' => 'car_parts', 'name' => 'قطع غيار وإكسسوارات'],
            ['category_slug' => 'vehicles', 'slug' => 'trucks', 'name' => 'شاحنات ومعدات ثقيلة'],
            ['category_slug' => 'vehicles', 'slug' => 'boats', 'name' => 'قوارب ويخوت'],
            ['category_slug' => 'vehicles', 'slug' => 'rental', 'name' => 'تأجير سيارات'],

            // عقارات
            ['category_slug' => 'realestate', 'slug' => 'apartments_sale', 'name' => 'شقق للبيع'],
            ['category_slug' => 'realestate', 'slug' => 'apartments_rent', 'name' => 'شقق للإيجار'],
            ['category_slug' => 'realestate', 'slug' => 'villas', 'name' => 'فلل ومنازل'],
            ['category_slug' => 'realestate', 'slug' => 'lands', 'name' => 'أراضي للبيع'],
            ['category_slug' => 'realestate', 'slug' => 'commercial', 'name' => 'عقارات تجارية'],
            ['category_slug' => 'realestate', 'slug' => 'farms', 'name' => 'مزارع ومزارع سياحية'],
            ['category_slug' => 'realestate', 'slug' => 'chalets', 'name' => 'شاليهات واستراحات'],

            // مواشي وحيوانات
            ['category_slug' => 'animals', 'slug' => 'sheep', 'name' => 'أغنام'],
            ['category_slug' => 'animals', 'slug' => 'cows', 'name' => 'أبقار'],
            ['category_slug' => 'animals', 'slug' => 'camels', 'name' => 'إبل'],
            ['category_slug' => 'animals', 'slug' => 'birds', 'name' => 'طيور زينة'],
            ['category_slug' => 'animals', 'slug' => 'pets', 'name' => 'كلاب وقطط'],
            ['category_slug' => 'animals', 'slug' => 'horses', 'name' => 'خيول'],
            ['category_slug' => 'animals', 'slug' => 'fish', 'name' => 'أسماك وكائنات بحرية'],
            ['category_slug' => 'animals', 'slug' => 'feed', 'name' => 'أعلاف ومستلزمات'],

            // إلكترونيات
            ['category_slug' => 'electronics', 'slug' => 'phones', 'name' => 'هواتف ذكية'],
            ['category_slug' => 'electronics', 'slug' => 'computers', 'name' => 'حاسبات وأجهزة لوحية'],
            ['category_slug' => 'electronics', 'slug' => 'tv_audio', 'name' => 'تلفزيونات وصوتيات'],
            ['category_slug' => 'electronics', 'slug' => 'gaming', 'name' => 'ألعاب فيديو'],
            ['category_slug' => 'electronics', 'slug' => 'cameras', 'name' => 'كاميرات وتصوير'],
            ['category_slug' => 'electronics', 'slug' => 'accessories', 'name' => 'إكسسوارات إلكترونية'],
            ['category_slug' => 'electronics', 'slug' => 'home_appliances', 'name' => 'أجهزة منزلية'],

            // أثاث منزلي
            ['category_slug' => 'furniture', 'slug' => 'living_room', 'name' => 'أثاث غرف معيشة'],
            ['category_slug' => 'furniture', 'slug' => 'bedroom', 'name' => 'أثاث غرف نوم'],
            ['category_slug' => 'furniture', 'slug' => 'kitchen', 'name' => 'مطابخ ومستلزماتها'],
            ['category_slug' => 'furniture', 'slug' => 'garden', 'name' => 'أثاث حدائق'],
            ['category_slug' => 'furniture', 'slug' => 'office', 'name' => 'أثاث مكتبي'],
            ['category_slug' => 'furniture', 'slug' => 'decoration', 'name' => 'ديكور وزينة'],
            ['category_slug' => 'furniture', 'slug' => 'lighting', 'name' => 'إضاءة'],

            // أزياء وملابس
            ['category_slug' => 'fashion', 'slug' => 'men', 'name' => 'ملابس رجالية'],
            ['category_slug' => 'fashion', 'slug' => 'women', 'name' => 'ملابس نسائية'],
            ['category_slug' => 'fashion', 'slug' => 'kids', 'name' => 'ملابس أطفال'],
            ['category_slug' => 'fashion', 'slug' => 'watches', 'name' => 'ساعات ومجوهرات'],
            ['category_slug' => 'fashion', 'slug' => 'bags', 'name' => 'حقائب وأمتعة'],
            ['category_slug' => 'fashion', 'slug' => 'shoes', 'name' => 'أحذية'],
            ['category_slug' => 'fashion', 'slug' => 'accessories', 'name' => 'إكسسوارات'],

            // خدمات
            ['category_slug' => 'services', 'slug' => 'home_services', 'name' => 'خدمات منزلية'],
            ['category_slug' => 'services', 'slug' => 'business_services', 'name' => 'خدمات أعمال'],
            ['category_slug' => 'services', 'slug' => 'health_services', 'name' => 'خدمات صحية'],
            ['category_slug' => 'services', 'slug' => 'beauty_services', 'name' => 'خدمات تجميل'],
            ['category_slug' => 'services', 'slug' => 'tech_services', 'name' => 'خدمات تقنية'],
            ['category_slug' => 'services', 'slug' => 'events_services', 'name' => 'خدمات مناسبات'],
            ['category_slug' => 'services', 'slug' => 'transportation', 'name' => 'خدمات نقل وشحن'],
        ];

        // إدخال الفئات الفرعية
        foreach ($subcategories as $subcategory) {
            $categoryId = DB::table('categories')
                ->where('slug', $subcategory['category_slug'])
                ->value('id');

            if ($categoryId) {
                DB::table('subcategories')->insertOrIgnore([
                    'category_id' => $categoryId,
                    'slug' => $subcategory['slug'],
                    'name' => $subcategory['name'],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // لا نقوم بحذف الجداول، فقط نحذف البيانات الجديدة
        // يمكن تعديل هذا السلوك حسب الحاجة
    }
}
