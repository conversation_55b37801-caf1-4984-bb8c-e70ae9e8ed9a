<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SiteSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class SiteSettingsController extends Controller
{
    public function __construct()
    {
        // التأكد من أن المستخدم مدير
        $this->middleware(function ($request, $next) {
            if (!auth()->check() || !auth()->user()->is_admin) {
                abort(403, 'غير مصرح لك بالوصول إلى هذه الصفحة');
            }
            return $next($request);
        });
    }

    /**
     * عرض صفحة إعدادات الموقع
     */
    public function index()
    {
        $settings = SiteSetting::where('is_active', true)
            ->orderBy('group')
            ->orderBy('sort_order')
            ->get()
            ->groupBy('group');

        return view('admin.site-settings.index', compact('settings'));
    }

    /**
     * تحديث الإعدادات
     */
    public function update(Request $request)
    {
        try {
            $settings = SiteSetting::where('is_active', true)->get();
            
            foreach ($settings as $setting) {
                $value = $request->input($setting->key);
                
                if ($value !== null) {
                    // التحقق من صحة البيانات
                    if (!$setting->validateValue($value)) {
                        return back()->withErrors([
                            $setting->key => "قيمة غير صحيحة لحقل {$setting->label}"
                        ])->withInput();
                    }

                    // معالجة رفع الصور
                    if ($setting->type === 'image' && $request->hasFile($setting->key)) {
                        $value = $this->handleImageUpload($request->file($setting->key), $setting->key);
                    }

                    // تحديث القيمة
                    $setting->update(['value' => $value]);
                }
            }

            // مسح الكاش
            SiteSetting::clearCache();

            return back()->with('success', 'تم تحديث إعدادات الموقع بنجاح!');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'حدث خطأ أثناء تحديث الإعدادات: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * معالجة رفع الصور
     */
    private function handleImageUpload($file, $key)
    {
        try {
            // التحقق من نوع الملف
            $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'ico', 'svg'];
            $extension = $file->getClientOriginalExtension();
            
            if (!in_array(strtolower($extension), $allowedTypes)) {
                throw new \Exception('نوع الملف غير مدعوم');
            }

            // التحقق من حجم الملف (5MB)
            if ($file->getSize() > 5 * 1024 * 1024) {
                throw new \Exception('حجم الملف كبير جداً (الحد الأقصى 5MB)');
            }

            // إنشاء اسم فريد للملف
            $fileName = $key . '_' . time() . '.' . $extension;
            $path = 'images/site/' . $fileName;

            // التأكد من وجود المجلد
            $directory = public_path('images/site');
            if (!file_exists($directory)) {
                mkdir($directory, 0755, true);
            }

            // نقل الملف
            $file->move($directory, $fileName);

            // حذف الملف القديم إذا وجد
            $oldSetting = SiteSetting::where('key', $key)->first();
            if ($oldSetting && $oldSetting->value && $oldSetting->value !== $path) {
                $oldPath = public_path($oldSetting->value);
                if (file_exists($oldPath)) {
                    unlink($oldPath);
                }
            }

            return $path;

        } catch (\Exception $e) {
            throw new \Exception('فشل في رفع الصورة: ' . $e->getMessage());
        }
    }

    /**
     * إعادة تعيين الإعدادات للقيم الافتراضية
     */
    public function reset()
    {
        try {
            // حذف جميع الإعدادات الحالية
            SiteSetting::truncate();

            // إعادة تشغيل migration لإدراج القيم الافتراضية
            \Artisan::call('migrate:refresh', [
                '--path' => 'database/migrations/2024_01_01_000000_create_site_settings_table.php'
            ]);

            // مسح الكاش
            SiteSetting::clearCache();

            return back()->with('success', 'تم إعادة تعيين الإعدادات للقيم الافتراضية بنجاح!');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'حدث خطأ أثناء إعادة التعيين: ' . $e->getMessage()]);
        }
    }

    /**
     * تصدير الإعدادات
     */
    public function export()
    {
        try {
            $settings = SiteSetting::all()->toArray();
            $fileName = 'site_settings_' . date('Y-m-d_H-i-s') . '.json';
            
            return response()->json($settings)
                ->header('Content-Disposition', 'attachment; filename="' . $fileName . '"');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'فشل في تصدير الإعدادات: ' . $e->getMessage()]);
        }
    }

    /**
     * استيراد الإعدادات
     */
    public function import(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'settings_file' => 'required|file|mimes:json'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $file = $request->file('settings_file');
            $content = file_get_contents($file->getRealPath());
            $settings = json_decode($content, true);

            if (!$settings) {
                throw new \Exception('ملف غير صحيح');
            }

            foreach ($settings as $settingData) {
                SiteSetting::updateOrCreate(
                    ['key' => $settingData['key']],
                    $settingData
                );
            }

            // مسح الكاش
            SiteSetting::clearCache();

            return back()->with('success', 'تم استيراد الإعدادات بنجاح!');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'فشل في استيراد الإعدادات: ' . $e->getMessage()]);
        }
    }

    /**
     * مسح الكاش
     */
    public function clearCache()
    {
        try {
            SiteSetting::clearCache();
            \Artisan::call('cache:clear');
            
            return back()->with('success', 'تم مسح الكاش بنجاح!');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'فشل في مسح الكاش: ' . $e->getMessage()]);
        }
    }
}
