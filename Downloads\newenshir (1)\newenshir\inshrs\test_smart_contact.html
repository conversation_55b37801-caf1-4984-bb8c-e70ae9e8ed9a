<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أزرار التواصل الذكي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
            text-align: right;
        }
        .container {
            max-width: 800px;
            margin: 2rem auto;
        }
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        .form-control {
            border-radius: 0.5rem;
            border: 2px solid #e9ecef;
            padding: 0.75rem;
        }
        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
        .alert {
            border-radius: 0.75rem;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .btn {
            border-radius: 50rem;
            padding: 0.5rem 1rem;
            font-weight: 500;
        }
        .demo-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .test-input {
            background-color: white;
            border: 2px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .test-input:focus-within {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
        .whatsapp-alert {
            animation: slideIn 0.3s ease-out;
        }
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fab fa-whatsapp me-2"></i>
                    اختبار أزرار التواصل الذكي
                </h4>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>كيفية الاختبار:</strong>
                    <br>
                    اكتب رقم يبدأ بـ <code>05</code> أو <code>966</code> في أي من الحقول أدناه وسيظهر تنبيه تلقائي لفتح واتساب.
                </div>

                <form>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">
                                    <i class="fas fa-phone me-2"></i>
                                    رقم الهاتف
                                </label>
                                <input type="text" name="phone" id="phone" class="form-control" placeholder="مثال: 0501234567">
                                <small class="text-muted">جرب كتابة: 0501234567</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="whatsapp" class="form-label">
                                    <i class="fab fa-whatsapp me-2"></i>
                                    رقم الواتساب
                                </label>
                                <input type="text" name="whatsapp" id="whatsapp" class="form-control" placeholder="مثال: 966501234567">
                                <small class="text-muted">جرب كتابة: 966501234567</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope me-2"></i>
                            البريد الإلكتروني
                        </label>
                        <input type="email" name="email" id="email" class="form-control" placeholder="مثال: <EMAIL>">
                        <small class="text-muted">هذا الحقل لا يحتوي على ميزة التواصل الذكي</small>
                    </div>
                </form>
            </div>
        </div>

        <div class="demo-section">
            <h5 class="mb-3">
                <i class="fas fa-cogs me-2"></i>
                أمثلة للاختبار
            </h5>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="test-input">
                        <strong>أرقام سعودية (05):</strong>
                        <ul class="mt-2 mb-0">
                            <li>0501234567</li>
                            <li>0551234567</li>
                            <li>0591234567</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="test-input">
                        <strong>أرقام دولية (966):</strong>
                        <ul class="mt-2 mb-0">
                            <li>966501234567</li>
                            <li>966551234567</li>
                            <li>966591234567</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="test-input">
                        <strong>أرقام أخرى:</strong>
                        <ul class="mt-2 mb-0">
                            <li>01234567 (لن يظهر تنبيه)</li>
                            <li>971501234567 (لن يظهر تنبيه)</li>
                            <li>123456789 (لن يظهر تنبيه)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center">
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <strong>تم تنفيذ المهمة الثانية بنجاح!</strong>
                <br>
                <small>عندما يبدأ المستخدم بكتابة رقم بـ 05 أو 966، يتم توجيهه تلقائيًا إلى واتساب.</small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Smart Contact Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const phoneInput = document.getElementById('phone');
            const whatsappInput = document.getElementById('whatsapp');
            
            // دالة للتحقق من الرقم وإظهار تنبيه واتساب
            function checkForWhatsAppRedirect(input, inputType) {
                if (!input) return;
                
                input.addEventListener('input', function() {
                    const value = this.value.trim();
                    
                    // التحقق من بداية الرقم بـ 05 أو 966
                    if (value.startsWith('05') || value.startsWith('966')) {
                        // إنشاء تنبيه ديناميكي
                        showWhatsAppAlert(value, inputType, this);
                    }
                });
            }
            
            // دالة إظهار تنبيه واتساب
            function showWhatsAppAlert(phoneNumber, inputType, inputElement) {
                // إزالة أي تنبيه سابق
                const existingAlert = document.querySelector('.whatsapp-alert');
                if (existingAlert) {
                    existingAlert.remove();
                }
                
                // تنسيق الرقم للواتساب
                let whatsappNumber = phoneNumber;
                if (whatsappNumber.startsWith('05')) {
                    whatsappNumber = '966' + whatsappNumber.substring(1);
                }
                
                // إنشاء عنصر التنبيه
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-info whatsapp-alert mt-2';
                alertDiv.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="fab fa-whatsapp text-success me-2" style="font-size: 1.2rem;"></i>
                        <div class="flex-grow-1">
                            <strong>تم اكتشاف رقم سعودي!</strong>
                            <br>
                            <small>هل تريد فتح واتساب مباشرة؟ (${whatsappNumber})</small>
                        </div>
                        <div>
                            <button type="button" class="btn btn-success btn-sm me-2" onclick="openWhatsApp('${whatsappNumber}')">
                                <i class="fab fa-whatsapp me-1"></i>
                                فتح واتساب
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="dismissAlert(this)">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                `;
                
                // إدراج التنبيه بعد حقل الإدخال
                inputElement.parentNode.insertBefore(alertDiv, inputElement.nextSibling);
                
                // إخفاء التنبيه تلقائياً بعد 10 ثوان
                setTimeout(() => {
                    if (alertDiv && alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 10000);
            }
            
            // تطبيق التحقق على حقول الهاتف والواتساب
            checkForWhatsAppRedirect(phoneInput, 'phone');
            checkForWhatsAppRedirect(whatsappInput, 'whatsapp');
        });
        
        // دالة فتح واتساب
        function openWhatsApp(phoneNumber) {
            const whatsappUrl = `https://wa.me/${phoneNumber}`;
            window.open(whatsappUrl, '_blank');
        }
        
        // دالة إغلاق التنبيه
        function dismissAlert(button) {
            const alert = button.closest('.whatsapp-alert');
            if (alert) {
                alert.remove();
            }
        }
    </script>
</body>
</html>
