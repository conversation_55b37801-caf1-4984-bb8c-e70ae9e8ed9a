@extends('layouts.dashboard')

@section('styles')
<style>
    .form-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }
    
    .form-section-title {
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 10px;
        margin-bottom: 20px;
        color: #0d6efd;
        font-weight: 600;
    }
    
    .required-field::after {
        content: " *";
        color: red;
    }
    
    .preview-image {
        max-width: 150px;
        max-height: 150px;
        border-radius: 5px;
        border: 1px solid #dee2e6;
    }
</style>
@endsection

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">إنشاء سيرة ذاتية جديدة</h5>
                </div>
                <div class="card-body">
                    @if($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('resumes.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        
                        <!-- المعلومات الشخصية -->
                        <div class="form-section">
                            <h5 class="form-section-title">المعلومات الشخصية</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="full_name" class="form-label required-field">الاسم الكامل</label>
                                    <input type="text" class="form-control @error('full_name') is-invalid @enderror" id="full_name" name="full_name" value="{{ old('full_name') }}" required>
                                    @error('full_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="job_title" class="form-label">المسمى الوظيفي</label>
                                    <input type="text" class="form-control @error('job_title') is-invalid @enderror" id="job_title" name="job_title" value="{{ old('job_title') }}">
                                    @error('job_title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label required-field">البريد الإلكتروني</label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email', Auth::user()->email) }}" required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف</label>
                                    <input type="text" class="form-control @error('phone') is-invalid @enderror" id="phone" name="phone" value="{{ old('phone', Auth::user()->phone) }}">
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="address" class="form-label">العنوان</label>
                                    <input type="text" class="form-control @error('address') is-invalid @enderror" id="address" name="address" value="{{ old('address') }}">
                                    @error('address')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="photo" class="form-label">الصورة الشخصية</label>
                                    <input type="file" class="form-control @error('photo') is-invalid @enderror" id="photo" name="photo" accept="image/*">
                                    @error('photo')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div id="photo-preview" class="mt-2"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- ملخص -->
                        <div class="form-section">
                            <h5 class="form-section-title">ملخص السيرة الذاتية</h5>
                            <div class="mb-3">
                                <label for="summary" class="form-label">نبذة مختصرة عنك</label>
                                <textarea class="form-control @error('summary') is-invalid @enderror" id="summary" name="summary" rows="4">{{ old('summary') }}</textarea>
                                @error('summary')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">اكتب نبذة مختصرة عن خبراتك ومهاراتك وأهدافك المهنية.</div>
                            </div>
                        </div>
                        
                        <!-- المهارات -->
                        <div class="form-section">
                            <h5 class="form-section-title">المهارات</h5>
                            <div class="mb-3">
                                <label for="skills" class="form-label">المهارات</label>
                                <textarea class="form-control @error('skills') is-invalid @enderror" id="skills" name="skills" rows="4">{{ old('skills') }}</textarea>
                                @error('skills')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">اكتب مهاراتك مفصولة بفواصل أو سطور جديدة.</div>
                            </div>
                        </div>
                        
                        <!-- التعليم -->
                        <div class="form-section">
                            <h5 class="form-section-title">التعليم</h5>
                            <div class="mb-3">
                                <label for="education" class="form-label">المؤهلات التعليمية</label>
                                <textarea class="form-control @error('education') is-invalid @enderror" id="education" name="education" rows="4">{{ old('education') }}</textarea>
                                @error('education')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">اكتب مؤهلاتك التعليمية مع ذكر اسم المؤسسة التعليمية والتخصص وسنة التخرج.</div>
                            </div>
                        </div>
                        
                        <!-- الخبرات -->
                        <div class="form-section">
                            <h5 class="form-section-title">الخبرات العملية</h5>
                            <div class="mb-3">
                                <label for="experience" class="form-label">الخبرات العملية</label>
                                <textarea class="form-control @error('experience') is-invalid @enderror" id="experience" name="experience" rows="4">{{ old('experience') }}</textarea>
                                @error('experience')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">اكتب خبراتك العملية مع ذكر اسم الشركة والمسمى الوظيفي وفترة العمل والمهام الرئيسية.</div>
                            </div>
                        </div>
                        
                        <!-- معلومات إضافية -->
                        <div class="form-section">
                            <h5 class="form-section-title">معلومات إضافية</h5>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="languages" class="form-label">اللغات</label>
                                    <textarea class="form-control @error('languages') is-invalid @enderror" id="languages" name="languages" rows="3">{{ old('languages') }}</textarea>
                                    @error('languages')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="certifications" class="form-label">الشهادات والدورات</label>
                                    <textarea class="form-control @error('certifications') is-invalid @enderror" id="certifications" name="certifications" rows="3">{{ old('certifications') }}</textarea>
                                    @error('certifications')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="interests" class="form-label">الاهتمامات</label>
                                    <textarea class="form-control @error('interests') is-invalid @enderror" id="interests" name="interests" rows="3">{{ old('interests') }}</textarea>
                                    @error('interests')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ route('resumes.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i> العودة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ السيرة الذاتية
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // معاينة الصورة قبل الرفع
    document.getElementById('photo').addEventListener('change', function(event) {
        const photoPreview = document.getElementById('photo-preview');
        photoPreview.innerHTML = '';
        
        if (event.target.files && event.target.files[0]) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const img = document.createElement('img');
                img.src = e.target.result;
                img.classList.add('preview-image', 'mt-2');
                photoPreview.appendChild(img);
            }
            
            reader.readAsDataURL(event.target.files[0]);
        }
    });
</script>
@endsection
