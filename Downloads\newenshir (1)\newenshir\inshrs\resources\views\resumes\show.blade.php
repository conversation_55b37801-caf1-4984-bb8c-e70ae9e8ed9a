@extends('layouts.dashboard')

@section('styles')
<style>
    :root {
        --primary-color: {{ $resume->primary_color ?? session('resume_primary_color', '#E67E22') }};
        --secondary-color: {{ $resume->secondary_color ?? session('resume_secondary_color', '#3498DB') }};
        --text-color: {{ $resume->text_color ?? session('resume_text_color', '#333333') }};
        --background-color: {{ $resume->background_color ?? session('resume_background_color', '#FFFFFF') }};
        --font-family: {{ $resume->font_family ?? session('resume_font_family', 'Cairo') }}, 'Arial', sans-serif;
    }

    .resume-container {
        background-color: var(--background-color);
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        font-family: var(--font-family);
    }

    .resume-header {
        background-color: var(--primary-color);
        color: white;
        padding: 30px;
        position: relative;
    }

    .resume-photo {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        border: 5px solid rgba(255, 255, 255, 0.3);
        object-fit: cover;
    }

    .resume-name {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .resume-title {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-bottom: 15px;
    }

    .resume-contact {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-top: 20px;
    }

    .resume-contact-item {
        display: flex;
        align-items: center;
        background-color: rgba(255, 255, 255, 0.2);
        padding: 5px 15px;
        border-radius: 20px;
    }

    .resume-contact-item i {
        margin-left: 8px;
    }

    .resume-body {
        padding: 30px;
        color: var(--text-color);
    }

    .resume-section {
        margin-bottom: 30px;
    }

    .resume-section-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--primary-color);
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 10px;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }

    .resume-section-title i {
        margin-left: 10px;
        opacity: 0.8;
    }

    .resume-section-content {
        white-space: pre-line;
        line-height: 1.6;
    }

    .resume-actions {
        position: absolute;
        top: 20px;
        left: 20px;
    }

    .no-photo-placeholder {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 4rem;
        color: white;
    }

    .customize-panel {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .color-picker {
        width: 100%;
        height: 40px;
        padding: 0;
        border: 1px solid #ced4da;
        border-radius: 5px;
    }

    .font-select {
        width: 100%;
        height: 40px;
        padding: 0 10px;
        border: 1px solid #ced4da;
        border-radius: 5px;
    }
</style>
@endsection

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-10">
            @if(session('success'))
                <div class="alert alert-success mb-4">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger mb-4">
                    {{ session('error') }}
                </div>
            @endif

            <div class="resume-container">
                <div class="resume-header">
                    <div class="resume-actions">
                        <div class="btn-group">
                            <a href="{{ route('resumes.edit', $resume->id) }}" class="btn btn-light btn-sm">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <a href="{{ route('resumes.pdf', $resume->id) }}" class="btn btn-light btn-sm">
                                <i class="fas fa-file-pdf"></i> تحميل PDF
                            </a>
                        </div>
                    </div>

                    <div class="row align-items-center">
                        <div class="col-md-3 text-center mb-4 mb-md-0">
                            @if($resume->photo)
                                <img src="{{ Storage::url($resume->photo) }}" alt="{{ $resume->full_name }}" class="resume-photo">
                            @else
                                <div class="no-photo-placeholder">
                                    <i class="fas fa-user"></i>
                                </div>
                            @endif
                        </div>
                        <div class="col-md-9">
                            <h1 class="resume-name">{{ $resume->full_name }}</h1>
                            @if($resume->job_title)
                                <div class="resume-title">{{ $resume->job_title }}</div>
                            @endif

                            <div class="resume-contact">
                                <div class="resume-contact-item">
                                    <i class="fas fa-envelope"></i>
                                    <span>{{ $resume->email }}</span>
                                </div>

                                @if($resume->phone)
                                    <div class="resume-contact-item">
                                        <i class="fas fa-phone"></i>
                                        <span>{{ $resume->phone }}</span>
                                    </div>
                                @endif

                                @if($resume->address)
                                    <div class="resume-contact-item">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <span>{{ $resume->address }}</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <div class="resume-body">
                    @if($resume->summary)
                        <div class="resume-section">
                            <h3 class="resume-section-title">
                                <i class="fas fa-user"></i> نبذة مختصرة
                            </h3>
                            <div class="resume-section-content">
                                {{ $resume->summary }}
                            </div>
                        </div>
                    @endif

                    @if($resume->skills)
                        <div class="resume-section">
                            <h3 class="resume-section-title">
                                <i class="fas fa-tools"></i> المهارات
                            </h3>
                            <div class="resume-section-content">
                                {{ $resume->skills }}
                            </div>
                        </div>
                    @endif

                    @if($resume->experience)
                        <div class="resume-section">
                            <h3 class="resume-section-title">
                                <i class="fas fa-briefcase"></i> الخبرات العملية
                            </h3>
                            <div class="resume-section-content">
                                {{ $resume->experience }}
                            </div>
                        </div>
                    @endif

                    @if($resume->education)
                        <div class="resume-section">
                            <h3 class="resume-section-title">
                                <i class="fas fa-graduation-cap"></i> التعليم
                            </h3>
                            <div class="resume-section-content">
                                {{ $resume->education }}
                            </div>
                        </div>
                    @endif

                    <div class="row">
                        @if($resume->languages)
                            <div class="col-md-4">
                                <div class="resume-section">
                                    <h3 class="resume-section-title">
                                        <i class="fas fa-language"></i> اللغات
                                    </h3>
                                    <div class="resume-section-content">
                                        {{ $resume->languages }}
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if($resume->certifications)
                            <div class="col-md-4">
                                <div class="resume-section">
                                    <h3 class="resume-section-title">
                                        <i class="fas fa-certificate"></i> الشهادات
                                    </h3>
                                    <div class="resume-section-content">
                                        {{ $resume->certifications }}
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if($resume->interests)
                            <div class="col-md-4">
                                <div class="resume-section">
                                    <h3 class="resume-section-title">
                                        <i class="fas fa-heart"></i> الاهتمامات
                                    </h3>
                                    <div class="resume-section-content">
                                        {{ $resume->interests }}
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <div class="customize-panel mt-4">
                <h4 class="mb-3">تخصيص السيرة الذاتية</h4>
                <form action="{{ route('resumes.customize', $resume->id) }}" method="POST">
                    @csrf
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="primary_color" class="form-label">اللون الرئيسي</label>
                            <input type="color" class="color-picker" id="primary_color" name="primary_color" value="{{ $resume->primary_color ?? session('resume_primary_color', '#E67E22') }}">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="secondary_color" class="form-label">اللون الثانوي</label>
                            <input type="color" class="color-picker" id="secondary_color" name="secondary_color" value="{{ $resume->secondary_color ?? session('resume_secondary_color', '#3498DB') }}">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="text_color" class="form-label">لون النص</label>
                            <input type="color" class="color-picker" id="text_color" name="text_color" value="{{ $resume->text_color ?? session('resume_text_color', '#333333') }}">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="background_color" class="form-label">لون الخلفية</label>
                            <input type="color" class="color-picker" id="background_color" name="background_color" value="{{ $resume->background_color ?? session('resume_background_color', '#FFFFFF') }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="font_family" class="form-label">نوع الخط</label>
                            <select class="font-select" id="font_family" name="font_family">
                                <option value="Cairo" {{ ($resume->font_family ?? session('resume_font_family')) == 'Cairo' ? 'selected' : '' }}>Cairo</option>
                                <option value="Tajawal" {{ ($resume->font_family ?? session('resume_font_family')) == 'Tajawal' ? 'selected' : '' }}>Tajawal</option>
                                <option value="Almarai" {{ ($resume->font_family ?? session('resume_font_family')) == 'Almarai' ? 'selected' : '' }}>Almarai</option>
                                <option value="Amiri" {{ ($resume->font_family ?? session('resume_font_family')) == 'Amiri' ? 'selected' : '' }}>Amiri</option>
                                <option value="Scheherazade" {{ ($resume->font_family ?? session('resume_font_family')) == 'Scheherazade' ? 'selected' : '' }}>Scheherazade</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-save"></i> حفظ التخصيص
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <div class="d-flex justify-content-between mt-4">
                <a href="{{ route('resumes.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة إلى القائمة
                </a>
                <div>
                    <a href="{{ route('resumes.edit', $resume->id) }}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> تعديل
                    </a>
                    <a href="{{ route('resumes.pdf', $resume->id) }}" class="btn btn-primary">
                        <i class="fas fa-file-pdf"></i> تحميل PDF
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
