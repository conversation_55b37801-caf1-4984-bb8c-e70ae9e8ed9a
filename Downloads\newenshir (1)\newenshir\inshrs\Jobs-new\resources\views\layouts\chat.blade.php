
@extends('layouts.chat')

@section('content')

  <!-- محتوى الصفحة -->
  <div class="container">
      

        

  <!-- زر أيقونة الدردشة -->
  <button class="chat-toggle" id="chatToggle">
    <svg viewBox="0 0 24 24">
      <path d="M20,2H4C2.897,2,2,2.897,2,4v14c0,1.103,0.897,2,2,2h4v4l4-4h10c1.103,0,2-0.897,2-2V4C22,2.897,21.103,2,20,2z"/>
    </svg>
  </button>

  <!-- نافذة الدردشة الرئيسية -->
  <div class="chat-container" id="chatContainer">
    <div class="chat-header">
      <button class="close-btn" id="closeChat">×</button>
      <span>دردشة الموقع</span>
      <span></span>
    </div>
    <div class="search-bar">
      <input type="text" placeholder="ابحث هنا...">
    </div>
    <div class="chat-content">
      <div class="article-item" data-page="page1">
        <h3>تعليمات الاستخدام</h3>
        <p>اضغط لقراءة المزيد عن تعليمات الاستخدام وتفاصيلها.</p>
      </div>
      <div class="article-item" data-page="page2">
        <h3>مقالة تقنية</h3>
        <p>اضغط لقراءة المزيد حول آخر المستجدات التقنية.</p>
      </div>
      <div class="article-item" data-page="page3">
        <h3>مقالات الدعم</h3>
        <p>اضغط لقراءة المزيد عن قسم الدعم والشروحات المفصلة.</p>
      </div>
    </div>
  </div>

  <!-- صفحة التفاصيل للمقالة 1 -->
  <div class="chat-container page-detail" id="page1">
    <div class="chat-header">
      <button class="close-btn">×</button>
      <span>تعليمات الاستخدام</span>
      <button class="back-btn" data-target="chatContainer">⟵</button>
    </div>
    <div class="chat-content">
      <div class="message bot">
        <div class="bubble">
          هنا تجد معلومات مفصلة عن تعليمات استخدام الموقع، تشمل شرح الخطوات والنصائح والأدوات المتاحة للمستخدم.
        </div>
        <div class="time">قبل 5 دقائق</div>
      </div>
      <div class="message bot">
        <div class="bubble">
          يمكنك إضافة المزيد من المحتوى هنا لتوفير تجربة تعليمية شاملة.
        </div>
        <div class="time">قبل 5 دقائق</div>
      </div>
      <div class="reaction-buttons">
        <button>👍</button>
        <button>👎</button>
      </div>
    </div>
  </div>

  <!-- صفحة التفاصيل للمقالة 2 -->
  <div class="chat-container page-detail" id="page2">
    <div class="chat-header">
      <button class="close-btn">×</button>
      <span>مقالة تقنية</span>
      <button class="back-btn" data-target="chatContainer">⟵</button>
    </div>
    <div class="chat-content">
      <div class="message bot">
        <div class="bubble">
          في هذه الصفحة نقدم آخر المستجدات التقنية، مع شرح لأحدث التقنيات والأدوات المستخدمة في تطوير المواقع والتطبيقات.
        </div>
        <div class="time">قبل 5 دقائق</div>
      </div>
      <div class="message bot">
        <div class="bubble">
          ستجد هنا مقالات تعليمية وأمثلة عملية تساعدك على تطبيق هذه التقنيات بكفاءة.
        </div>
        <div class="time">قبل 5 دقائق</div>
      </div>
      <div class="reaction-buttons">
        <button>👍</button>
        <button>👎</button>
      </div>
    </div>
  </div>

  <!-- صفحة التفاصيل للمقالة 3 -->
  <div class="chat-container page-detail" id="page3">
    <div class="chat-header">
      <button class="close-btn">×</button>
      <span>دعم الموقع</span>
      <button class="back-btn" data-target="chatContainer">⟵</button>
    </div>
    <div class="chat-content">
      <div class="message bot">
        <div class="bubble">
          تحتوي هذه الصفحة على شروحات ودعم فني مفصل لحل المشكلات والإجابة على الاستفسارات.
        </div>
        <div class="time">قبل 5 دقائق</div>
      </div>
      <div class="message bot">
        <div class="bubble">
          يهدف القسم إلى توفير دعم سريع وفعال لضمان تجربة مستخدم مميزة.
        </div>
        <div class="time">قبل 5 دقائق</div>
      </div>
      <div class="reaction-buttons">
        <button>👍</button>
        <button>👎</button>
      </div>
    </div>
  </div>

  <script>
    const chatToggle = document.getElementById('chatToggle');
    const chatContainer = document.getElementById('chatContainer');
    const articleItems = document.querySelectorAll('.article-item');
    const backButtons = document.querySelectorAll('.back-btn');
    const closeButtons = document.querySelectorAll('.close-btn');

    // تبديل عرض نافذة الدردشة الرئيسية
    chatToggle.addEventListener('click', () => {
      document.querySelectorAll('.chat-container.page-detail').forEach(page => {
        page.classList.remove('active');
      });
      chatContainer.classList.toggle('active');
    });

    // الانتقال إلى صفحة التفاصيل عند الضغط على مقال
    articleItems.forEach(item => {
      item.addEventListener('click', () => {
        const pageId = item.getAttribute('data-page');
        chatContainer.classList.remove('active');
        document.getElementById(pageId).classList.add('active');
      });
    });

    // زر العودة إلى نافذة الدردشة الرئيسية
    backButtons.forEach(btn => {
      btn.addEventListener('click', () => {
        document.querySelectorAll('.chat-container.page-detail').forEach(page => {
          page.classList.remove('active');
        });
        chatContainer.classList.add('active');
      });
    });

    // زر الإغلاق
    closeButtons.forEach(btn => {
      btn.addEventListener('click', () => {
        document.querySelectorAll('.chat-container').forEach(container => {
          container.classList.remove('active');
        });
      });
    });

    // إغلاق النافذة عند النقر خارجها
    document.addEventListener('click', (e) => {
      const isInside = chatToggle.contains(e.target) ||
        chatContainer.contains(e.target) ||
        [...document.querySelectorAll('.chat-container.page-detail')].some(page => page.contains(e.target));
      if (!isInside) {
        document.querySelectorAll('.chat-container').forEach(container => {
          container.classList.remove('active');
        });
      }
    });
  </script>


<style>
   

    /* زر الأيقونة */
    .chat-toggle {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: #007bff;
      color: #fff;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      border: none;
      cursor: pointer;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      transition: background 0.3s ease, transform 0.3s ease;
      z-index: 1001;
    }
    .chat-toggle:hover {
      background: #0056b3;
      transform: scale(1.05);
    }
    .chat-toggle:focus {
      outline: none;
    }
    .chat-toggle svg {
      width: 30px;
      height: 30px;
      fill: #fff;
    }

    /* تصميم النوافذ */
    .chat-container {
      position: fixed;
      bottom: 90px;
      right: 20px;
      width: 350px;
      max-height: 500px;
      background: #fff;
      border-radius: 15px;
      box-shadow: 0 8px 20px rgba(0,0,0,0.15);
      overflow: hidden;
      display: none;
      flex-direction: column;
      z-index: 1000;
      animation: fadeIn 0.4s ease forwards;
    }
    .chat-container.active {
      display: flex;
    }

    /* رأس النافذة */
    .chat-header {
      background: #007bff;
      color: #fff;
      padding: 10px 15px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 16px;
      position: relative;
    }
    .chat-header .close-btn {
      background: none;
      border: none;
      color: #fff;
      font-size: 20px;
      cursor: pointer;
    }
    .chat-header .back-btn {
      background: none;
      border: none;
      color: #fff;
      font-size: 16px;
      cursor: pointer;
    }

    /* محتوى الدردشة */
    .chat-content {
      padding: 15px;
      overflow-y: auto;
      flex: 1;
      background: #f5f7fa;
      font-size: 14px;
      color: #333;
    }

    /* عناصر المقالات */
    .article-item {
      margin-bottom: 10px;
      padding: 10px;
      border-radius: 8px;
      background: #fff;
      cursor: pointer;
      transition: background 0.3s ease;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .article-item:hover {
      background: #f8f9fa;
    }
    .article-item h3 {
      margin: 0 0 5px;
      font-size: 16px;
      color: #007bff;
    }
    .article-item p {
      margin: 0;
      font-size: 13px;
      color: #666;
    }

    /* تصميم الرسائل */
    .message {
      margin-bottom: 15px;
      display: flex;
      flex-direction: column;
    }
    .message.bot {
      align-items: flex-start;
    }
    .message.user {
      align-items: flex-end;
    }
    .message .bubble {
      max-width: 70%;
      padding: 10px 15px;
      border-radius: 15px;
      font-size: 14px;
      line-height: 1.5;
    }
    .message.bot .bubble {
      background: #fff;
      color: #333;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .message.user .bubble {
      background: #007bff;
      color: #fff;
    }
    .message .time {
      font-size: 12px;
      color: #999;
      margin-top: 5px;
    }

    /* أزرار التفاعل */
    .reaction-buttons {
      display: flex;
      gap: 10px;
      margin-top: 10px;
    }
    .reaction-buttons button {
      background: #fff;
      border: 1px solid #ddd;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background 0.3s ease;
    }
    .reaction-buttons button:hover {
      background: #f0f0f0;
    }

    /* صفحات التفاصيل */
    .page-detail {
      display: none;
      flex-direction: column;
    }
    .page-detail.active {
      display: flex;
    }

    /* شريط البحث */
    .search-bar {
      padding: 10px;
      background: #fff;
      border-bottom: 1px solid #ddd;
    }
    .search-bar input {
      width: 100%;
      padding: 8px 15px;
      border: 1px solid #ddd;
      border-radius: 20px;
      font-size: 14px;
      outline: none;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
  </style>
  @yield('content')
    </div>

    @endsection