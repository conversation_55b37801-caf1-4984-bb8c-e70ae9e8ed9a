<?php
// app/Http/Controllers/DataController.php

namespace App\Http\Controllers;

use App\Models\Data;
use Illuminate\Http\Request;

class DataController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth'); // التأكد من أن المستخدم مسجل دخوله
    }

    public function create()
    {
        return view('data.create'); // عرض صفحة إضافة البيانات
    }

    public function store(Request $request)
    {
        // التحقق من صحة البيانات
        $validated = $request->validate([
            'job_title' => 'required|string|max:255', // عنوان الوظيفة
            'description' => 'nullable|string', // وصف الوظيفة
            'specialization' => 'nullable|string', // التخصص
            'experience' => 'nullable|integer', // سنوات الخبرة
            'skills' => 'nullable|string', // المهارات
            'location' => 'nullable|string', // الموقع
            'whatsapp' => 'nullable|string', // الواتساب
            'phone' => 'nullable|string', // الهاتف
        ]);

        // حفظ البيانات في قاعدة البيانات
        Data::create([
            'user_id' => auth()->id(), // حفظ الـ user_id تلقائيًا
            'job_title' => $request->job_title,
            'description' => $request->description,
            'specialization' => $request->specialization,
            'experience' => $request->experience,
            'skills' => $request->skills,
            'location' => $request->location,
            'whatsapp' => $request->whatsapp,
            'phone' => $request->phone,
        ]);

        return redirect()->route('data.create')->with('success', 'تم إضافة الوظيفة بنجاح!');
    }
}
