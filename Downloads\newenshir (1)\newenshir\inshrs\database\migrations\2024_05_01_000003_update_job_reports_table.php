<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('job_reports', function (Blueprint $table) {
            // إضافة حقول جديدة إلى جدول تقارير الوظائف الحالي
            if (!Schema::hasColumn('job_reports', 'report_type')) {
                $table->string('report_type')->after('user_id')->default('محتوى غير لائق');
            }
            
            if (!Schema::hasColumn('job_reports', 'status')) {
                $table->enum('status', ['pending', 'reviewed', 'rejected'])->default('pending')->after('reason');
            }
            
            if (!Schema::hasColumn('job_reports', 'admin_notes')) {
                $table->text('admin_notes')->nullable()->after('status');
            }
            
            if (!Schema::hasColumn('job_reports', 'reviewed_at')) {
                $table->timestamp('reviewed_at')->nullable()->after('admin_notes');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('job_reports', function (Blueprint $table) {
            $table->dropColumn(['report_type', 'status', 'admin_notes', 'reviewed_at']);
        });
    }
};
