<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعلانات | منصة الإعلانات السعودية</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
   <style>
        :root {
            --primary-color: #1e40af;
            --primary-light: #3b82f6;
            --primary-dark: #1e3a8a;
            --secondary-color: #059669;
            --accent-color: #f59e0b;
            --light-bg: #f8fafc;
            --card-bg: #ffffff;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --text-white: #ffffff;
            --border-color: #e5e7eb;
            --success-color: #10b981;
            --shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Cairo', 'Tajawal', sans-serif;
        }

        body {
            background-color: var(--light-bg);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background: linear-gradient(to left, var(--primary-color), var(--primary-dark));
            color: var(--text-white);
            padding: 1rem 0;
            box-shadow: var(--shadow);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.75rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .logo i {
            font-size: 2rem;
            color: var(--accent-color);
        }

        nav ul {
            display: flex;
            list-style: none;
            gap: 1rem;
        }

        nav a {
            color: var(--text-white);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        nav a:hover, nav a.active {
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        nav a i {
            font-size: 1.1rem;
            color: var(--accent-color);
        }

        .auth-buttons {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.6rem 1.25rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .btn-login {
            background-color: transparent;
            color: var(--text-white);
            border: 2px solid var(--text-white);
        }

        .btn-login:hover {
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .btn-signup {
            background-color: var(--accent-color);
            color: var(--text-dark);
            font-weight: 700;
        }

        .btn-signup:hover {
            background-color: #f3a533;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        /* Search Section - منصة انشر */
        .search-section {
            padding: 1.5rem 0;
            background-color: #f8f9fa;
            position: relative;
            overflow: hidden;
        }

        .search-section .container {
            position: relative;
            z-index: 2;
        }

        .search-title {
            text-align: center;
            margin-bottom: 1.5rem;
            color: #333;
        }

        .search-title h1 {
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        .search-title p {
            font-size: 1rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
            margin-bottom: 1rem;
            color: #666;
        }

        .admin-action {
            margin-bottom: 1.5rem;
        }

        .admin-action .btn-signup {
            font-size: 1rem;
            padding: 0.75rem 1.5rem;
            background: var(--secondary-color);
            color: white;
        }

        .admin-action .btn-signup:hover {
            background: #047857;
        }

        /* منصة انشر Search Container */
        .ansher-search-container {
            max-width: 900px;
            margin: 1rem auto 2rem;
        }

        .ansher-search-form {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .search-input-container {
            position: relative;
            width: 100%;
        }

        .search-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 1.2rem;
            z-index: 2;
        }

        .ansher-search-input {
            width: 100%;
            padding: 0.9rem 2.75rem 0.9rem 1rem;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.2s;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .ansher-search-input:focus {
            border-color: #2196F3;
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.2);
            outline: none;
        }

        .search-filters {
            display: flex;
            gap: 0.75rem;
            width: 100%;
        }

        .filter-item {
            flex: 1;
        }

        .ansher-select {
            width: 100%;
            padding: 0.9rem 1rem;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            background-color: white;
            color: #333;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: left 1rem center;
            padding-left: 2.5rem;
        }

        .ansher-select:focus {
            border-color: #2196F3;
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.2);
            outline: none;
        }

        .ansher-search-btn, .ansher-filter-btn {
            padding: 0.9rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            transition: all 0.2s;
        }

        .ansher-search-btn {
            background-color: #2196F3;
            color: white;
            min-width: 100px;
        }

        .ansher-search-btn:hover {
            background-color: #1976D2;
        }

        .ansher-filter-btn {
            background-color: #f5f5f5;
            color: #555;
            border: 1px solid #e0e0e0;
            min-width: 100px;
        }

        .ansher-filter-btn:hover {
            background-color: #eeeeee;
        }

        /* Jobs Section */
        .jobs-section {
            padding: 3rem 0;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e5e7eb;
        }

        .section-header h2 {
            font-size: 1.8rem;
            color: var(--primary-dark);
            font-weight: 700;
            position: relative;
            padding-right: 1rem;
        }

        .section-header h2::before {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            height: 100%;
            width: 4px;
            background-color: var(--accent-color);
            border-radius: 2px;
        }

        .job-count {
            background-color: #e0f2fe;
            color: var(--primary-dark);
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 1.5rem;
            display: inline-block;
        }

        .sort-options {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            background: white;
            padding: 0.5rem 1rem;
            border-radius: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            border: 1px solid #e5e7eb;
        }

        .sort-options span {
            color: var(--text-light);
            font-weight: 600;
        }

        .sort-options select {
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--card-bg);
            color: var(--primary-dark);
            font-weight: 500;
            cursor: pointer;
        }

        .job-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
        }

        .job-card {
            background-color: var(--card-bg);
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
            padding: 1.75rem;
            transition: var(--transition);
            border: 1px solid #f1f5f9;
            position: relative;
            overflow: hidden;
            opacity: 0;
            animation: fadeIn 0.5s ease forwards;
        }

        .job-card:hover {
            transform: translateY(-7px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            border-color: rgba(59, 130, 246, 0.3);
        }

        .job-card::before {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            width: 6px;
            height: 100%;
            background: linear-gradient(to bottom, var(--primary-color), var(--primary-light));
            border-radius: 0 16px 16px 0;
        }

        .job-badge {
            position: absolute;
            top: 1.5rem;
            left: 1.5rem;
            background: linear-gradient(to right, #fde68a, #fbbf24);
            color: #92400e;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 700;
            box-shadow: 0 2px 5px rgba(251, 191, 36, 0.3);
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }

        .job-badge i {
            font-size: 0.7rem;
        }

        .job-card h3 {
            color: var(--primary-dark);
            margin-bottom: 0.75rem;
            font-size: 1.35rem;
            line-height: 1.3;
        }

        .job-company {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1.25rem;
        }

        .company-logo {
            width: 50px;
            height: 50px;
            background: linear-gradient(to bottom right, var(--primary-light), var(--primary-dark));
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-white);
            font-weight: 700;
            font-size: 1.5rem;
            box-shadow: 0 3px 8px rgba(30, 64, 175, 0.2);
        }

        .job-details {
            margin-bottom: 1.25rem;
            background-color: #f8fafc;
            border-radius: 10px;
            padding: 1rem;
        }

        .job-detail {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.75rem;
            color: var(--text-dark);
        }

        .job-detail i {
            color: var(--primary-light);
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }

        .job-description {
            margin-top: 1.25rem;
            margin-bottom: 1.5rem;
            color: var(--text-light);
            line-height: 1.6;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            position: relative;
            padding-bottom: 0.5rem;
        }

        .job-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .apply-btn {
            background: linear-gradient(to right, var(--secondary-color), #34d399);
            color: var(--text-white);
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            transition: var(--transition);
            text-decoration: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .apply-btn:hover {
            background: linear-gradient(to left, var(--secondary-color), #34d399);
            transform: translateY(-3px);
            box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
        }

        .save-btn {
            background-color: #f0fdf4;
            color: var(--secondary-color);
            border: 1px solid var(--secondary-color);
            padding: 0.6rem 1.1rem;
            border-radius: 8px;
            font-weight: 500;
            transition: var(--transition);
            cursor: pointer;
        }

        .save-btn:hover {
            background-color: #dcfce7;
            transform: translateY(-2px);
        }

        /* Footer */
        footer {
            background-color: var(--text-dark);
            color: var(--text-white);
            padding: 2rem 0;
            text-align: center;
            box-shadow: var(--shadow);
        }

        .footer-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
        }

        .footer-links {
            display: flex;
            gap: 1.5rem;
        }

        .footer-links a {
            color: var(--text-white);
            text-decoration: none;
            transition: var(--transition);
        }

        .footer-links a:hover {
            text-decoration: underline;
            color: var(--accent-color);
        }

        .social-links {
            display: flex;
            gap: 1rem;
            font-size: 1.25rem;
        }

        .social-links a {
            color: var(--text-white);
            transition: color 0.3s ease;
        }

        .social-links a:hover {
            color: var(--accent-color);
        }

        .footer-bottom {
            margin-top: 1.5rem;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* Animations */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .header-content, .auth-buttons, nav ul, .search-filters, .footer-links, .social-links {
                flex-direction: column;
                align-items: center;
                text-align: center;
            }

            .search-filters {
                width: 100%;
            }

            .filter-item {
                width: 100%;
            }

            .ansher-select {
                width: 100%;
            }

            .job-cards {
                grid-template-columns: 1fr;
            }
        }

        /* Additional منصة انشر Styles */
        .category-bar {
            background-color: #fff;
            padding: 0.75rem 0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.08);
            overflow-x: auto;
            white-space: nowrap;
        }

        .category-list {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0 1rem;
        }

        .category-item {
            color: #6b7280;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            transition: color 0.3s ease;
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
        }

        .category-item:hover {
            color: var(--primary-color);
            background-color: #f9f9f9;
        }

        .category-item i {
            font-size: 1rem;
        }

        /* Ad Listing Styles */
        .ad-listings {
            padding: 2rem 0;
        }

        .ad-card {
            background-color: var(--card-bg);
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid #f1f5f9;
        }

        .ad-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
        }

        .ad-image {
            height: 220px;
            overflow: hidden;
            position: relative;
        }

        .ad-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.4s ease;
        }

        .ad-card:hover .ad-image img {
            transform: scale(1.05);
        }

        .ad-details {
            padding: 1.25rem;
        }

        .ad-title {
            color: var(--primary-dark);
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            line-height: 1.3;
        }

        .ad-location, .ad-date {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-light);
            font-size: 0.85rem;
            margin-bottom: 0.5rem;
        }

        .ad-location i, .ad-date i {
            font-size: 0.9rem;
        }

        .ad-price {
            color: var(--secondary-color);
            font-size: 1.3rem;
            font-weight: 700;
        }

        /* Responsive Ad Grid */
        .ad-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1.75rem;
        }

        /* Mobile Styles */
        @media (max-width: 640px) {
            .ad-grid {
                grid-template-columns: 1fr;
            }

            .ad-image {
                height: 180px;
            }
        }

    </style>
</head>
<body>



    <!-- شريط التنقل العلوي -->
    <header class="bg-white border-b border-gray-200">
        <div class="container mx-auto px-4 py-2">
            <div class="flex justify-between items-center">
                <!-- القائمة الرئيسية -->
                <div class="flex space-x-6 rtl:space-x-reverse">
                    <a href="#" class="text-gray-700 hover:text-gray-900 px-2 py-1">الرئيسية</a>
                    <a href="#" class="text-gray-700 hover:text-gray-900 px-2 py-1">مواشي و حيوانات و طيور</a>
                    <a href="#" class="text-gray-700 hover:text-gray-900 px-2 py-1">أجهزة</a>
                    <a href="#" class="text-gray-700 hover:text-gray-900 px-2 py-1">عقارات</a>
                    <a href="#" class="text-gray-700 hover:text-gray-900 px-2 py-1">سيارات</a>
                </div>

                <!-- البحث -->
                <div class="flex items-center">
                    <a href="#" class="text-gray-700 hover:text-gray-900 px-2 py-1">
                        <i class="fas fa-search"></i>
                        <span class="mr-1">البحث</span>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- شريط الشعار والدخول -->
    <div class="bg-blue-600 text-white py-2">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center">
                <!-- الشعار -->
                <a href="#" class="text-white text-2xl font-bold">منصة انشر</a>

                <!-- زر الدخول -->
                <div class="flex items-center">
                    <a href="#" class="bg-blue-700 hover:bg-blue-800 px-4 py-1 rounded-md flex items-center">
                        <i class="fas fa-sign-in-alt ml-1"></i>
                        <span>دخول</span>
                    </a>
                    <div class="mx-2 text-white">
                        <i class="fas fa-moon"></i>
                    </div>
                    <div class="text-white">
                        <span>En</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم البحث والإضافة -->
    <div class="container mx-auto px-2 mt-4">
        <!-- زر إضافة عرض والبحث -->
        <div class="flex gap-2 mb-4">
            <!-- زر إضافة عرض -->
            <a href="{{ route('jobs.create') }}" class="flex items-center gap-1 bg-amber-100 text-amber-800 px-4 py-2 rounded-lg hover:bg-amber-200 transition-all duration-300">
                <i class="fas fa-plus"></i>
                <span>إضافة عرض</span>
            </a>

            <!-- مربع البحث -->
            <div class="flex flex-1">
                <form action="{{ route('jobs.index') }}" method="GET" class="flex w-full">
                    <!-- زر البحث -->
                    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-r-none rounded-l-lg hover:bg-blue-700 transition-all duration-300">
                        <i class="fas fa-search"></i>
                    </button>

                    <!-- حقل البحث -->
                    <input type="text" name="keyword" placeholder="ابحث عن سلعة" value="{{ request('keyword') }}" class="flex-1 border border-gray-300 px-4 py-2 rounded-l-lg rounded-r-none focus:outline-none focus:ring-2 focus:ring-blue-500 text-right">

                    <!-- حقل المدينة (مخفي) -->
                    <input type="hidden" name="location" value="{{ request('location') }}">
                </form>
            </div>
        </div>

        <!-- أيقونات التصنيفات -->
        <div class="grid grid-cols-9 gap-2 mb-4">
            <a href="#" class="flex flex-col items-center justify-center p-2 hover:bg-gray-100 rounded-lg transition-all duration-300">
                <div class="text-blue-600 text-2xl mb-1">
                    <i class="fas fa-car"></i>
                </div>
                <span class="text-sm text-gray-700">سيارات</span>
            </a>
            <a href="#" class="flex flex-col items-center justify-center p-2 hover:bg-gray-100 rounded-lg transition-all duration-300">
                <div class="text-blue-600 text-2xl mb-1">
                    <i class="fas fa-building"></i>
                </div>
                <span class="text-sm text-gray-700">عقارات</span>
            </a>
            <a href="#" class="flex flex-col items-center justify-center p-2 hover:bg-gray-100 rounded-lg transition-all duration-300">
                <div class="text-blue-600 text-2xl mb-1">
                    <i class="fas fa-desktop"></i>
                </div>
                <span class="text-sm text-gray-700">أجهزة</span>
            </a>
            <a href="#" class="flex flex-col items-center justify-center p-2 hover:bg-gray-100 rounded-lg transition-all duration-300">
                <div class="text-blue-600 text-2xl mb-1">
                    <i class="fas fa-paw"></i>
                </div>
                <span class="text-sm text-gray-700">حيوانات</span>
            </a>
            <a href="#" class="flex flex-col items-center justify-center p-2 hover:bg-gray-100 rounded-lg transition-all duration-300">
                <div class="text-blue-600 text-2xl mb-1">
                    <i class="fas fa-couch"></i>
                </div>
                <span class="text-sm text-gray-700">أثاث</span>
            </a>
            <a href="#" class="flex flex-col items-center justify-center p-2 hover:bg-gray-100 rounded-lg transition-all duration-300">
                <div class="text-blue-600 text-2xl mb-1">
                    <i class="fas fa-briefcase"></i>
                </div>
                <span class="text-sm text-gray-700">وظائف</span>
            </a>
            <a href="#" class="flex flex-col items-center justify-center p-2 hover:bg-gray-100 rounded-lg transition-all duration-300">
                <div class="text-blue-600 text-2xl mb-1">
                    <i class="fas fa-concierge-bell"></i>
                </div>
                <span class="text-sm text-gray-700">خدمات</span>
            </a>
            <a href="#" class="flex flex-col items-center justify-center p-2 hover:bg-gray-100 rounded-lg transition-all duration-300">
                <div class="text-blue-600 text-2xl mb-1">
                    <i class="fas fa-tshirt"></i>
                </div>
                <span class="text-sm text-gray-700">ازياء</span>
            </a>
            <a href="#" class="flex flex-col items-center justify-center p-2 hover:bg-gray-100 rounded-lg transition-all duration-300">
                <div class="text-blue-600 text-2xl mb-1">
                    <i class="fas fa-gamepad"></i>
                </div>
                <span class="text-sm text-gray-700">العاب</span>
            </a>
        </div>

        <!-- شريط التصفية -->
        <div class="flex justify-between items-center bg-gray-200 p-2 rounded-lg mb-4">
            <button class="text-blue-600 hover:text-blue-800">
                <i class="fas fa-filter"></i>
                <span>تصفية</span>
            </button>

            <div class="flex items-center gap-2">
                <span class="text-gray-600">كل المناطق</span>
                <i class="fas fa-map-marker-alt text-blue-600"></i>
                <span class="text-gray-600">القريب</span>
                <i class="fas fa-car text-blue-600"></i>
            </div>
        </div>
    </div>

    <section class="ad-listings">
        <div class="container">
            <div class="ad-grid">

                <div class="ad-card">
                    <div class="ad-image">
                        <img src="https://via.placeholder.com/400x250" alt="Ad Image">
                    </div>
                    <div class="ad-details">
                        <h3 class="ad-title">سيارة تويوتا كورولا 2023 للبيع</h3>
                        <div class="ad-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>الرياض</span>
                        </div>
                        <div class="ad-date">
                            <i class="far fa-clock"></i>
                            <span>منذ 3 ساعات</span>
                        </div>
                        <p class="ad-price">75,000 ر.س</p>
                    </div>
                </div>

                <div class="ad-card">
                    <div class="ad-image">
                        <img src="https://via.placeholder.com/400x250" alt="Ad Image">
                    </div>
                    <div class="ad-details">
                        <h3 class="ad-title">شقة فاخرة للإيجار في جدة</h3>
                        <div class="ad-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>جدة</span>
                        </div>
                        <div class="ad-date">
                            <i class="far fa-clock"></i>
                            <span>منذ يوم</span>
                        </div>
                        <p class="ad-price">45,000 ر.س / سنوي</p>
                    </div>
                </div>

                <div class="ad-card">
                    <div class="ad-image">
                        <img src="https://via.placeholder.com/400x250" alt="Ad Image">
                    </div>
                    <div class="ad-details">
                        <h3 class="ad-title">جوال iPhone 14 Pro Max جديد</h3>
                        <div class="ad-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>الدمام</span>
                        </div>
                        <div class="ad-date">
                            <i class="far fa-clock"></i>
                            <span>منذ أسبوع</span>
                        </div>
                        <p class="ad-price">5,200 ر.س</p>
                    </div>
                </div>

            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-links">
                    <a href="#">الشروط والأحكام</a>
                    <a href="#">سياسة الخصوصية</a>
                    <a href="#">خريطة الموقع</a>
                </div>
                <div class="social-links">
                    <a href="#"><i class="fab fa-facebook"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                    <a href="#"><i class="fab fa-linkedin"></i></a>
                </div>
                <div class="footer-bottom">
                    &copy; 2025 منصة الإعلانات السعودية. جميع الحقوق محفوظة.
                </div>
            </div>
        </div>
    </footer>

    <div class="chat-input-container">
        <div class="input-wrapper">
            <input type="text" placeholder="أرسل رسالة..." class="chat-input">
            <button class="send-button">
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
    </div>

    <style>
        .chat-input-container {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            background-color: #f0f0f0;
            padding: 10px;
            box-shadow: 0px -2px 5px rgba(0, 0, 0, 0.1);
        }

        .input-wrapper {
            display: flex;
            align-items: center;
            background-color: white;
            border-radius: 25px;
            padding: 5px 15px;
            border: 1px solid #ccc;
        }

        .chat-input {
            flex: 1;
            border: none;
            outline: none;
            padding: 8px;
            font-size: 16px;
        }

        .send-button {
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
    </style>
</body>
</html>