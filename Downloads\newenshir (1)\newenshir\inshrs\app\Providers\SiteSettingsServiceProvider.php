<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Schema;
use App\Models\SiteSetting;

class SiteSettingsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // التأكد من وجود الجدول قبل تحميل الإعدادات
        if (Schema::hasTable('site_settings')) {
            try {
                // تحميل جميع الإعدادات ومشاركتها مع جميع العروض
                $settings = SiteSetting::getAll();
                
                // مشاركة الإعدادات مع جميع العروض
                View::share('siteSettings', $settings);
                
                // مشاركة الإعدادات الشائعة كمتغيرات منفصلة
                View::share('siteName', SiteSetting::siteName());
                View::share('siteLogo', SiteSetting::siteLogo());
                View::share('siteFavicon', SiteSetting::siteFavicon());
                View::share('siteDescription', SiteSetting::siteDescription());
                View::share('contactEmail', SiteSetting::contactEmail());
                View::share('contactPhone', SiteSetting::contactPhone());
                View::share('primaryColor', SiteSetting::primaryColor());
                View::share('secondaryColor', SiteSetting::secondaryColor());
                
                // إعداد config للتطبيق
                config([
                    'app.name' => SiteSetting::siteName(),
                    'mail.from.address' => SiteSetting::contactEmail(),
                    'mail.from.name' => SiteSetting::siteName(),
                ]);
                
            } catch (\Exception $e) {
                // في حالة حدوث خطأ، استخدم القيم الافتراضية
                \Log::warning('خطأ في تحميل إعدادات الموقع: ' . $e->getMessage());
            }
        }
    }
}
