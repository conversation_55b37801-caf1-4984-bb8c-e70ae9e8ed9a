<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Http\RedirectResponse;

class Google<PERSON>uthController extends Controller
{
    /**
     * Redirect the user to the Google authentication page.
     */
    public function redirect(): RedirectResponse
    {
        return Socialite::driver('google')->redirect();
    }

    /**
     * Obtain the user information from Google.
     */
    public function callback()
    {
        $user = Socialite::driver('google')->user();

        // Check if the user's email already exists in your database
        $existingUser = User::where('email', $user->getEmail())->first();

        if ($existingUser) {
            // Log in the existing user
            Auth::login($existingUser);
        } else {
            // Create a new user
            $newUser = User::create([
                'name' => $user->getName(),
                'email' => $user->getEmail(),
                'password' => \Hash::make(\Str::random(24)), // Generate a random password
                'email_verified_at' => now(), // Mark email as verified
            ]);

            // Log in the new user
            Auth::login($newUser);
        }

        // Redirect to your desired page after login/registration
        return redirect()->intended('/dashboard'); // Change /dashboard to your desired redirect path
    }
}