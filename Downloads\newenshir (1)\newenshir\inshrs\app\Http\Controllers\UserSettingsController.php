<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use App\Models\User;

class UserSettingsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * عرض صفحة إعدادات المستخدم
     */
    public function index()
    {
        $user = Auth::user();

        // فك تشفير المستخدمين المحظورين - مع معالجة الأخطاء
        $blockedUsers = collect();

        try {
            if (isset($user->blocked_users) && $user->blocked_users) {
                $blockedUserIds = json_decode($user->blocked_users, true);
                if (is_array($blockedUserIds) && !empty($blockedUserIds)) {
                    $blockedUsers = User::whereIn('id', $blockedUserIds)->get(['id', 'name', 'email']);
                }
            }
        } catch (\Exception $e) {
            // في حالة حدوث خطأ، استخدم collection فارغ
            $blockedUsers = collect();
            Log::warning('خطأ في تحميل المستخدمين المحظورين: ' . $e->getMessage());
        }

        return view('user.settings.index', [
            'user' => $user,
            'blockedUsers' => $blockedUsers
        ]);
    }

    /**
     * تحديث معلومات الملف الشخصي
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
        ], [
            'name.required' => 'الاسم مطلوب',
            'name.max' => 'الاسم يجب أن يكون أقل من 255 حرف',
            'email.required' => 'البريد الإلكتروني مطلوب',
            'email.email' => 'البريد الإلكتروني غير صحيح',
            'email.unique' => 'البريد الإلكتروني مستخدم من قبل',
            'phone.max' => 'رقم الهاتف يجب أن يكون أقل من 20 رقم',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $user->update([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
            ]);

            return back()->with('success', 'تم تحديث معلومات الملف الشخصي بنجاح!');

        } catch (\Exception $e) {
            Log::error('خطأ في تحديث الملف الشخصي: ' . $e->getMessage());
            return back()->withErrors(['error' => 'حدث خطأ أثناء تحديث المعلومات. يرجى المحاولة مرة أخرى.']);
        }
    }

    /**
     * تحديث إعدادات الخصوصية
     */
    public function updatePrivacy(Request $request)
    {
        $user = Auth::user();

        try {
            // التحقق من وجود الحقول في قاعدة البيانات
            $columns = Schema::getColumnListing('users');

            $updateData = [];

            // إضافة الحقول فقط إذا كانت موجودة في قاعدة البيانات
            if (in_array('allow_messages', $columns)) {
                $updateData['allow_messages'] = $request->has('allow_messages');
            }
            if (in_array('allow_comments', $columns)) {
                $updateData['allow_comments'] = $request->has('allow_comments');
            }
            if (in_array('show_phone', $columns)) {
                $updateData['show_phone'] = $request->has('show_phone');
            }
            if (in_array('show_email', $columns)) {
                $updateData['show_email'] = $request->has('show_email');
            }
            if (in_array('show_online_status', $columns)) {
                $updateData['show_online_status'] = $request->has('show_online_status');
            }
            if (in_array('profile_public', $columns)) {
                $updateData['profile_public'] = $request->has('profile_public');
            }
            if (in_array('show_ads_count', $columns)) {
                $updateData['show_ads_count'] = $request->has('show_ads_count');
            }
            if (in_array('show_join_date', $columns)) {
                $updateData['show_join_date'] = $request->has('show_join_date');
            }
            if (in_array('searchable_profile', $columns)) {
                $updateData['searchable_profile'] = $request->has('searchable_profile');
            }
            if (in_array('show_in_suggestions', $columns)) {
                $updateData['show_in_suggestions'] = $request->has('show_in_suggestions');
            }
            if (in_array('privacy_updated_at', $columns)) {
                $updateData['privacy_updated_at'] = now();
            }

            if (empty($updateData)) {
                return back()->withErrors(['error' => 'يجب تشغيل Migration أولاً لإضافة حقول الخصوصية. يرجى تشغيل: php artisan migrate']);
            }

            $user->update($updateData);

            return back()->with('success', 'تم تحديث إعدادات الخصوصية بنجاح!');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'حدث خطأ أثناء تحديث الإعدادات: ' . $e->getMessage()]);
        }
    }

    /**
     * تحديث إعدادات الإشعارات
     */
    public function updateNotifications(Request $request)
    {
        $user = Auth::user();

        // لا نحتاج للتحقق من boolean لأننا نستخدم has() للـ checkboxes
        try {
            $user->update([
                'email_notifications' => $request->has('email_notifications'),
                'sms_notifications' => $request->has('sms_notifications'),
                'push_notifications' => $request->has('push_notifications'),
                'marketing_emails' => $request->has('marketing_emails'),
                'login_alerts' => $request->has('login_alerts'),
            ]);

            return back()->with('success', 'تم تحديث إعدادات الإشعارات بنجاح!');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'حدث خطأ أثناء تحديث الإعدادات: ' . $e->getMessage()]);
        }
    }

    /**
     * تحديث إعدادات المظهر واللغة
     */
    public function updatePreferences(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'preferred_language' => 'required|in:ar,en',
            'theme_preference' => 'required|in:light,dark,auto',
            'timezone' => 'required|string|max:50',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $user->update([
                'preferred_language' => $request->preferred_language,
                'theme_preference' => $request->theme_preference,
                'timezone' => $request->timezone,
            ]);

            return back()->with('success', 'تم تحديث التفضيلات بنجاح!');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'حدث خطأ أثناء تحديث التفضيلات: ' . $e->getMessage()]);
        }
    }

    /**
     * تحديث كلمة المرور
     */
    public function updatePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required',
            'new_password' => 'required|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $user = Auth::user();

        // التحقق من كلمة المرور الحالية
        if (!Hash::check($request->current_password, $user->password)) {
            return back()->withErrors(['current_password' => 'كلمة المرور الحالية غير صحيحة']);
        }

        try {
            $user->update([
                'password' => Hash::make($request->new_password),
            ]);

            return back()->with('success', 'تم تحديث كلمة المرور بنجاح!');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'حدث خطأ أثناء تحديث كلمة المرور: ' . $e->getMessage()]);
        }
    }

    /**
     * حظر مستخدم
     */
    public function blockUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        $user = Auth::user();
        $userToBlock = $request->user_id;

        // منع المستخدم من حظر نفسه
        if ($user->id == $userToBlock) {
            return back()->withErrors(['error' => 'لا يمكنك حظر نفسك']);
        }

        try {
            $blockedUsers = json_decode($user->blocked_users, true) ?? [];

            if (!in_array($userToBlock, $blockedUsers)) {
                $blockedUsers[] = $userToBlock;
                $user->update([
                    'blocked_users' => json_encode($blockedUsers)
                ]);
            }

            return back()->with('success', 'تم حظر المستخدم بنجاح!');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'حدث خطأ أثناء حظر المستخدم: ' . $e->getMessage()]);
        }
    }

    /**
     * إلغاء حظر مستخدم
     */
    public function unblockUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        $user = Auth::user();
        $userToUnblock = $request->user_id;

        try {
            $blockedUsers = json_decode($user->blocked_users, true) ?? [];
            $blockedUsers = array_filter($blockedUsers, function($id) use ($userToUnblock) {
                return $id != $userToUnblock;
            });

            $user->update([
                'blocked_users' => json_encode(array_values($blockedUsers))
            ]);

            return back()->with('success', 'تم إلغاء حظر المستخدم بنجاح!');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'حدث خطأ أثناء إلغاء حظر المستخدم: ' . $e->getMessage()]);
        }
    }

    /**
     * تفعيل/إلغاء تفعيل المصادقة الثنائية
     */
    public function toggleTwoFactor(Request $request)
    {
        $user = Auth::user();

        try {
            $user->update([
                'two_factor_enabled' => !$user->two_factor_enabled
            ]);

            $message = $user->two_factor_enabled ? 'تم تفعيل المصادقة الثنائية بنجاح!' : 'تم إلغاء تفعيل المصادقة الثنائية!';

            return back()->with('success', $message);

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'حدث خطأ أثناء تحديث المصادقة الثنائية: ' . $e->getMessage()]);
        }
    }

    /**
     * حذف الحساب
     */
    public function deleteAccount(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'password' => 'required',
            'confirmation' => 'required|in:DELETE',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        $user = Auth::user();

        // التحقق من كلمة المرور
        if (!Hash::check($request->password, $user->password)) {
            return back()->withErrors(['password' => 'كلمة المرور غير صحيحة']);
        }

        try {
            // حذف بيانات المستخدم المرتبطة (يمكن تخصيصها حسب الحاجة)
            // $user->ads()->delete();
            // $user->jobs()->delete();
            // $user->conversations()->delete();

            // حذف الحساب
            $user->delete();

            // تسجيل الخروج
            Auth::logout();

            return redirect('/')->with('success', 'تم حذف حسابك بنجاح');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'حدث خطأ أثناء حذف الحساب: ' . $e->getMessage()]);
        }
    }
}
