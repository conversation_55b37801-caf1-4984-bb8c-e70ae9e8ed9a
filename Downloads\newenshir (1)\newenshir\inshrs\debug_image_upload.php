<?php

// تشخيص مشكلة رفع الصور عند إنشاء إعلان جديد
// تشغيل: php debug_image_upload.php

require_once 'vendor/autoload.php';

// تحميل إعدادات Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Ad;
use App\Models\AdImage;
use App\Services\ImageService;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;

echo "🔍 تشخيص مشكلة رفع الصور\n";
echo "==========================\n\n";

try {
    // 1. فحص جدول ad_images
    echo "1️⃣ فحص جدول ad_images:\n";
    if (Schema::hasTable('ad_images')) {
        echo "   ✅ الجدول موجود\n";
        
        $columns = Schema::getColumnListing('ad_images');
        echo "   📋 الأعمدة: " . implode(', ', $columns) . "\n";
        
        $count = AdImage::count();
        echo "   📊 عدد الصور: {$count}\n";
        
        // فحص الصور الحديثة
        $recentImages = AdImage::latest()->take(5)->get();
        if ($recentImages->count() > 0) {
            echo "   📸 آخر 5 صور:\n";
            foreach ($recentImages as $image) {
                $hasData = !empty($image->data) ? 'نعم' : 'لا';
                $hasFile = !empty($image->image) ? 'نعم' : 'لا';
                echo "     - ID: {$image->id}, Ad: {$image->ad_id}, Data: {$hasData}, File: {$hasFile}\n";
            }
        }
    } else {
        echo "   ❌ الجدول غير موجود\n";
        exit(1);
    }

    // 2. فحص ImageService
    echo "\n2️⃣ فحص ImageService:\n";
    if (class_exists('App\Services\ImageService')) {
        echo "   ✅ ImageService موجود\n";
        
        try {
            $imageService = new ImageService();
            echo "   ✅ تم إنشاء instance بنجاح\n";
        } catch (Exception $e) {
            echo "   ❌ خطأ في إنشاء ImageService: " . $e->getMessage() . "\n";
        }
    } else {
        echo "   ❌ ImageService غير موجود\n";
    }

    // 3. فحص مكتبة Intervention Image
    echo "\n3️⃣ فحص مكتبة Intervention Image:\n";
    if (class_exists('Intervention\Image\ImageManager')) {
        echo "   ✅ مكتبة Intervention Image موجودة\n";
        
        try {
            $manager = new \Intervention\Image\ImageManager(new \Intervention\Image\Drivers\Gd\Driver());
            echo "   ✅ تم إنشاء ImageManager بنجاح\n";
        } catch (Exception $e) {
            echo "   ❌ خطأ في إنشاء ImageManager: " . $e->getMessage() . "\n";
        }
    } else {
        echo "   ❌ مكتبة Intervention Image غير موجودة\n";
    }

    // 4. فحص إعدادات PHP
    echo "\n4️⃣ فحص إعدادات PHP:\n";
    
    $uploadMaxFilesize = ini_get('upload_max_filesize');
    $postMaxSize = ini_get('post_max_size');
    $maxFileUploads = ini_get('max_file_uploads');
    $memoryLimit = ini_get('memory_limit');
    
    echo "   📁 upload_max_filesize: {$uploadMaxFilesize}\n";
    echo "   📦 post_max_size: {$postMaxSize}\n";
    echo "   🔢 max_file_uploads: {$maxFileUploads}\n";
    echo "   💾 memory_limit: {$memoryLimit}\n";
    
    // فحص امتدادات PHP المطلوبة
    $extensions = ['gd', 'fileinfo', 'mbstring'];
    foreach ($extensions as $ext) {
        if (extension_loaded($ext)) {
            echo "   ✅ امتداد {$ext} موجود\n";
        } else {
            echo "   ❌ امتداد {$ext} غير موجود\n";
        }
    }

    // 5. فحص الإعلانات الحديثة
    echo "\n5️⃣ فحص الإعلانات الحديثة:\n";
    
    $recentAds = Ad::latest()->take(5)->get();
    if ($recentAds->count() > 0) {
        echo "   📢 آخر 5 إعلانات:\n";
        foreach ($recentAds as $ad) {
            $imageCount = $ad->images()->count();
            echo "     - ID: {$ad->id}, العنوان: " . substr($ad->title, 0, 30) . "..., الصور: {$imageCount}\n";
            
            if ($imageCount > 0) {
                $images = $ad->images()->get();
                foreach ($images as $img) {
                    $size = !empty($img->data) ? round(strlen(base64_decode($img->data)) / 1024, 2) . 'KB' : 'غير محدد';
                    echo "       * صورة ID: {$img->id}, الحجم: {$size}\n";
                }
            }
        }
    } else {
        echo "   ⚠️ لا توجد إعلانات\n";
    }

    // 6. اختبار معالجة صورة تجريبية
    echo "\n6️⃣ اختبار معالجة صورة تجريبية:\n";
    
    // إنشاء صورة تجريبية صغيرة
    $testImagePath = sys_get_temp_dir() . '/test_image.jpg';
    
    try {
        // إنشاء صورة تجريبية بسيطة
        $image = imagecreate(100, 100);
        $backgroundColor = imagecolorallocate($image, 255, 255, 255);
        $textColor = imagecolorallocate($image, 0, 0, 0);
        imagestring($image, 5, 10, 40, 'TEST', $textColor);
        imagejpeg($image, $testImagePath, 90);
        imagedestroy($image);
        
        if (file_exists($testImagePath)) {
            echo "   ✅ تم إنشاء صورة تجريبية: " . filesize($testImagePath) . " بايت\n";
            
            // محاولة معالجة الصورة باستخدام ImageService
            if (isset($imageService)) {
                // محاكاة UploadedFile
                $finfo = finfo_open(FILEINFO_MIME_TYPE);
                $mimeType = finfo_file($finfo, $testImagePath);
                finfo_close($finfo);
                
                echo "   🔍 محاولة معالجة الصورة التجريبية...\n";
                echo "   📄 نوع الملف: {$mimeType}\n";
                
                // قراءة الصورة مباشرة (بدلاً من UploadedFile)
                try {
                    $manager = new \Intervention\Image\ImageManager(new \Intervention\Image\Drivers\Gd\Driver());
                    $processedImage = $manager->read($testImagePath);
                    
                    // تحويل إلى JPEG
                    $tempFile = tempnam(sys_get_temp_dir(), 'processed_img');
                    $processedImage->toJpeg(90)->save($tempFile);
                    $imageData = file_get_contents($tempFile);
                    unlink($tempFile);
                    
                    $base64Data = base64_encode($imageData);
                    $size = strlen($imageData) / 1024;
                    
                    echo "   ✅ تم معالجة الصورة بنجاح\n";
                    echo "   📊 الحجم بعد المعالجة: " . round($size, 2) . "KB\n";
                    echo "   📝 طول البيانات المُرمزة: " . strlen($base64Data) . " حرف\n";
                    
                } catch (Exception $e) {
                    echo "   ❌ خطأ في معالجة الصورة: " . $e->getMessage() . "\n";
                }
            }
            
            // حذف الصورة التجريبية
            unlink($testImagePath);
            echo "   🗑️ تم حذف الصورة التجريبية\n";
        } else {
            echo "   ❌ فشل في إنشاء صورة تجريبية\n";
        }
    } catch (Exception $e) {
        echo "   ❌ خطأ في اختبار الصورة التجريبية: " . $e->getMessage() . "\n";
    }

    // 7. فحص مجلد الصور
    echo "\n7️⃣ فحص مجلد الصور:\n";
    
    $imageDir = public_path('images/ads');
    if (is_dir($imageDir)) {
        echo "   ✅ مجلد الصور موجود: {$imageDir}\n";
        
        $files = glob($imageDir . '/*');
        echo "   📁 عدد الملفات: " . count($files) . "\n";
        
        if (count($files) > 0) {
            echo "   📸 آخر 3 ملفات:\n";
            $recentFiles = array_slice($files, -3);
            foreach ($recentFiles as $file) {
                $size = round(filesize($file) / 1024, 2);
                echo "     - " . basename($file) . " ({$size}KB)\n";
            }
        }
    } else {
        echo "   ⚠️ مجلد الصور غير موجود: {$imageDir}\n";
        
        // محاولة إنشاء المجلد
        try {
            mkdir($imageDir, 0755, true);
            echo "   ✅ تم إنشاء مجلد الصور\n";
        } catch (Exception $e) {
            echo "   ❌ فشل في إنشاء مجلد الصور: " . $e->getMessage() . "\n";
        }
    }

    // 8. فحص صلاحيات الكتابة
    echo "\n8️⃣ فحص صلاحيات الكتابة:\n";
    
    $writableDirectories = [
        storage_path('logs'),
        storage_path('app'),
        public_path('images'),
        sys_get_temp_dir()
    ];
    
    foreach ($writableDirectories as $dir) {
        if (is_writable($dir)) {
            echo "   ✅ قابل للكتابة: {$dir}\n";
        } else {
            echo "   ❌ غير قابل للكتابة: {$dir}\n";
        }
    }

    echo "\n🎯 خلاصة التشخيص:\n";
    echo "==================\n";
    
    // فحص المشاكل المحتملة
    $issues = [];
    
    if (!Schema::hasTable('ad_images')) {
        $issues[] = "جدول ad_images غير موجود";
    }
    
    if (!class_exists('App\Services\ImageService')) {
        $issues[] = "ImageService غير موجود";
    }
    
    if (!class_exists('Intervention\Image\ImageManager')) {
        $issues[] = "مكتبة Intervention Image غير مثبتة";
    }
    
    if (!extension_loaded('gd')) {
        $issues[] = "امتداد GD غير مثبت";
    }
    
    if (!is_dir(public_path('images/ads'))) {
        $issues[] = "مجلد الصور غير موجود";
    }
    
    if (!is_writable(storage_path('logs'))) {
        $issues[] = "مجلد logs غير قابل للكتابة";
    }
    
    if (empty($issues)) {
        echo "🎉 جميع المتطلبات متوفرة!\n\n";
        
        echo "🔧 خطوات استكشاف الأخطاء:\n";
        echo "1. جرب إنشاء إعلان جديد مع صورة\n";
        echo "2. تحقق من logs في storage/logs/laravel.log\n";
        echo "3. تحقق من جدول ad_images في قاعدة البيانات\n";
        echo "4. تحقق من Developer Tools في المتصفح\n\n";
        
        echo "📊 إحصائيات:\n";
        echo "- عدد الإعلانات: " . Ad::count() . "\n";
        echo "- عدد الصور: " . AdImage::count() . "\n";
        echo "- آخر إعلان: " . (Ad::latest()->first()->created_at ?? 'لا يوجد') . "\n";
        
    } else {
        echo "⚠️ مشاكل تحتاج إلى حل:\n";
        foreach ($issues as $issue) {
            echo "❌ {$issue}\n";
        }
        
        echo "\n🔧 حلول مقترحة:\n";
        echo "1. composer require intervention/image\n";
        echo "2. php artisan migrate\n";
        echo "3. mkdir -p public/images/ads\n";
        echo "4. chmod 755 public/images/ads\n";
    }

} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
    echo "\nتفاصيل الخطأ:\n";
    echo $e->getTraceAsString() . "\n";
}

echo "\n📚 للمزيد من المساعدة:\n";
echo "======================\n";
echo "- تحقق من storage/logs/laravel.log\n";
echo "- تحقق من إعدادات PHP (php.ini)\n";
echo "- تحقق من صلاحيات المجلدات\n";

?>
