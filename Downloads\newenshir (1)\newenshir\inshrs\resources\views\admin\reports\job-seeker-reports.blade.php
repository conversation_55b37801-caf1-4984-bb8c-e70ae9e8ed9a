@extends('layouts.admin')

@section('title', 'بلاغات الباحثين عن عمل')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">بلاغات الباحثين عن عمل</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.reports') }}" class="btn btn-default">
                            <i class="fas fa-arrow-right"></i> العودة للبلاغات
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    <!-- فلاتر البحث -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <form action="{{ route('admin.reports.job-seekers') }}" method="GET" class="form-inline">
                                <div class="form-group mx-1">
                                    <label for="status" class="mx-1">الحالة:</label>
                                    <select name="status" id="status" class="form-control">
                                        <option value="">الكل</option>
                                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>قيد المراجعة</option>
                                        <option value="reviewed" {{ request('status') == 'reviewed' ? 'selected' : '' }}>تمت المراجعة</option>
                                        <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>مرفوض</option>
                                    </select>
                                </div>
                                <div class="form-group mx-1">
                                    <label for="report_type" class="mx-1">نوع البلاغ:</label>
                                    <select name="report_type" id="report_type" class="form-control">
                                        <option value="">الكل</option>
                                        <option value="معلومات زائفة" {{ request('report_type') == 'معلومات زائفة' ? 'selected' : '' }}>معلومات زائفة</option>
                                        <option value="محتوى غير لائق" {{ request('report_type') == 'محتوى غير لائق' ? 'selected' : '' }}>محتوى غير لائق</option>
                                        <option value="احتيال أو نصب" {{ request('report_type') == 'احتيال أو نصب' ? 'selected' : '' }}>احتيال أو نصب</option>
                                        <option value="انتحال شخصية" {{ request('report_type') == 'انتحال شخصية' ? 'selected' : '' }}>انتحال شخصية</option>
                                        <option value="سبب آخر" {{ request('report_type') == 'سبب آخر' ? 'selected' : '' }}>سبب آخر</option>
                                    </select>
                                </div>
                                <div class="form-group mx-1">
                                    <label for="search" class="mx-1">بحث:</label>
                                    <input type="text" name="search" id="search" class="form-control" value="{{ request('search') }}" placeholder="بحث...">
                                </div>
                                <button type="submit" class="btn btn-primary mx-1">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                                <a href="{{ route('admin.reports.job-seekers') }}" class="btn btn-secondary mx-1">
                                    <i class="fas fa-redo"></i> إعادة تعيين
                                </a>
                            </form>
                        </div>
                    </div>

                    <!-- جدول البلاغات -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الباحث عن عمل</th>
                                    <th>المبلغ</th>
                                    <th>نوع البلاغ</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($reports as $report)
                                    <tr>
                                        <td>{{ $report->id }}</td>
                                        <td>
                                            <a href="{{ route('jobSeekers.show', $report->job_seeker_id) }}" target="_blank">
                                                {{ $report->jobSeeker->user->name ?? 'باحث عن عمل محذوف' }}
                                            </a>
                                        </td>
                                        <td>{{ $report->user->name ?? 'مستخدم محذوف' }}</td>
                                        <td>{{ $report->report_type }}</td>
                                        <td>
                                            @if($report->status == 'pending')
                                                <span class="badge badge-warning">قيد المراجعة</span>
                                            @elseif($report->status == 'reviewed')
                                                <span class="badge badge-success">تمت المراجعة</span>
                                            @elseif($report->status == 'rejected')
                                                <span class="badge badge-danger">مرفوض</span>
                                            @endif
                                        </td>
                                        <td>{{ $report->created_at->format('Y-m-d H:i') }}</td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-info" data-toggle="modal" data-target="#jobSeekerReportModal{{ $report->id }}">
                                                <i class="fas fa-eye"></i> عرض
                                            </button>
                                        </td>
                                    </tr>
                                    
                                    <!-- Modal -->
                                    <div class="modal fade" id="jobSeekerReportModal{{ $report->id }}" tabindex="-1" role="dialog" aria-labelledby="jobSeekerReportModalLabel{{ $report->id }}" aria-hidden="true">
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="jobSeekerReportModalLabel{{ $report->id }}">تفاصيل البلاغ #{{ $report->id }}</h5>
                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                        <span aria-hidden="true">&times;</span>
                                                    </button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <h6>معلومات البلاغ</h6>
                                                            <table class="table table-bordered">
                                                                <tr>
                                                                    <th>رقم البلاغ</th>
                                                                    <td>{{ $report->id }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <th>نوع البلاغ</th>
                                                                    <td>{{ $report->report_type }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <th>المبلغ</th>
                                                                    <td>{{ $report->user->name ?? 'مستخدم محذوف' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <th>تاريخ البلاغ</th>
                                                                    <td>{{ $report->created_at->format('Y-m-d H:i') }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <th>الحالة</th>
                                                                    <td>
                                                                        @if($report->status == 'pending')
                                                                            <span class="badge badge-warning">قيد المراجعة</span>
                                                                        @elseif($report->status == 'reviewed')
                                                                            <span class="badge badge-success">تمت المراجعة</span>
                                                                        @elseif($report->status == 'rejected')
                                                                            <span class="badge badge-danger">مرفوض</span>
                                                                        @endif
                                                                    </td>
                                                                </tr>
                                                                @if($report->reviewed_at)
                                                                <tr>
                                                                    <th>تاريخ المراجعة</th>
                                                                    <td>{{ $report->reviewed_at->format('Y-m-d H:i') }}</td>
                                                                </tr>
                                                                @endif
                                                            </table>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <h6>معلومات الباحث عن عمل</h6>
                                                            <table class="table table-bordered">
                                                                <tr>
                                                                    <th>رقم الباحث</th>
                                                                    <td>{{ $report->job_seeker_id }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <th>اسم الباحث</th>
                                                                    <td>{{ $report->jobSeeker->user->name ?? 'باحث عن عمل محذوف' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <th>الموقع</th>
                                                                    <td>{{ $report->jobSeeker->location ?? 'غير محدد' }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <th>رابط الملف</th>
                                                                    <td>
                                                                        <a href="{{ route('jobSeekers.show', $report->job_seeker_id) }}" target="_blank" class="btn btn-sm btn-primary">
                                                                            <i class="fas fa-external-link-alt"></i> عرض الملف
                                                                        </a>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="row mt-3">
                                                        <div class="col-12">
                                                            <div class="card">
                                                                <div class="card-header bg-light">
                                                                    <h6 class="mb-0">سبب البلاغ</h6>
                                                                </div>
                                                                <div class="card-body">
                                                                    {{ $report->reason }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    @if($report->admin_notes)
                                                    <div class="row mt-3">
                                                        <div class="col-12">
                                                            <div class="card">
                                                                <div class="card-header bg-light">
                                                                    <h6 class="mb-0">ملاحظات الإدارة</h6>
                                                                </div>
                                                                <div class="card-body">
                                                                    {{ $report->admin_notes }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    @endif
                                                    
                                                    <form action="{{ route('admin.reports.job-seekers.status', $report->id) }}" method="POST">
                                                        @csrf
                                                        <div class="form-group mt-3">
                                                            <label for="status">تحديث حالة البلاغ</label>
                                                            <select name="status" id="status" class="form-control">
                                                                <option value="pending" {{ $report->status == 'pending' ? 'selected' : '' }}>قيد المراجعة</option>
                                                                <option value="reviewed" {{ $report->status == 'reviewed' ? 'selected' : '' }}>تمت المراجعة</option>
                                                                <option value="rejected" {{ $report->status == 'rejected' ? 'selected' : '' }}>مرفوض</option>
                                                            </select>
                                                        </div>
                                                        <div class="form-group">
                                                            <label for="admin_notes">ملاحظات الإدارة</label>
                                                            <textarea name="admin_notes" id="admin_notes" rows="3" class="form-control">{{ $report->admin_notes }}</textarea>
                                                        </div>
                                                        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center">لا توجد بلاغات</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- الترقيم -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $reports->appends(request()->query())->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
