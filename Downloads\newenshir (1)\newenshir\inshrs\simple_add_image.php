<?php

require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;

echo "Adding test profile image...\n";

$user = User::first();
if (!$user) {
    echo "No users found\n";
    exit;
}

echo "User: {$user->name}\n";

// Simple 1x1 PNG image
$testImage = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

$user->updateProfileImage($testImage, 'png', 70);

$user = $user->fresh();

echo "Has image: " . ($user->hasProfileImage() ? 'Yes' : 'No') . "\n";
echo "Image type: " . ($user->profile_image_type ?? 'None') . "\n";
echo "Image size: " . ($user->profile_image_size ?? 'None') . "\n";

if ($user->hasProfileImage()) {
    echo "SUCCESS! Profile image added.\n";
    echo "Now go to /profile to see it.\n";
} else {
    echo "FAILED! Could not add profile image.\n";
}

?>
