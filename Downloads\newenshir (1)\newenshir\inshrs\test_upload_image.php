<?php

// ملف اختبار رفع الصورة الشخصية مباشرة
// تشغيل: php test_upload_image.php

require_once 'vendor/autoload.php';

// تحميل إعدادات Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\DB;

echo "🧪 اختبار رفع الصورة الشخصية مباشرة\n";
echo "=====================================\n\n";

try {
    // الحصول على أول مستخدم
    $user = User::first();
    if (!$user) {
        echo "❌ لا يوجد مستخدمون في قاعدة البيانات\n";
        exit;
    }

    echo "👤 المستخدم: {$user->name} (ID: {$user->id})\n";
    echo "📧 البريد: {$user->email}\n\n";

    // فحص الحالة الحالية
    echo "📊 الحالة الحالية:\n";
    echo "   hasProfileImage(): " . ($user->hasProfileImage() ? 'نعم' : 'لا') . "\n";
    echo "   profile_image: " . ($user->profile_image ? 'موجود (' . strlen($user->profile_image) . ' حرف)' : 'فارغ') . "\n";
    echo "   profile_image_type: " . ($user->profile_image_type ?? 'فارغ') . "\n";
    echo "   profile_image_size: " . ($user->profile_image_size ?? 'فارغ') . "\n";
    echo "   profile_image_updated_at: " . ($user->profile_image_updated_at ?? 'فارغ') . "\n\n";

    // إنشاء صورة تجريبية بسيطة
    echo "🎨 إنشاء صورة تجريبية...\n";
    
    // صورة PNG بسيطة 1x1 بكسل (أصغر صورة ممكنة)
    $testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    $imageType = 'png';
    $imageSize = strlen(base64_decode($testImageBase64));
    
    echo "   📸 نوع الصورة: {$imageType}\n";
    echo "   📏 حجم الصورة: {$imageSize} بايت\n";
    echo "   📝 طول Base64: " . strlen($testImageBase64) . " حرف\n\n";

    // الطريقة الأولى: استخدام updateProfileImage method
    echo "🔄 الطريقة الأولى: استخدام updateProfileImage method...\n";
    try {
        $user->updateProfileImage($testImageBase64, $imageType, $imageSize);
        echo "   ✅ تم استدعاء updateProfileImage بنجاح\n";
        
        // إعادة تحميل البيانات
        $user = $user->fresh();
        
        echo "   📊 النتيجة:\n";
        echo "      hasProfileImage(): " . ($user->hasProfileImage() ? 'نعم' : 'لا') . "\n";
        echo "      profile_image: " . ($user->profile_image ? 'موجود (' . strlen($user->profile_image) . ' حرف)' : 'فارغ') . "\n";
        echo "      profile_image_type: " . ($user->profile_image_type ?? 'فارغ') . "\n";
        echo "      profile_image_size: " . ($user->profile_image_size ?? 'فارغ') . "\n";
        
        if ($user->hasProfileImage()) {
            echo "   🎉 نجح! الصورة محفوظة بالطريقة الأولى\n\n";
        } else {
            echo "   ❌ فشل! الصورة لم تُحفظ بالطريقة الأولى\n\n";
            
            // الطريقة الثانية: استخدام update مباشرة
            echo "🔄 الطريقة الثانية: استخدام update مباشرة...\n";
            
            $result = $user->update([
                'profile_image' => $testImageBase64,
                'profile_image_type' => $imageType,
                'profile_image_size' => $imageSize,
                'profile_image_updated_at' => now(),
            ]);
            
            echo "   📊 نتيجة update(): " . ($result ? 'نجح' : 'فشل') . "\n";
            
            // إعادة تحميل البيانات
            $user = $user->fresh();
            
            echo "   📊 النتيجة:\n";
            echo "      hasProfileImage(): " . ($user->hasProfileImage() ? 'نعم' : 'لا') . "\n";
            echo "      profile_image: " . ($user->profile_image ? 'موجود (' . strlen($user->profile_image) . ' حرف)' : 'فارغ') . "\n";
            echo "      profile_image_type: " . ($user->profile_image_type ?? 'فارغ') . "\n";
            echo "      profile_image_size: " . ($user->profile_image_size ?? 'فارغ') . "\n";
            
            if ($user->hasProfileImage()) {
                echo "   🎉 نجح! الصورة محفوظة بالطريقة الثانية\n\n";
            } else {
                echo "   ❌ فشل! الصورة لم تُحفظ بالطريقة الثانية\n\n";
                
                // الطريقة الثالثة: استخدام DB مباشرة
                echo "🔄 الطريقة الثالثة: استخدام DB مباشرة...\n";
                
                $affected = DB::table('users')
                    ->where('id', $user->id)
                    ->update([
                        'profile_image' => $testImageBase64,
                        'profile_image_type' => $imageType,
                        'profile_image_size' => $imageSize,
                        'profile_image_updated_at' => now(),
                    ]);
                
                echo "   📊 عدد الصفوف المتأثرة: {$affected}\n";
                
                // إعادة تحميل البيانات
                $user = User::find($user->id);
                
                echo "   📊 النتيجة:\n";
                echo "      hasProfileImage(): " . ($user->hasProfileImage() ? 'نعم' : 'لا') . "\n";
                echo "      profile_image: " . ($user->profile_image ? 'موجود (' . strlen($user->profile_image) . ' حرف)' : 'فارغ') . "\n";
                echo "      profile_image_type: " . ($user->profile_image_type ?? 'فارغ') . "\n";
                echo "      profile_image_size: " . ($user->profile_image_size ?? 'فارغ') . "\n";
                
                if ($user->hasProfileImage()) {
                    echo "   🎉 نجح! الصورة محفوظة بالطريقة الثالثة\n\n";
                } else {
                    echo "   ❌ فشل! الصورة لم تُحفظ بأي طريقة\n\n";
                }
            }
        }
        
    } catch (Exception $e) {
        echo "   ❌ خطأ في updateProfileImage: " . $e->getMessage() . "\n\n";
    }

    // اختبار عرض الصورة
    if ($user->hasProfileImage()) {
        echo "🖼️ اختبار عرض الصورة...\n";
        
        $imageUrl = $user->getProfileImageUrl();
        echo "   🔗 رابط الصورة: " . substr($imageUrl, 0, 100) . "...\n";
        
        $imageSize = $user->getProfileImageSizeForHumans();
        echo "   📏 حجم الصورة: {$imageSize}\n";
        
        $defaultAvatar = $user->getDefaultAvatar();
        echo "   🎨 الصورة الافتراضية: " . substr($defaultAvatar, 0, 100) . "...\n\n";
        
        // اختبار حذف الصورة
        echo "🗑️ اختبار حذف الصورة...\n";
        try {
            $user->deleteProfileImage();
            echo "   ✅ تم استدعاء deleteProfileImage بنجاح\n";
            
            // إعادة تحميل البيانات
            $user = $user->fresh();
            
            echo "   📊 النتيجة بعد الحذف:\n";
            echo "      hasProfileImage(): " . ($user->hasProfileImage() ? 'نعم' : 'لا') . "\n";
            echo "      profile_image: " . ($user->profile_image ? 'موجود' : 'فارغ') . "\n";
            
            if (!$user->hasProfileImage()) {
                echo "   🎉 نجح! تم حذف الصورة\n\n";
            } else {
                echo "   ❌ فشل! الصورة لم تُحذف\n\n";
            }
            
        } catch (Exception $e) {
            echo "   ❌ خطأ في deleteProfileImage: " . $e->getMessage() . "\n\n";
        }
    }

    // فحص fillable attributes
    echo "🔍 فحص fillable attributes...\n";
    $fillable = $user->getFillable();
    $profileImageFields = ['profile_image', 'profile_image_type', 'profile_image_size', 'profile_image_updated_at'];
    
    foreach ($profileImageFields as $field) {
        if (in_array($field, $fillable)) {
            echo "   ✅ {$field} - في fillable\n";
        } else {
            echo "   ❌ {$field} - ليس في fillable\n";
        }
    }
    echo "\n";

    // فحص casts
    echo "🔄 فحص casts...\n";
    $casts = $user->getCasts();
    $profileImageCasts = ['profile_image_size', 'profile_image_updated_at'];
    
    foreach ($profileImageCasts as $field) {
        if (isset($casts[$field])) {
            echo "   ✅ {$field} - cast إلى {$casts[$field]}\n";
        } else {
            echo "   ❌ {$field} - ليس في casts\n";
        }
    }
    echo "\n";

    echo "🎯 خلاصة الاختبار:\n";
    echo "==================\n";
    
    if ($user->hasProfileImage()) {
        echo "✅ النظام يعمل بشكل صحيح!\n";
        echo "✅ يمكن رفع وحفظ الصور الشخصية\n";
        echo "✅ يمكن عرض الصور المحفوظة\n";
        echo "✅ يمكن حذف الصور الشخصية\n";
    } else {
        echo "❌ هناك مشكلة في النظام\n";
        echo "❌ لا يمكن حفظ الصور الشخصية\n";
        echo "🔧 يحتاج إلى مراجعة User Model أو قاعدة البيانات\n";
    }

} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
    echo "\nتفاصيل الخطأ:\n";
    echo $e->getTraceAsString() . "\n";
}

echo "\n📞 للمساعدة:\n";
echo "============\n";
echo "- تحقق من ملف storage/logs/laravel.log\n";
echo "- راجع User Model في app/Models/User.php\n";
echo "- تأكد من وجود الحقول في قاعدة البيانات\n";
echo "- اختبر النظام عبر /profile أو /test-profile-image\n";

?>
