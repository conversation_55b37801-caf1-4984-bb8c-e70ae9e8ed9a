<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>آخر الوظائف المضافة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .jobs-table {
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table-header {
            background-color: #f8f9fa;
            color: #2c3e50;
            font-weight: 700;
        }
        
        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        
        .status-active {
            background-color: #e8f5e9;
            color: #2e7d32;
        }
        
        .status-closed {
            background-color: #ffebee;
            color: #c62828;
        }
        
        .status-pending {
            background-color: #fff3e0;
            color: #ef6c00;
        }
        
        .action-btn {
            padding: 5px 10px;
            margin: 0 3px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            .table-responsive {
                overflow-x: auto;
            }
        }
    </style>
</head>
<body>

<div class="container-fluid py-5">
    <div class="container">
        <h2 class="mb-4 text-end">آخر الوظائف المضافة</h2>
        
        <div class="table-responsive jobs-table">
            <table class="table table-hover mb-0">
                <thead class="table-header">
                    <tr>
                        <th class="py-3">عنوان الوظيفة</th>
                        <th class="py-3">الشركة</th>
                        <th class="py-3">تاريخ النشر</th>
                        <th class="py-3">الحالة</th>
                        <th class="py-3 text-center">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>مطور ويب أمامي</td>
                        <td>شركة التقنية المبتكرة</td>
                        <td>2023-10-15</td>
                        <td>
                            <span class="status-badge status-active">نشطة</span>
                        </td>
                        <td class="text-center">
                            <button class="btn btn-sm btn-info action-btn">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-warning action-btn">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger action-btn">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    <!-- يمكن إضافة المزيد من الصفوف هنا -->
                    <tr>
                        <td>مصمم جرافيك</td>
                        <td>وكالة الإبداع الرقمي</td>
                        <td>2023-10-14</td>
                        <td>
                            <span class="status-badge status-closed">مغلقة</span>
                        </td>
                        <td class="text-center">
                            <!-- نفس الأزرار -->
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

</body>
</html>