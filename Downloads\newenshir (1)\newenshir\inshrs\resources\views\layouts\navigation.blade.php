<style>
    /* تعديل لتقريب القائمة المنسدلة أكثر */
    .user-dropdown-container {
        margin-left: 6rem; /* زيادة المسافة لتقريبها أكثر نحو الوسط */
    }
    @media (max-width: 640px) {
        .user-dropdown-container {
            margin-left: 0; /* إعادة تعيين الهامش في الشاشات الصغيرة */
        }
    }
    /* دعم RTL */
    body, nav {
        direction: rtl;
        text-align: right;
    }
    .space-x-8 > :not([hidden]) ~ :not([hidden]) {
        margin-right: 2rem; /* تعديل المسافات لـ RTL */
        margin-left: 0;
    }
    .ml-1 {
        margin-right: 0.25rem; /* تعديل لـ RTL */
        margin-left: 0;
    }

    /* أنماط الهيدر */
    nav.bg-white {
        background-color: #ffffff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .border-b.border-gray-100 {
        border-bottom: 1px solid #f3f4f6;
    }

    .max-w-7xl {
        max-width: 80rem;
    }

    .mx-auto {
        margin-left: auto;
        margin-right: auto;
    }

    .px-4 {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .sm\:px-6 {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }

    .lg\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem;
    }

    .flex {
        display: flex;
    }

    .justify-between {
        justify-content: space-between;
    }

    .h-16 {
        height: 4rem;
    }

    .shrink-0 {
        flex-shrink: 0;
    }

    .items-center {
        align-items: center;
    }

    .hidden {
        display: none;
    }

    .sm\:flex {
        display: flex;
    }

    .sm\:-my-px {
        margin-top: -1px;
        margin-bottom: -1px;
    }

    .sm\:mr-10 {
        margin-right: 2.5rem;
    }

    .text-gray-500 {
        color: #6b7280;
    }

    .hover\:text-gray-700:hover {
        color: #374151;
    }

    .bg-white {
        background-color: #ffffff;
    }

    .rounded-md {
        border-radius: 0.375rem;
    }

    .text-sm {
        font-size: 0.875rem;
    }

    .font-medium {
        font-weight: 500;
    }

    .transition {
        transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 150ms;
    }

    .ease-in-out {
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }

    .duration-150 {
        transition-duration: 150ms;
    }

    .focus\:outline-none:focus {
        outline: 2px solid transparent;
        outline-offset: 2px;
    }

    .inline-flex {
        display: inline-flex;
    }

    .border {
        border-width: 1px;
    }

    .border-transparent {
        border-color: transparent;
    }

    .px-3 {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .py-2 {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }

    .fill-current {
        fill: currentColor;
    }

    .h-4 {
        height: 1rem;
    }

    .w-4 {
        width: 1rem;
    }

    .text-gray-400 {
        color: #9ca3af;
    }

    .hover\:text-gray-500:hover {
        color: #6b7280;
    }

    .hover\:bg-gray-100:hover {
        background-color: #f3f4f6;
    }

    .focus\:bg-gray-100:focus {
        background-color: #f3f4f6;
    }

    .focus\:text-gray-500:focus {
        color: #6b7280;
    }

    .h-6 {
        height: 1.5rem;
    }

    .w-6 {
        width: 1.5rem;
    }

    .pt-2 {
        padding-top: 0.5rem;
    }

    .pb-3 {
        padding-bottom: 0.75rem;
    }

    .space-y-1 > :not([hidden]) ~ :not([hidden]) {
        margin-top: 0.25rem;
    }

    .pt-4 {
        padding-top: 1rem;
    }

    .pb-1 {
        padding-bottom: 0.25rem;
    }

    .border-t {
        border-top-width: 1px;
    }

    .border-gray-200 {
        border-color: #e5e7eb;
    }

    .px-4 {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .text-base {
        font-size: 1rem;
    }

    .text-gray-800 {
        color: #1f2937;
    }

    .mt-3 {
        margin-top: 0.75rem;
    }

    /* تنسيقات إضافية للهيدر */
    nav {
        font-family: 'Figtree', sans-serif;
    }

    .nav-link {
        color: #4a90e2;
        font-weight: 600;
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        transition: all 0.3s ease;
    }

    .nav-link:hover {
        background-color: #f3f4f6;
        color: #357abd;
    }

    .nav-link.active {
        color: #357abd;
        border-bottom: 2px solid #357abd;
    }
</style>
<nav x-data="{ open: false }" class="bg-white border-b border-gray-100">
    <!-- Primary Navigation Menu -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <div class="flex">
                <!-- Logo -->
                <div class="shrink-0 flex items-center">
                    <a href="{{ route('dashboard') }}">
                        <!-- <x-application-logo class="block h-9 w-auto fill-current text-gray-800" /> -->
                    </a>
                </div>

                <!-- Navigation Links -->
                <div class="hidden space-x-8 sm:-my-px sm:mr-10 sm:flex">
                    <x-nav-link :href="route('dashboard')" :active="request()->routeIs('dashboard')">
                        لوحة التحكم
                    </x-nav-link>
                </div>
            </div>

            <!-- Settings Dropdown -->
            <div class="hidden sm:flex sm:items-center user-dropdown-container">
                <x-dropdown align="right" width="48">
                    <x-slot name="trigger">
                        <button class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 bg-white hover:text-gray-700 focus:outline-none transition ease-in-out duration-150">
                            <div>{{ Auth::user()->name }}</div>
                            <div class="ml-1">
                                <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </div>
                        </button>
                    </x-slot>

                    <x-slot name="content">
                        <x-dropdown-link :href="route('profile.edit')">
                            الملف الشخصي
                        </x-dropdown-link>
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <x-dropdown-link :href="route('logout')"
                                    onclick="event.preventDefault(); this.closest('form').submit();">
                                تسجيل الخروج
                            </x-dropdown-link>
                        </form>
                    </x-slot>
                </x-dropdown>
            </div>

            <!-- Hamburger -->
            <div class="-ml-2 flex items-center sm:hidden">
                <button @click="open = ! open" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 focus:text-gray-500 transition duration-150 ease-in-out">
                    <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                        <path :class="{'hidden': open, 'inline-flex': ! open }" class="inline-flex" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        <path :class="{'hidden': ! open, 'inline-flex': open }" class="hidden" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Responsive Navigation Menu -->
    <div :class="{'block': open, 'hidden': ! open}" class="hidden sm:hidden">
        <div class="pt-2 pb-3 space-y-1">
            <x-responsive-nav-link :href="route('dashboard')" :active="request()->routeIs('dashboard')">
                لوحة التحكم
            </x-responsive-nav-link>
        </div>

        <!-- Responsive Settings Options -->
        <div class="pt-4 pb-1 border-t border-gray-200">
            <div class="px-4">
                <div class="font-medium text-base text-gray-800">{{ Auth::user()->name }}</div>
                <div class="font-medium text-sm text-gray-500">{{ Auth::user()->email }}</div>
            </div>

            <div class="mt-3 space-y-1">
                <x-responsive-nav-link :href="route('profile.edit')">
                    الملف الشخصي
                </x-responsive-nav-link>
                <form method="POST" action="{{ route('logout') }}">
                    @csrf
                    <x-responsive-nav-link :href="route('logout')"
                            onclick="event.preventDefault(); this.closest('form').submit();">
                        تسجيل الخروج
                    </x-responsive-nav-link>
                </form>
            </div>
        </div>
    </div>
</nav>
