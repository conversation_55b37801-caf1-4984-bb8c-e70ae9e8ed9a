/* ملف الأنماط المشتركة لجميع صفحات الموقع */

:root {
    /* الألوان الأساسية */
    --primary-color: #3AB0FF;
    --primary-light: #61C0FF;
    --primary-dark: #1D8DF0;
    --secondary-color: #F2F2F2;
    --accent-color: #E67E22; /* لون برتقالي غامق */

    /* ألوان النص والخلفية */
    --light-bg: #F8F9FA;
    --card-bg: #FFFFFF;
    --text-dark: #333333;
    --text-light: #6b7280;
    --text-white: #FFFFFF;
    --border-color: #E5E7EB;

    /* ألوان الحالات */
    --success-color: #6BCB77;
    --warning-color: #FFB347;
    --error-color: #FF6B6B;

    /* تأثيرات */
    --shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
}

/* أنماط الهيدر */
header {
    background: linear-gradient(to right, #1e293b, #0f172a);
    color: var(--text-white);
    padding: 1rem 0;
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
    overflow: hidden;
}

header::before {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(to right, var(--primary-light), var(--accent-color));
}

header::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(to left, var(--primary-light), var(--accent-color));
    z-index: 1;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.header-pattern {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.05) 0%, transparent 100%),
        radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.05) 0%, transparent 100%);
    z-index: 0;
}

/* أنماط القائمة */
nav ul {
    display: flex;
    list-style: none;
    gap: 1rem;
    margin: 0;
    padding: 0;
}

nav a {
    color: var(--text-white);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

nav a:hover, nav a.active {
    background-color: rgba(230, 126, 34, 0.2); /* لون برتقالي غامق بشفافية */
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

nav a i {
    font-size: 1.1rem;
    color: var(--accent-color); /* لون برتقالي غامق */
}

.nav-text {
    display: inline-block;
}

/* أنماط الفوتر */
footer {
    background: linear-gradient(to right, #1e293b, #0f172a);
    color: var(--text-white);
    padding: 4rem 0 2rem;
    margin-top: 4rem;
    position: relative;
    overflow: hidden;
}

footer::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(to right, var(--primary-light), var(--accent-color));
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 3rem;
}

.footer-column h3 {
    margin-bottom: 1.5rem;
    color: var(--text-white);
    font-size: 1.3rem;
    position: relative;
    padding-bottom: 0.75rem;
}

.footer-column h3::after {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 3px;
    background-color: var(--accent-color);
    border-radius: 2px;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: #cbd5e1;
    text-decoration: none;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-links a::before {
    content: "\f054";
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    font-size: 0.7rem;
    color: var(--accent-color);
}

.footer-links a:hover {
    color: var(--text-white);
    transform: translateX(-5px);
}

.footer-bottom {
    margin-top: 3rem;
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #334155;
    color: #94a3b8;
}

/* أنماط الأزرار */
.btn {
    padding: 0.6rem 1.25rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--text-white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-accent {
    background-color: var(--accent-color);
    color: var(--text-white);
}

.btn-accent:hover {
    background-color: #d35400;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-outline {
    background-color: transparent;
    color: var(--text-dark);
    border: 2px solid var(--border-color);
}

.btn-outline:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    /* تحسين الهيدر للجوال */
    header {
        padding: 0.5rem 0;
    }

    header::before, header::after {
        height: 2px; /* تقليل سماكة الحدود */
    }

    .header-content {
        flex-direction: row; /* تغيير من عمودي إلى أفقي */
        gap: 0.5rem;
        padding: 0;
    }

    nav {
        width: 100%;
    }

    nav ul {
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.25rem;
        margin: 0;
        padding: 0;
    }

    nav a {
        padding: 0.3rem 0.4rem;
        font-size: 0.75rem;
        min-height: 0;
    }

    nav a i {
        font-size: 0.9rem;
        margin-left: 0.1rem;
    }

    /* تقليل المسافة بين الأيقونة والنص */
    nav a {
        gap: 0.2rem;
    }

    /* إزالة التأثيرات الحركية في الجوال لتوفير المساحة */
    nav a:hover {
        transform: none;
        box-shadow: none;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}

/* تحسينات إضافية للشاشات الصغيرة جدًا */
@media (max-width: 480px) {
    header {
        padding: 0.3rem 0;
    }

    nav a {
        padding: 0.25rem 0.3rem;
        font-size: 0.7rem;
    }

    nav a i {
        font-size: 0.8rem;
    }

    nav ul {
        gap: 0.15rem;
    }

    /* عرض الأيقونات فقط في الشاشات الصغيرة جدًا مع تلميح نصي */
    .nav-text {
        font-size: 0.65rem;
    }

    /* تحسين مظهر العنصر النشط */
    nav a.active {
        background-color: rgba(230, 126, 34, 0.3);
        box-shadow: none;
        transform: none;
    }
}
