<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BasicInfo extends Model
{
    use HasFactory;

    // حدد الجدول إذا كان اسمه مختلفًا عن الاسم الافتراضي
    protected $table = 'basic_info';  // اسم الجدول في قاعدة البيانات

    // الحقول القابلة للتعيين (Mass Assignment)
    protected $fillable = [
        'full_name',
        'email',
        'created_at',
        'updated_at'
    ];


    
    // حدد العلاقة مع الموديلات الأخرى
    public function technicalSkills()
    {
        return $this->hasMany(TechnicalSkill::class);
    }

    public function workExperiences()
    {
        return $this->hasMany(WorkExperience::class);
    }

    public function languages()
    {
        return $this->hasMany(Language::class);
    }

    public function trainingCourses()
    {
        return $this->hasMany(TrainingCourse::class);
    }

    public function contactInfos()
    {
        return $this->hasMany(ContactInfo::class);
    }
}
