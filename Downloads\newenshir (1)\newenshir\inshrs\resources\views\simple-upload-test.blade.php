<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>اختبار رفع الصورة الشخصية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .current-image {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #007bff;
            margin: 20px auto;
            display: block;
        }
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            background: #f8f9fa;
            border-color: #0056b3;
        }
        .result-box {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .btn-custom {
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">🧪 اختبار رفع الصورة الشخصية</h1>
        
        <!-- معلومات المستخدم الحالي -->
        <div class="result-box">
            <h3>👤 معلومات المستخدم:</h3>
            <p><strong>الاسم:</strong> {{ Auth::user()->name }}</p>
            <p><strong>البريد:</strong> {{ Auth::user()->email }}</p>
            <p><strong>ID:</strong> {{ Auth::user()->id }}</p>
        </div>

        <!-- الصورة الحالية -->
        <div class="text-center">
            <h3>🖼️ الصورة الحالية:</h3>
            @if(Auth::user()->hasProfileImage())
                <img src="{{ Auth::user()->getProfileImageUrl() }}" alt="الصورة الشخصية" class="current-image" id="current-image">
                <p class="text-success">✅ يوجد صورة شخصية</p>
                <p><strong>النوع:</strong> {{ Auth::user()->profile_image_type }}</p>
                <p><strong>الحجم:</strong> {{ Auth::user()->getProfileImageSizeForHumans() }}</p>
                <p><strong>آخر تحديث:</strong> {{ Auth::user()->profile_image_updated_at ? Auth::user()->profile_image_updated_at->diffForHumans() : 'غير محدد' }}</p>
            @else
                <img src="{{ Auth::user()->getDefaultAvatar() }}" alt="صورة افتراضية" class="current-image" id="current-image">
                <p class="text-warning">⚠️ لا توجد صورة شخصية</p>
            @endif
        </div>

        <!-- منطقة رفع الصورة -->
        <div class="upload-area" onclick="document.getElementById('file-input').click()">
            <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
            <h4>انقر هنا لرفع صورة جديدة</h4>
            <p>أو اسحب الصورة هنا</p>
            <small class="text-muted">الأنواع المدعومة: JPG, PNG, GIF (حد أقصى 5MB)</small>
        </div>

        <input type="file" id="file-input" accept="image/*" style="display: none;">

        <!-- أزرار الاختبار -->
        <div class="text-center">
            <button type="button" class="btn btn-primary btn-custom" onclick="testUpload()">
                📤 اختبار الرفع
            </button>
            <button type="button" class="btn btn-danger btn-custom" onclick="deleteImage()">
                🗑️ حذف الصورة
            </button>
            <button type="button" class="btn btn-info btn-custom" onclick="refreshInfo()">
                🔄 تحديث المعلومات
            </button>
        </div>

        <!-- منطقة النتائج -->
        <div id="results" class="result-box" style="display: none;">
            <h3>📊 نتائج الاختبار:</h3>
            <div id="results-content"></div>
        </div>

        <!-- شريط التقدم -->
        <div id="progress-container" style="display: none;" class="mt-3">
            <div class="progress">
                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
            </div>
            <small class="text-muted">جاري رفع الصورة...</small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script>
        // رفع الصورة عند اختيارها
        document.getElementById('file-input').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                uploadImage(file);
            }
        });

        // رفع الصورة
        function uploadImage(file) {
            const formData = new FormData();
            formData.append('profile_image', file);
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

            // عرض شريط التقدم
            const progressContainer = document.getElementById('progress-container');
            const progressBar = progressContainer.querySelector('.progress-bar');
            progressContainer.style.display = 'block';
            progressBar.style.width = '0%';

            // محاكاة تقدم الرفع
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 30;
                if (progress > 90) progress = 90;
                progressBar.style.width = progress + '%';
            }, 200);

            fetch('{{ route("user.profile-image.upload") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                clearInterval(progressInterval);
                progressBar.style.width = '100%';
                
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                    showResults(data);
                    
                    if (data.success) {
                        // تحديث الصورة
                        document.getElementById('current-image').src = data.image_url;
                        
                        // إعادة تحميل الصفحة بعد ثانيتين
                        setTimeout(() => {
                            location.reload();
                        }, 2000);
                    }
                }, 500);
            })
            .catch(error => {
                clearInterval(progressInterval);
                progressContainer.style.display = 'none';
                console.error('Error:', error);
                showResults({
                    success: false,
                    message: 'حدث خطأ أثناء رفع الصورة'
                });
            });
        }

        // اختبار الرفع
        function testUpload() {
            // إنشاء صورة تجريبية
            const canvas = document.createElement('canvas');
            canvas.width = 100;
            canvas.height = 100;
            const ctx = canvas.getContext('2d');
            
            // رسم خلفية ملونة
            ctx.fillStyle = '#3498db';
            ctx.fillRect(0, 0, 100, 100);
            
            // رسم نص
            ctx.fillStyle = 'white';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('TEST', 50, 55);
            
            // تحويل إلى blob
            canvas.toBlob(function(blob) {
                const file = new File([blob], 'test-image.png', { type: 'image/png' });
                uploadImage(file);
            }, 'image/png');
        }

        // حذف الصورة
        function deleteImage() {
            if (!confirm('هل أنت متأكد من حذف الصورة الشخصية؟')) {
                return;
            }

            fetch('{{ route("user.profile-image.delete") }}', {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                showResults(data);
                
                if (data.success) {
                    // تحديث الصورة للافتراضية
                    document.getElementById('current-image').src = data.default_avatar;
                    
                    // إعادة تحميل الصفحة بعد ثانيتين
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showResults({
                    success: false,
                    message: 'حدث خطأ أثناء حذف الصورة'
                });
            });
        }

        // تحديث المعلومات
        function refreshInfo() {
            fetch('{{ route("user.profile-image.info") }}', {
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                showResults(data);
            })
            .catch(error => {
                console.error('Error:', error);
                showResults({
                    success: false,
                    message: 'حدث خطأ في الحصول على المعلومات'
                });
            });
        }

        // عرض النتائج
        function showResults(data) {
            const resultsContainer = document.getElementById('results');
            const resultsContent = document.getElementById('results-content');
            
            let html = '';
            
            if (data.success) {
                html += '<div class="alert alert-success">✅ ' + data.message + '</div>';
            } else {
                html += '<div class="alert alert-danger">❌ ' + data.message + '</div>';
            }
            
            if (data.debug) {
                html += '<h5>🔍 معلومات التشخيص:</h5>';
                html += '<ul>';
                for (const [key, value] of Object.entries(data.debug)) {
                    html += '<li><strong>' + key + ':</strong> ' + value + '</li>';
                }
                html += '</ul>';
            }
            
            if (data.user) {
                html += '<h5>👤 معلومات المستخدم:</h5>';
                html += '<ul>';
                for (const [key, value] of Object.entries(data.user)) {
                    html += '<li><strong>' + key + ':</strong> ' + value + '</li>';
                }
                html += '</ul>';
            }
            
            resultsContent.innerHTML = html;
            resultsContainer.style.display = 'block';
            
            // إخفاء النتائج بعد 10 ثوان
            setTimeout(() => {
                resultsContainer.style.display = 'none';
            }, 10000);
        }

        // Drag and drop
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.style.background = '#e3f2fd';
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.style.background = '';
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.style.background = '';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                uploadImage(files[0]);
            }
        });
    </script>
</body>
</html>
