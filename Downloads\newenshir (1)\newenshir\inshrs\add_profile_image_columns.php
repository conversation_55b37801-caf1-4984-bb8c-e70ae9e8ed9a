<?php

// ملف لإضافة حقول الصورة الشخصية مباشرة
// يمكن تشغيله من خلال: php add_profile_image_columns.php

require_once 'vendor/autoload.php';

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

// تحميل إعدادات Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "بدء إضافة حقول الصورة الشخصية...\n";

    // التحقق من وجود الحقول أولاً
    $columns = Schema::getColumnListing('users');
    
    if (in_array('profile_image', $columns)) {
        echo "✅ حقول الصورة الشخصية موجودة بالفعل!\n";
        
        // عرض معلومات الحقول الموجودة
        $profileImageColumns = array_filter($columns, function($column) {
            return strpos($column, 'profile_image') === 0;
        });
        
        echo "الحقول الموجودة:\n";
        foreach ($profileImageColumns as $column) {
            echo "- {$column}\n";
        }
        
        exit;
    }

    echo "إضافة حقول الصورة الشخصية...\n";

    // إضافة الحقول
    Schema::table('users', function (Blueprint $table) {
        // إضافة حقل الصورة الشخصية
        $table->longText('profile_image')->nullable()->comment('الصورة الشخصية (Base64)');
        $table->string('profile_image_type', 20)->nullable()->comment('نوع الصورة (jpeg, png, gif)');
        $table->integer('profile_image_size')->nullable()->comment('حجم الصورة بالبايت');
        $table->timestamp('profile_image_updated_at')->nullable()->comment('آخر تحديث للصورة الشخصية');
    });

    echo "✅ تم إضافة حقول الصورة الشخصية بنجاح!\n";

    // التحقق من إضافة الحقول
    $newColumns = Schema::getColumnListing('users');
    $addedColumns = array_diff($newColumns, $columns);
    
    echo "\nالحقول المضافة:\n";
    foreach ($addedColumns as $column) {
        echo "- {$column}\n";
    }

    // اختبار إضافة صورة تجريبية
    echo "\n🧪 اختبار النظام...\n";
    
    $user = \App\Models\User::first();
    if ($user) {
        echo "المستخدم التجريبي: {$user->name} (ID: {$user->id})\n";
        
        // اختبار helper methods
        echo "hasProfileImage(): " . ($user->hasProfileImage() ? 'نعم' : 'لا') . "\n";
        echo "getProfileImageUrl(): " . substr($user->getProfileImageUrl(), 0, 50) . "...\n";
        echo "getDefaultAvatar(): " . substr($user->getDefaultAvatar(), 0, 50) . "...\n";
        
        echo "✅ جميع الـ helper methods تعمل بشكل صحيح!\n";
    } else {
        echo "⚠️ لا يوجد مستخدمون في قاعدة البيانات للاختبار\n";
    }

    echo "\n🎉 النظام جاهز للاستخدام!\n";
    echo "\nيمكنك الآن:\n";
    echo "1. الذهاب إلى /user/settings\n";
    echo "2. النقر على تبويب 'الملف الشخصي'\n";
    echo "3. رفع صورة شخصية\n";
    echo "4. اختبار عرض الصورة في مختلف أجزاء الموقع\n";

} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    echo "\nحلول بديلة:\n";
    echo "1. تشغيل SQL مباشرة في قاعدة البيانات:\n";
    echo "   - افتح add_profile_image_columns.sql\n";
    echo "   - انسخ المحتوى وشغله في phpMyAdmin\n";
    echo "\n2. تشغيل Migration يدوياً:\n";
    echo "   php artisan migrate --path=database/migrations/2024_01_03_000000_add_profile_image_to_users_table.php\n";
    echo "\n3. إضافة الحقول يدوياً:\n";
    echo "   ALTER TABLE users ADD COLUMN profile_image LONGTEXT NULL;\n";
    echo "   ALTER TABLE users ADD COLUMN profile_image_type VARCHAR(20) NULL;\n";
    echo "   ALTER TABLE users ADD COLUMN profile_image_size INT NULL;\n";
    echo "   ALTER TABLE users ADD COLUMN profile_image_updated_at TIMESTAMP NULL;\n";
}

echo "\n📋 معلومات إضافية:\n";
echo "- الحد الأقصى لحجم الصورة: 5 ميجابايت\n";
echo "- الأنواع المدعومة: JPG, PNG, GIF\n";
echo "- يتم ضغط الصور الكبيرة تلقائياً\n";
echo "- الصور محفوظة في قاعدة البيانات كـ Base64\n";
echo "- الصور الافتراضية مولدة من UI Avatars\n";

?>
