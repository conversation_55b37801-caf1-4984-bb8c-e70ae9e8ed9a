<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JobCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'slug', 'name', 'icon', 'is_active'
    ];

    /**
     * العلاقة مع عناوين الوظائف
     */
    public function jobTitles()
    {
        return $this->hasMany(JobTitle::class);
    }

    /**
     * العلاقة مع الوظائف
     */
    public function jobs()
    {
        return $this->hasMany(JobPosting::class);
    }
}
