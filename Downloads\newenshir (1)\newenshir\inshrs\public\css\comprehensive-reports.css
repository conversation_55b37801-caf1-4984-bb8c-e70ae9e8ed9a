/* التقارير الشاملة - التصميم */

.comprehensive-reports-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header */
.reports-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-title i {
    font-size: 2rem;
    opacity: 0.9;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0.5rem 0 0 0;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    border: none;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background: #45a049;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-outline:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Section Titles */
.section-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-title i {
    color: #667eea;
}

/* Overview Stats */
.stats-overview {
    margin-bottom: 3rem;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.overview-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.overview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.overview-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.users-card::before { background: linear-gradient(90deg, #4CAF50, #45a049); }
.ads-card::before { background: linear-gradient(90deg, #FF9800, #F57C00); }
.jobs-card::before { background: linear-gradient(90deg, #2196F3, #1976D2); }
.seekers-card::before { background: linear-gradient(90deg, #9C27B0, #7B1FA2); }

.card-icon {
    width: 70px;
    height: 70px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
}

.users-card .card-icon { background: linear-gradient(135deg, #4CAF50, #45a049); }
.ads-card .card-icon { background: linear-gradient(135deg, #FF9800, #F57C00); }
.jobs-card .card-icon { background: linear-gradient(135deg, #2196F3, #1976D2); }
.seekers-card .card-icon { background: linear-gradient(135deg, #9C27B0, #7B1FA2); }

.card-content {
    flex: 1;
}

.card-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1;
}

.card-label {
    font-size: 1.1rem;
    color: #7f8c8d;
    margin: 0.5rem 0;
}

.card-trend {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.trend-value {
    font-weight: 600;
    color: #27ae60;
    background: rgba(39, 174, 96, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.9rem;
}

.trend-label {
    font-size: 0.9rem;
    color: #95a5a6;
}

/* Charts Section */
.charts-section {
    margin-bottom: 3rem;
}

.charts-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.chart-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.chart-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.chart-legend {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #7f8c8d;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.legend-item.ads .legend-color { background: #FF9800; }
.legend-item.jobs .legend-color { background: #2196F3; }
.legend-item.users .legend-color { background: #4CAF50; }

.chart-container {
    position: relative;
    height: 300px;
}

/* Detailed Stats */
.detailed-stats {
    margin-bottom: 3rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.stat-section {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.stat-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #ecf0f1;
}

.stat-title i {
    color: #667eea;
}

.stat-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #ecf0f1;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: #7f8c8d;
    font-weight: 500;
}

.stat-value {
    font-weight: 700;
    color: #2c3e50;
    font-size: 1.1rem;
}

.stat-value.featured {
    color: #f39c12;
}

.stat-value.warning {
    color: #e74c3c;
}

/* Tables */
.detailed-tables {
    margin-bottom: 3rem;
}

.tables-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.table-section {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.table-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.table-title i {
    color: #667eea;
}

.table-container {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: #f8f9fa;
    color: #2c3e50;
    font-weight: 600;
    padding: 1rem;
    text-align: right;
    border-bottom: 2px solid #ecf0f1;
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid #ecf0f1;
    color: #7f8c8d;
}

.user-info, .ad-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.user-name, .ad-title {
    font-weight: 600;
    color: #2c3e50;
}

.user-email {
    font-size: 0.9rem;
    color: #95a5a6;
}

.badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.badge-primary {
    background: #667eea;
    color: white;
}

.badge-featured {
    background: #f39c12;
    color: white;
}

.views-count {
    font-weight: 600;
    color: #2c3e50;
}

/* Additional Info */
.additional-info {
    margin-bottom: 3rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.info-section {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.info-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.info-title i {
    color: #667eea;
}

.salary-ranges, .location-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.salary-item, .location-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
}

.salary-range, .location-name {
    font-weight: 500;
    color: #2c3e50;
}

.salary-count, .location-count {
    font-weight: 600;
    color: #7f8c8d;
    font-size: 0.9rem;
}

.salary-bar {
    width: 100%;
    height: 6px;
    background: #ecf0f1;
    border-radius: 3px;
    margin-top: 0.5rem;
    overflow: hidden;
}

.salary-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Footer */
.report-footer {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.report-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #7f8c8d;
    font-weight: 500;
}

.report-date i {
    color: #667eea;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
    }

    .page-title {
        font-size: 2rem;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .overview-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .tables-grid {
        grid-template-columns: 1fr;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .comprehensive-reports-container {
        padding: 0;
    }

    .container {
        padding: 0 0.5rem;
    }

    .overview-card,
    .chart-card,
    .stat-section,
    .table-section,
    .info-section,
    .report-footer {
        padding: 1.5rem;
        margin: 0 0.5rem;
    }

    .card-number {
        font-size: 2rem;
    }

    .chart-container {
        height: 250px;
    }
}

/* Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.overview-card,
.chart-card,
.stat-section,
.table-section,
.info-section {
    animation: fadeInUp 0.6s ease-out;
}

.overview-card:nth-child(1) { animation-delay: 0.1s; }
.overview-card:nth-child(2) { animation-delay: 0.2s; }
.overview-card:nth-child(3) { animation-delay: 0.3s; }
.overview-card:nth-child(4) { animation-delay: 0.4s; }

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Print Styles */
@media print {
    .header-actions,
    .report-actions {
        display: none;
    }

    .comprehensive-reports-container {
        background: white;
    }

    .overview-card,
    .chart-card,
    .stat-section,
    .table-section,
    .info-section,
    .report-footer {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }

    .chart-container {
        height: 200px;
    }
}
