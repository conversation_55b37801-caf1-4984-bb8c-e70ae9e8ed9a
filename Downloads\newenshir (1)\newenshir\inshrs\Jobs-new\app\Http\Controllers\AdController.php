<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Ad;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class AdController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth')->except(['index', 'show']);
    }


    // عرض كل الإعلانات
    public function index(Request $request)
    {
        $query = Ad::query();

        // فلترة بالكلمة المفتاحية
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        // فلترة بالفئة والفئة الفرعية
        if ($request->filled('category')) {
            $query->where('category', $request->category);

            // إذا تم تحديد فئة فرعية
            if ($request->filled('subcategory')) {
                $query->where('subcategory', $request->subcategory);
            }
        }

        // فلترة بالموقع
        if ($request->filled('location')) {
            $query->where('location', $request->location);
        }

        // فلترة بالسعر
        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }

        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // ترتيب الإعلانات حسب الاختيار
        switch ($request->input('sort')) {
            case 'newest':
                $query->orderByRaw('
                    CASE
                        WHEN is_featured = 1 AND (featured_until IS NULL OR featured_until > NOW()) THEN 0
                        ELSE 1
                    END
                ')->latest();
                break;
            case 'oldest':
                $query->orderByRaw('
                    CASE
                        WHEN is_featured = 1 AND (featured_until IS NULL OR featured_until > NOW()) THEN 0
                        ELSE 1
                    END
                ')->oldest();
                break;
            case 'price_high':
                $query->orderByRaw('
                    CASE
                        WHEN is_featured = 1 AND (featured_until IS NULL OR featured_until > NOW()) THEN 0
                        ELSE 1
                    END
                ')->orderByDesc('price');
                break;
            case 'price_low':
                $query->orderByRaw('
                    CASE
                        WHEN is_featured = 1 AND (featured_until IS NULL OR featured_until > NOW()) THEN 0
                        ELSE 1
                    END
                ')->orderBy('price');
                break;
            case 'title_asc':
                $query->orderByRaw('
                    CASE
                        WHEN is_featured = 1 AND (featured_until IS NULL OR featured_until > NOW()) THEN 0
                        ELSE 1
                    END
                ')->orderBy('title');
                break;
            default:
                // الترتيب الافتراضي: المميزة النشطة أولاً ثم الأحدث
                $query->orderByRaw('
                    CASE
                        WHEN is_featured = 1 AND (featured_until IS NULL OR featured_until > NOW()) THEN 0
                        ELSE 1
                    END
                ')->latest();
                break;
        }

        $ads = $query->paginate(5)->appends($request->query());

        return view('ads.index', compact('ads'));
    }

    public function create()
    {
        return view('ads.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|string|max:100',
            'subcategory' => 'nullable|string|max:100',
            'location' => 'nullable|string|max:255',
            'price' => 'nullable|numeric',
            'phone' => 'nullable|string|max:50|regex:/^[0-9]+$/',
            'whatsapp' => 'nullable|string|max:50|regex:/^[0-9]+$/',
            'email' => 'nullable|email|max:255',
            'featured_days' => 'nullable|integer|min:1',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $user = auth()->user();
        $isFeatured = $request->has('is_featured');
        $days = $request->input('featured_days', 0);

        // التحقق من النقاط عند طلب إعلان مميز
        if ($isFeatured && $days > 0) {
            if ($user->points < $days) {
                return back()->withErrors(['featured_days' => 'ليس لديك نقاط كافية لتثبيت الإعلان لهذه المدة.'])->withInput();
            }

            // خصم النقاط
            // تحديث النقاط في قاعدة البيانات مباشرة
            DB::table('users')->where('id', $user->id)->decrement('points', $days);

            $validated['is_featured'] = true;
            $validated['featured_until'] = now()->addDays($days);
        } else {
            $validated['is_featured'] = false;
            $validated['featured_until'] = null;
        }

        // معالجة الصورة إذا تم تحميلها
        if ($request->hasFile('image')) {
            try {
                // التحقق من أن الملف صالح
                if (!$request->file('image')->isValid()) {
                    Log::error('Invalid image file uploaded');
                    return back()->withErrors(['image' => 'الملف غير صالح'])->withInput();
                }

                // إنشاء اسم فريد للصورة باستخدام الوقت والاسم الأصلي
                $imageName = time() . '_' . $request->file('image')->getClientOriginalName();

                // تخزين الصورة في مجلد ads داخل public/images
                $destinationPath = public_path('images/ads');

                // التأكد من وجود المجلد
                if (!is_dir($destinationPath)) {
                    mkdir($destinationPath, 0755, true);
                }

                $request->file('image')->move($destinationPath, $imageName);

                // التحقق من نجاح التخزين
                $fullPath = public_path('images/ads/' . $imageName);
                if (!file_exists($fullPath)) {
                    Log::error('Failed to store image');
                    return back()->withErrors(['image' => 'فشل في تخزين الصورة'])->withInput();
                }

                // تسجيل معلومات النجاح
                Log::info('Image stored successfully in public directory: ' . $fullPath);

                // تخزين مسار الصورة في قاعدة البيانات
                $validated['image'] = 'images/ads/' . $imageName;
            } catch (\Exception $e) {
                Log::error('Error uploading image: ' . $e->getMessage());
                return back()->withErrors(['image' => 'حدث خطأ أثناء رفع الصورة'])->withInput();
            }
        }

        $validated['user_id'] = $user->id;

        Ad::create($validated);

        return redirect()->route('ads.index')->with('success', 'تم نشر الإعلان بنجاح');
    }

    public function show($id)
    {
        $ad = Ad::findOrFail($id);

        // الحصول على إعلانات مشابهة من نفس الفئة والفئة الفرعية
        $query = Ad::where('category', $ad->category)
                  ->where('id', '!=', $ad->id);

        // إذا كان الإعلان له فئة فرعية، نبحث عن إعلانات من نفس الفئة الفرعية أولاً
        if ($ad->subcategory) {
            $query->where(function($q) use ($ad) {
                $q->where('subcategory', $ad->subcategory)
                  ->orWhereNull('subcategory');
            });
        }

        $similarAds = $query->latest()
                           ->take(3)
                           ->get();

        return view('ads.show', compact('ad', 'similarAds'));
    }

    public function edit($id)
    {
        $ad = Ad::findOrFail($id);

        if ($ad->user_id !== auth()->id()) {
            abort(403);
        }

        return view('ads.edit', compact('ad'));
    }

    public function update(Request $request, $id)
    {
        $ad = Ad::findOrFail($id);

        if ($ad->user_id !== auth()->id()) {
            abort(403);
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|string|max:100',
            'subcategory' => 'nullable|string|max:100',
            'location' => 'nullable|string|max:255',
            'price' => 'nullable|numeric',
            'phone' => 'nullable|string|max:50|regex:/^[0-9]+$/',
            'whatsapp' => 'nullable|string|max:50|regex:/^[0-9]+$/',
            'email' => 'nullable|email|max:255',
            'featured_days' => 'nullable|integer|min:1',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $user = auth()->user();
        $isFeatured = $request->has('is_featured');
        $days = $request->input('featured_days', 0);

        // إذا كان الإعلان سيصبح مميزًا ولم يكن مميزًا من قبل أو تم تمديد فترة التثبيت
        if ($isFeatured && $days > 0 && (!$ad->is_featured || $ad->featured_until < now())) {
            // التحقق من وجود نقاط كافية للمستخدم
            if ($user->points < $days) {
                return back()->withErrors(['featured_days' => 'ليس لديك نقاط كافية لتثبيت الإعلان لهذه المدة.'])->withInput();
            }

            // خصم النقاط وتحديث المستخدم
            // تحديث النقاط في قاعدة البيانات مباشرة
            DB::table('users')->where('id', $user->id)->decrement('points', $days);

            $validated['is_featured'] = true;
            $validated['featured_until'] = now()->addDays($days);
        } elseif (!$isFeatured) {
            $validated['is_featured'] = false;
            $validated['featured_until'] = null;
        } else {
            // الإعلان مميز بالفعل ولم يتم تغيير حالته
            unset($validated['is_featured']);
            unset($validated['featured_until']);
        }

        // معالجة الصورة إذا تم تحميلها
        if ($request->hasFile('image')) {
            try {
                // التحقق من أن الملف صالح
                if (!$request->file('image')->isValid()) {
                    Log::error('Invalid image file uploaded during update');
                    return back()->withErrors(['image' => 'الملف غير صالح'])->withInput();
                }

                // حذف الصورة القديمة إذا كانت موجودة
                if ($ad->image) {
                    // تحديد ما إذا كانت الصورة في المجلد القديم أو الجديد
                    if (strpos($ad->image, 'images/ads/') === 0) {
                        // الصورة في المجلد الجديد
                        $oldImagePath = public_path($ad->image);
                    } else {
                        // الصورة في المجلد القديم
                        $oldImagePath = storage_path('app/public/' . $ad->image);
                    }

                    if (file_exists($oldImagePath)) {
                        unlink($oldImagePath);
                        Log::info('Old image deleted: ' . $oldImagePath);
                    } else {
                        Log::warning('Old image not found for deletion: ' . $oldImagePath);
                    }
                }

                // إنشاء اسم فريد للصورة باستخدام الوقت والاسم الأصلي
                $imageName = time() . '_' . $request->file('image')->getClientOriginalName();

                // تخزين الصورة في مجلد ads داخل public/images
                $destinationPath = public_path('images/ads');

                // التأكد من وجود المجلد
                if (!is_dir($destinationPath)) {
                    mkdir($destinationPath, 0755, true);
                }

                $request->file('image')->move($destinationPath, $imageName);

                // التحقق من نجاح التخزين
                $fullPath = public_path('images/ads/' . $imageName);
                if (!file_exists($fullPath)) {
                    Log::error('Failed to store image during update');
                    return back()->withErrors(['image' => 'فشل في تخزين الصورة'])->withInput();
                }

                // تسجيل معلومات النجاح
                Log::info('Image updated successfully in public directory: ' . $fullPath);

                // تخزين مسار الصورة في قاعدة البيانات
                $validated['image'] = 'images/ads/' . $imageName;
            } catch (\Exception $e) {
                Log::error('Error updating image: ' . $e->getMessage());
                return back()->withErrors(['image' => 'حدث خطأ أثناء تحديث الصورة'])->withInput();
            }
        }

        $ad->update($validated);

        return redirect()->route('ads.index')->with('success', 'تم تعديل الإعلان بنجاح');
    }

    public function destroy($id)
    {
        $ad = Ad::findOrFail($id);

        if ($ad->user_id !== auth()->id()) {
            abort(403);
        }

        // حذف الصورة إذا كانت موجودة
        if ($ad->image) {
            // تحديد ما إذا كانت الصورة في المجلد القديم أو الجديد
            if (strpos($ad->image, 'images/ads/') === 0) {
                // الصورة في المجلد الجديد
                $imagePath = public_path($ad->image);
            } else {
                // الصورة في المجلد القديم
                $imagePath = storage_path('app/public/' . $ad->image);
            }

            if (file_exists($imagePath)) {
                unlink($imagePath);
                Log::info('Image deleted during ad deletion: ' . $imagePath);
            } else {
                Log::warning('Image not found for deletion during ad deletion: ' . $imagePath);
            }
        }

        $ad->delete();

        return redirect()->route('ads.index')->with('success', 'تم حذف الإعلان بنجاح');
    }

    /**
     * جلب الإعلانات القريبة من موقع المستخدم
     */
    public function nearby(Request $request)
    {
        // التحقق من وجود بيانات الموقع
        if (!$request->filled(['latitude', 'longitude'])) {
            return response()->json([
                'success' => false,
                'message' => 'يجب توفير إحداثيات الموقع'
            ]);
        }

        // الحصول على الإحداثيات
        $userLat = $request->latitude;
        $userLng = $request->longitude;
        $locationName = $request->location_name ?? 'موقعك الحالي';

        try {
            // استعلام لجلب الإعلانات وحساب المسافة
            // ملاحظة: هذا يفترض أن لديك أعمدة latitude و longitude في جدول الإعلانات
            // إذا لم تكن موجودة، ستحتاج إلى إضافتها أولاً

            // نستخدم صيغة Haversine لحساب المسافة بين نقطتين على سطح الكرة الأرضية
            $query = Ad::selectRaw("
                ads.*,
                (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance
            ", [$userLat, $userLng, $userLat])
            ->whereNotNull('latitude')
            ->whereNotNull('longitude')
            ->orderByRaw('
                CASE
                    WHEN is_featured = 1 AND (featured_until IS NULL OR featured_until > NOW()) THEN 0
                    ELSE 1
                END
            ')
            ->orderBy('distance');

            // تحديد نطاق المسافة (افتراضيًا 50 كم)
            $maxDistance = $request->input('max_distance', 50);
            $query->having('distance', '<=', $maxDistance);

            // تحديد عدد النتائج
            $limit = $request->input('limit', 20);
            $ads = $query->take($limit)->get();

            // تحويل البيانات إلى الشكل المطلوب للعرض
            $formattedAds = $ads->map(function($ad) {
                return [
                    'id' => $ad->id,
                    'title' => $ad->title,
                    'description' => $ad->description,
                    'description_short' => Str::limit($ad->description, 100),
                    'price' => $ad->price,
                    'location' => $ad->location,
                    'category' => $ad->category,
                    'subcategory' => $ad->subcategory,
                    'is_featured' => $ad->is_featured,
                    'image_url' => $ad->getImageUrl(),
                    'created_at_human' => $ad->created_at->diffForHumans(),
                    'user_name' => $ad->user->name ?? 'مستخدم',
                    'distance' => round($ad->distance, 1) // تقريب المسافة إلى رقم عشري واحد
                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'تم العثور على ' . count($formattedAds) . ' إعلان قريب من ' . $locationName,
                'ads' => $formattedAds
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching nearby ads: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الإعلانات القريبة: ' . $e->getMessage()
            ]);
        }
    }
}
