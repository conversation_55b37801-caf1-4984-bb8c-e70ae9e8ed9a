<template>
  <div class="special-ads-container" v-if="ads.length > 0">
    <div class="special-ads-wrapper">
      <div class="row">
        <div v-for="ad in ads" :key="ad.id" class="col-md-4 mb-4">
          <div class="special-ad-card">
            <a @click.prevent="handleAdClick(ad)" :href="ad.url || '#'" class="special-ad-link">
              <div class="special-ad-image">
                <img :src="getImageUrl(ad)" :alt="ad.title" class="img-fluid">
              </div>
              <div class="special-ad-title">
                <h5>{{ ad.title }}</h5>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SpecialAds',
  props: {
    position: {
      type: String,
      required: true,
      validator: value => ['top', 'middle', 'bottom', 'sidebar'].includes(value)
    }
  },
  data() {
    return {
      ads: []
    }
  },
  mounted() {
    this.fetchAds();
  },
  methods: {
    fetchAds() {
      axios.get(`/api/special-ads/${this.position}`)
        .then(response => {
          if (response.data.success) {
            this.ads = response.data.data;
          }
        })
        .catch(error => {
          console.error('Error fetching special ads:', error);
        });
    },
    getImageUrl(ad) {
      if (ad.image_id) {
        return `/images/${ad.image_id}?v=${new Date().getTime()}`;
      }
      return 'https://via.placeholder.com/800x400?text=إعلان+مميز';
    },
    handleAdClick(ad) {
      // تسجيل النقرة
      axios.post(`/api/special-ads/click/${ad.id}`)
        .then(response => {
          if (response.data.success && response.data.url) {
            // فتح الرابط في نافذة جديدة
            window.open(response.data.url, '_blank');
          }
        })
        .catch(error => {
          console.error('Error recording ad click:', error);
          // فتح الرابط حتى في حالة حدوث خطأ
          if (ad.url) {
            window.open(ad.url, '_blank');
          }
        });
    }
  }
}
</script>

<style scoped>
.special-ads-container {
  margin: 20px 0;
}

.special-ad-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background-color: #fff;
}

.special-ad-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.special-ad-image {
  height: 200px;
  overflow: hidden;
}

.special-ad-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.special-ad-card:hover .special-ad-image img {
  transform: scale(1.05);
}

.special-ad-title {
  padding: 10px 15px;
  text-align: center;
}

.special-ad-title h5 {
  margin: 0;
  font-weight: bold;
  color: #333;
}

.special-ad-link {
  display: block;
  text-decoration: none;
  color: inherit;
}

/* تخصيص للشريط الجانبي */
@media (min-width: 768px) {
  .sidebar .special-ad-card {
    margin-bottom: 15px;
  }
  
  .sidebar .special-ad-image {
    height: 150px;
  }
}
</style>
