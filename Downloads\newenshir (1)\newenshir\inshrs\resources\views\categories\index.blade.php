@extends('layouts.app')

@section('title', 'تصفح الفئات')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="text-center mb-10">
        <h1 class="text-3xl font-bold text-gray-800 mb-2">تصفح جميع الفئات</h1>
        <p class="text-gray-600">اختر من بين مجموعة واسعة من الفئات لتجد ما تبحث عنه</p>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        @foreach($categories as $category)
        <a href="{{ route('categories.show', $category->slug) }}" class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
            <div class="p-6 text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas {{ $category->icon }} text-blue-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">{{ $category->name }}</h3>
                <p class="text-sm text-gray-500">{{ $category->subcategories->count() }} فئة فرعية</p>
            </div>
        </a>
        @endforeach
    </div>
</div>
@endsection
