@extends('layouts.admin')

@section('content')
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-university me-2"></i> تفاصيل طلب التحويل البنكي #{{ $transfer->id }}</h5>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i> {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i> {{ session('error') }}
                        </div>
                    @endif

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">معلومات الطلب</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>رقم المعاملة:</span>
                                            <span class="fw-bold">{{ $transfer->transaction_id }}</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>المبلغ:</span>
                                            <span class="fw-bold">{{ $transfer->amount }} ريال</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>عدد النقاط:</span>
                                            <span class="fw-bold">{{ $transfer->points_amount }} نقطة</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>تاريخ الطلب:</span>
                                            <span class="fw-bold">{{ $transfer->created_at->format('Y-m-d H:i') }}</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>الحالة:</span>
                                            <span class="fw-bold">
                                                @if($transfer->status == 'PENDING')
                                                    <span class="badge bg-warning">قيد الانتظار</span>
                                                @elseif($transfer->status == 'COMPLETED')
                                                    <span class="badge bg-success">مكتمل</span>
                                                @elseif($transfer->status == 'REJECTED')
                                                    <span class="badge bg-danger">مرفوض</span>
                                                @else
                                                    <span class="badge bg-secondary">{{ $transfer->status }}</span>
                                                @endif
                                            </span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">معلومات المستخدم</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>اسم المستخدم:</span>
                                            <span class="fw-bold">{{ $transfer->user->name ?? 'غير معروف' }}</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>البريد الإلكتروني:</span>
                                            <span class="fw-bold">{{ $transfer->user->email ?? 'غير معروف' }}</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>رصيد النقاط الحالي:</span>
                                            <span class="fw-bold">{{ $transfer->user->points ?? 0 }} نقطة</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">إيصال التحويل البنكي</h6>
                        </div>
                        <div class="card-body text-center">
                            @php
                                $paymentDetails = json_decode($transfer->payment_details, true) ?? [];
                                $receiptPath = $paymentDetails['receipt_path'] ?? null;
                            @endphp

                            @if($receiptPath)
                                <img src="{{ asset('storage/' . $receiptPath) }}" alt="إيصال التحويل" class="img-fluid mb-3" style="max-height: 400px;">
                                <div>
                                    <a href="{{ asset('storage/' . $receiptPath) }}" class="btn btn-sm btn-primary" target="_blank">
                                        <i class="fas fa-external-link-alt me-1"></i> فتح الصورة في نافذة جديدة
                                    </a>
                                </div>
                            @else
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i> لا يوجد إيصال مرفق
                                </div>
                            @endif
                        </div>
                    </div>

                    @if(isset($paymentDetails['notes']) && !empty($paymentDetails['notes']))
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">ملاحظات المستخدم</h6>
                            </div>
                            <div class="card-body">
                                <p class="mb-0">{{ $paymentDetails['notes'] }}</p>
                            </div>
                        </div>
                    @endif

                    @if($transfer->status == 'PENDING')
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <form action="{{ route('admin.bank-transfers.approve', $transfer->id) }}" method="POST">
                                    @csrf
                                    <button type="submit" class="btn btn-success w-100">
                                        <i class="fas fa-check-circle me-2"></i> الموافقة على الطلب وإضافة النقاط
                                    </button>
                                </form>
                            </div>
                            <div class="col-md-6 mb-3">
                                <button type="button" class="btn btn-danger w-100" data-bs-toggle="modal" data-bs-target="#rejectModal">
                                    <i class="fas fa-times-circle me-2"></i> رفض الطلب
                                </button>
                            </div>
                        </div>
                    @elseif($transfer->status == 'COMPLETED')
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i> تمت الموافقة على هذا الطلب وإضافة النقاط للمستخدم.
                            @if($transfer->completed_at)
                                <br>
                                <small>تاريخ الموافقة: {{ \Carbon\Carbon::parse($transfer->completed_at)->format('Y-m-d H:i') }}</small>
                            @endif
                        </div>
                    @elseif($transfer->status == 'REJECTED')
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle me-2"></i> تم رفض هذا الطلب.
                            @if(isset($paymentDetails['rejection_reason']))
                                <br>
                                <strong>سبب الرفض:</strong> {{ $paymentDetails['rejection_reason'] }}
                            @endif
                            @if(isset($paymentDetails['rejected_at']))
                                <br>
                                <small>تاريخ الرفض: {{ \Carbon\Carbon::parse($paymentDetails['rejected_at'])->format('Y-m-d H:i') }}</small>
                            @endif
                        </div>
                    @endif

                    <div class="text-center mt-4">
                        <a href="{{ route('admin.bank-transfers.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i> العودة إلى قائمة الطلبات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Rejection Reason -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('admin.bank-transfers.reject', $transfer->id) }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title" id="rejectModalLabel">رفض طلب التحويل البنكي</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="rejection_reason" class="form-label">سبب الرفض:</label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="3" required></textarea>
                        <div class="form-text">يرجى توضيح سبب رفض طلب التحويل البنكي. سيتم إرسال هذا السبب للمستخدم.</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">تأكيد الرفض</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
