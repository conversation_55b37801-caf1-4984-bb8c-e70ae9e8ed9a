<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\ViewTrackingService;

class CleanOldAdViews extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ads:clean-old-views {--days=30 : Number of days to keep views}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean old ad views to keep database size manageable';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $days = $this->option('days');
        
        $this->info("🧹 تنظيف المشاهدات الأقدم من {$days} يوم...");
        
        $viewTrackingService = app(ViewTrackingService::class);
        $deletedCount = $viewTrackingService->cleanOldViews($days);
        
        if ($deletedCount > 0) {
            $this->info("✅ تم حذف {$deletedCount} مشاهدة قديمة");
        } else {
            $this->info("ℹ️ لا توجد مشاهدات قديمة للحذف");
        }
        
        return 0;
    }
}
