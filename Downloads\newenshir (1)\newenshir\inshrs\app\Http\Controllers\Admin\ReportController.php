<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AdReport;
use App\Models\JobReport;
use App\Models\JobSeekerReport;
use Illuminate\Http\Request;

class ReportController extends Controller
{
    /**
     * عرض صفحة البلاغات
     */
    public function index()
    {
        // طريقة تصحيح بسيطة للتحقق من المشكلة
        try {
            // جلب إحصائيات البلاغات
            $stats = [
                'adReports' => [
                    'total' => 0,
                    'pending' => 0,
                    'reviewed' => 0,
                    'rejected' => 0,
                ],
                'jobReports' => [
                    'total' => 0,
                    'pending' => 0,
                    'reviewed' => 0,
                    'rejected' => 0,
                ],
                'jobSeekerReports' => [
                    'total' => 0,
                    'pending' => 0,
                    'reviewed' => 0,
                    'rejected' => 0,
                ],
            ];

            // حساب الإجماليات
            $totals = [
                'total' => 0,
                'pending' => 0,
                'reviewed' => 0,
                'rejected' => 0,
            ];

            // جلب البلاغات المعلقة
            $pendingAdReports = collect([]);
            $pendingJobReports = collect([]);
            $pendingJobSeekerReports = collect([]);

            // التحقق من وجود الصفحة
            if (!view()->exists('admin.reports.index')) {
                return response()->view('admin.dashboard', [
                    'error' => 'صفحة البلاغات غير موجودة. يرجى التحقق من وجود الملف admin/reports/index.blade.php'
                ], 404);
            }

            return view('admin.reports.index', compact(
                'stats',
                'totals',
                'pendingAdReports',
                'pendingJobReports',
                'pendingJobSeekerReports'
            ));
        } catch (\Exception $e) {
            // إرجاع رسالة الخطأ
            return response()->json([
                'error' => 'حدث خطأ: ' . $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * عرض بلاغات الإعلانات
     */
    public function adReports()
    {
        $reports = AdReport::with(['ad', 'user'])->latest()->paginate(15);
        return view('admin.reports.ad-reports', compact('reports'));
    }

    /**
     * عرض بلاغات الوظائف
     */
    public function jobReports()
    {
        $reports = JobReport::with(['job', 'user'])->latest()->paginate(15);
        return view('admin.reports.job-reports', compact('reports'));
    }

    /**
     * عرض بلاغات الباحثين عن عمل
     */
    public function jobSeekerReports()
    {
        $reports = JobSeekerReport::with(['jobSeeker', 'user'])->latest()->paginate(15);
        return view('admin.reports.job-seeker-reports', compact('reports'));
    }

    /**
     * تحديث حالة بلاغ إعلان
     */
    public function updateAdReportStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:pending,reviewed,rejected',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $report = AdReport::findOrFail($id);
        $report->status = $request->status;
        $report->admin_notes = $request->admin_notes;

        if ($request->status !== 'pending') {
            $report->reviewed_at = now();
        }

        $report->save();

        return back()->with('success', 'تم تحديث حالة البلاغ بنجاح.');
    }

    /**
     * تحديث حالة بلاغ وظيفة
     */
    public function updateJobReportStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:pending,reviewed,rejected',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $report = JobReport::findOrFail($id);
        $report->status = $request->status;
        $report->admin_notes = $request->admin_notes;

        if ($request->status !== 'pending') {
            $report->reviewed_at = now();
        }

        $report->save();

        return back()->with('success', 'تم تحديث حالة البلاغ بنجاح.');
    }

    /**
     * تحديث حالة بلاغ باحث عن عمل
     */
    public function updateJobSeekerReportStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:pending,reviewed,rejected',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $report = JobSeekerReport::findOrFail($id);
        $report->status = $request->status;
        $report->admin_notes = $request->admin_notes;

        if ($request->status !== 'pending') {
            $report->reviewed_at = now();
        }

        $report->save();

        return back()->with('success', 'تم تحديث حالة البلاغ بنجاح.');
    }
}
