<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رسالة نجاح البلاغ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
            text-align: right;
        }
        .container {
            max-width: 1000px;
            margin: 2rem auto;
        }
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        .demo-alert {
            border-radius: 0.75rem;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
            animation: slideIn 0.5s ease-out;
        }
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .report-form {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .btn {
            border-radius: 50rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        .form-control, .form-select {
            border-radius: 0.5rem;
            border: 2px solid #e9ecef;
            padding: 0.75rem;
        }
        .form-control:focus, .form-select:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
        .success-demo {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 2px solid #28a745;
            color: #155724;
        }
        .error-demo {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border: 2px solid #dc3545;
            color: #721c24;
        }
        .warning-demo {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #ffc107;
            color: #856404;
        }
        .info-demo {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border: 2px solid #17a2b8;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-flag me-2"></i>
                    اختبار رسالة "تم إرسال البلاغ بنجاح"
                </h4>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>تم تنفيذ المهمة بنجاح!</strong>
                    <br>
                    <small>تمت إضافة رسائل النجاح لجميع أنواع البلاغات في ReportController وصفحات العرض.</small>
                </div>
            </div>
        </div>

        <!-- عرض أنواع الرسائل المختلفة -->
        <div class="row mb-4">
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-check-circle me-2 text-success"></i>
                    رسالة نجاح البلاغ
                </h5>
                <div class="demo-alert success-demo">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle me-2" style="font-size: 1.5rem;"></i>
                        <div>
                            <strong>تم بنجاح!</strong>
                            <br>
                            تم إرسال البلاغ بنجاح، سيتم مراجعته من قبل الإدارة.
                        </div>
                        <button class="btn-close ms-auto" onclick="this.parentElement.parentElement.style.display='none'"></button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-exclamation-triangle me-2 text-danger"></i>
                    رسالة خطأ
                </h5>
                <div class="demo-alert error-demo">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle me-2" style="font-size: 1.5rem;"></i>
                        <div>
                            <strong>خطأ!</strong>
                            <br>
                            حدث خطأ أثناء إرسال البلاغ. يرجى المحاولة مرة أخرى.
                        </div>
                        <button class="btn-close ms-auto" onclick="this.parentElement.parentElement.style.display='none'"></button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-exclamation-circle me-2 text-warning"></i>
                    رسالة تحذير
                </h5>
                <div class="demo-alert warning-demo">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-circle me-2" style="font-size: 1.5rem;"></i>
                        <div>
                            <strong>تحذير!</strong>
                            <br>
                            يرجى ملء جميع الحقول المطلوبة قبل الإرسال.
                        </div>
                        <button class="btn-close ms-auto" onclick="this.parentElement.parentElement.style.display='none'"></button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2 text-info"></i>
                    رسالة معلومات
                </h5>
                <div class="demo-alert info-demo">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle me-2" style="font-size: 1.5rem;"></i>
                        <div>
                            <strong>معلومة!</strong>
                            <br>
                            سيتم مراجعة البلاغ من قبل فريق الإدارة خلال 24-48 ساعة.
                        </div>
                        <button class="btn-close ms-auto" onclick="this.parentElement.parentElement.style.display='none'"></button>
                    </div>
                </div>
            </div>
        </div>

        <!-- نموذج البلاغ التجريبي -->
        <div class="report-form">
            <h5 class="mb-4">
                <i class="fas fa-flag me-2"></i>
                نموذج البلاغ التجريبي
            </h5>
            
            <form id="reportForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="report_type" class="form-label">نوع البلاغ</label>
                            <select name="report_type" id="report_type" class="form-select" required>
                                <option value="">اختر نوع البلاغ</option>
                                <option value="معلومات زائفة">معلومات زائفة</option>
                                <option value="محتوى غير لائق">محتوى غير لائق</option>
                                <option value="احتيال أو نصب">احتيال أو نصب</option>
                                <option value="إعلان مضلل">إعلان مضلل</option>
                                <option value="سبب آخر">سبب آخر</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="item_type" class="form-label">نوع العنصر</label>
                            <select name="item_type" id="item_type" class="form-select" required>
                                <option value="">اختر نوع العنصر</option>
                                <option value="ad">إعلان</option>
                                <option value="job">وظيفة</option>
                                <option value="job_seeker">باحث عن عمل</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="reason" class="form-label">تفاصيل البلاغ</label>
                    <textarea name="reason" id="reason" rows="4" class="form-control" placeholder="يرجى توضيح سبب البلاغ بالتفصيل..." required></textarea>
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> سيتم مراجعة البلاغ من قبل فريق الإدارة، وسيتم اتخاذ الإجراء المناسب.
                </div>
                
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-paper-plane me-1"></i>
                        إرسال البلاغ
                    </button>
                    <button type="reset" class="btn btn-outline-secondary">
                        <i class="fas fa-undo me-1"></i>
                        إعادة تعيين
                    </button>
                </div>
            </form>
        </div>

        <!-- معلومات إضافية -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <h5>بلاغات الإعلانات</h5>
                        <p class="mb-0">رسالة نجاح مضافة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-briefcase fa-3x mb-3"></i>
                        <h5>بلاغات الوظائف</h5>
                        <p class="mb-0">رسالة نجاح مضافة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-user-tie fa-3x mb-3"></i>
                        <h5>بلاغات الباحثين</h5>
                        <p class="mb-0">رسالة نجاح مضافة</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <strong>تم تنفيذ المهمة السابعة بنجاح!</strong>
                <br>
                <small>تم إضافة رسالة "تم إرسال البلاغ بنجاح" لجميع أنواع البلاغات.</small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('reportForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // محاكاة إرسال البلاغ
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // تغيير النص لإظهار التحميل
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري الإرسال...';
            submitBtn.disabled = true;
            
            setTimeout(() => {
                // إظهار رسالة النجاح
                const successAlert = document.createElement('div');
                successAlert.className = 'alert alert-success alert-dismissible fade show';
                successAlert.innerHTML = `
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>تم بنجاح!</strong> تم إرسال البلاغ بنجاح، سيتم مراجعته من قبل الإدارة.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                
                // إدراج الرسالة في أعلى النموذج
                this.insertBefore(successAlert, this.firstChild);
                
                // إعادة تعيين النموذج
                this.reset();
                
                // إعادة تعيين الزر
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                
                // التمرير لأعلى لرؤية الرسالة
                successAlert.scrollIntoView({ behavior: 'smooth', block: 'center' });
                
            }, 2000);
        });
    </script>
</body>
</html>
