<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الوظائف والإعلانات والباحثين عن عمل</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* تحسينات إضافية للتصميم */
        .section-header {
            position: relative;
            padding-right: 15px;
            margin-bottom: 1.5rem;
        }

        .section-header::before {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            height: 100%;
            width: 5px;
            background: linear-gradient(to bottom, #3b82f6, #60a5fa);
            border-radius: 3px;
        }

        .action-button {
            transition: all 0.3s ease;
        }

        .action-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
    </style>
</head>
<body class="bg-gray-100">

<main class="container mx-auto px-4 py-6">
    <!-- شريط العنوان والأزرار الرئيسية -->
    <div class="mb-8 flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
            <h1 class="text-2xl md:text-3xl font-bold text-gray-800 mb-2">
                <i class="fas fa-tasks text-blue-600 ml-2"></i> إدارة المحتوى الخاص بك
            </h1>
            <p class="text-gray-600">يمكنك إدارة الوظائف والإعلانات وطلبات التوظيف من هنا</p>
        </div>

        <div class="flex flex-wrap gap-3">
            <a href="{{ route('jobs.create') }}" class="action-button bg-blue-600 hover:bg-blue-700 text-white px-5 py-2 rounded-lg shadow-md transition duration-300 flex items-center">
                <i class="fas fa-briefcase ml-2"></i> إضافة وظيفة
            </a>
            <a href="{{ route('ads.create') }}" class="action-button bg-green-600 hover:bg-green-700 text-white px-5 py-2 rounded-lg shadow-md transition duration-300 flex items-center">
                <i class="fas fa-bullhorn ml-2"></i> إضافة إعلان
            </a>
            <a href="{{ route('job_seekers.create') }}" class="action-button bg-purple-600 hover:bg-purple-700 text-white px-5 py-2 rounded-lg shadow-md transition duration-300 flex items-center">
                <i class="fas fa-user-plus ml-2"></i> طلب توظيف
            </a>
            <a href="javascript:history.back()" class="action-button bg-gray-500 hover:bg-gray-600 text-white px-5 py-2 rounded-lg shadow-md transition duration-300 flex items-center">
                <i class="fas fa-arrow-right ml-2"></i> رجوع
            </a>
        </div>
    </div>

    <!-- شريط التنقل بين الأقسام -->
    <div class="bg-white rounded-lg shadow-md p-4 mb-8">
        <div class="flex flex-wrap gap-4 justify-center">
            <button onclick="showSection('jobs')" class="tab-button px-5 py-2 rounded-lg bg-blue-100 text-blue-800 hover:bg-blue-200 transition">
                <i class="fas fa-briefcase ml-1"></i> الوظائف
            </button>
            <button onclick="showSection('jobSeekers')" class="tab-button px-5 py-2 rounded-lg bg-purple-100 text-purple-800 hover:bg-purple-200 transition">
                <i class="fas fa-user-tie ml-1"></i> الباحثين عن عمل
            </button>
            <button onclick="showSection('ads')" class="tab-button px-5 py-2 rounded-lg bg-green-100 text-green-800 hover:bg-green-200 transition">
                <i class="fas fa-ad ml-1"></i> الإعلانات
            </button>
        </div>
    </div>

<!-- قسم الباحثين عن عمل -->
<div id="jobSeekers-section" class="section-content">
    <div class="section-header">
        <h2 class="text-2xl font-bold text-gray-800">
            <i class="fas fa-user-tie ml-2 text-purple-600"></i>
            الباحثين عن عمل
        </h2>
    </div>

    @if($myjobSeeker->isEmpty())
        <div class="text-center bg-white shadow-lg rounded-xl p-10 max-w-xl mx-auto">
            <div class="text-6xl mb-4">🔍</div>
            <h2 class="text-2xl font-bold text-gray-700 mb-3">لا يوجد باحثين عن عمل</h2>
            <p class="text-gray-500 mb-6">لم تقم بإضافة أي طلبات توظيف حتى الآن</p>
            <a href="{{ route('job_seekers.create') }}" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg shadow-md transition duration-300">
                <i class="fas fa-plus ml-2"></i> إضافة طلب توظيف
            </a>
        </div>
    @else
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            @foreach($myjobSeeker as $seeker)
                <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition duration-300 card-hover">
                    <!-- معلومات الباحث عن عمل -->
                    <div class="p-4 bg-purple-50 border-b border-gray-200 flex items-center">
                        <div class="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center text-white">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <h3 class="text-lg font-bold text-purple-700 mr-3">{{ $seeker->user->name ?? 'غير معروف' }}</h3>
                    </div>

                    <!-- رأس البطاقة -->
                    <div class="p-6 relative">
                        <span class="absolute top-2 left-2 bg-purple-500 text-white px-3 py-1 rounded-full text-xs">
                            👔 باحث عن عمل
                        </span>
                        <h3 class="text-xl font-bold text-gray-800 mb-3 mt-2">{{ $seeker->job_title ?? 'لا يوجد عنوان' }}</h3>
                        <p class="text-gray-600 mb-4 text-sm">{{ Str::limit($seeker->description, 120, '...') }}</p>

                        <div class="mt-4 space-y-2">
                            <p class="text-sm text-gray-600">
                                <span class="ml-1">📍</span>
                                <span><strong>الموقع:</strong> {{ $seeker->location ?? 'غير محدد' }}</span>
                            </p>
                            <p class="text-sm text-gray-600">
                                <span class="ml-1">⏳</span>
                                <span><strong>الخبرة:</strong> {{ $seeker->experience ?? 'غير محدد' }} سنوات</span>
                            </p>
                            <p class="text-sm text-gray-600">
                                <span class="ml-1">🎓</span>
                                <span><strong>المؤهل:</strong> {{ $seeker->qualification ?? 'غير محدد' }}</span>
                            </p>
                            <p class="text-sm text-gray-600">
                                <span class="ml-1">🗓️</span>
                                <span><strong>التاريخ:</strong> {{ isset($seeker->created_at) ? $seeker->created_at->format('Y-m-d') : 'غير محدد' }}</span>
                            </p>
                        </div>
                    </div>

                    <!-- بيانات الاتصال -->
                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                        <h4 class="text-sm font-semibold text-gray-700 mb-2">بيانات الاتصال:</h4>
                        <div class="space-y-2 mb-4">
                            @if(isset($seeker->whatsapp))
                            <p class="flex items-center text-sm text-gray-600">
                                <i class="fab fa-whatsapp ml-2 text-green-500"></i>
                                <a href="https://wa.me/{{ $seeker->whatsapp }}" target="_blank" class="text-blue-600 hover:underline">{{ $seeker->whatsapp }}</a>
                            </p>
                            @endif

                            @if(isset($seeker->email))
                            <p class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-envelope ml-2 text-blue-500"></i>
                                <a href="mailto:{{ $seeker->email }}" class="text-blue-600 hover:underline">{{ $seeker->email }}</a>
                            </p>
                            @endif

                            @if(isset($seeker->phone_number))
                            <p class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-phone ml-2 text-gray-500"></i>
                                <span>{{ $seeker->phone_number }}</span>
                            </p>
                            @endif
                        </div>
                    </div>

                    <!-- قسم الأزرار -->
                    <div class="px-6 py-4 bg-white border-t border-gray-200 flex flex-wrap gap-2">
                        <a href=" " class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-sm flex-grow text-center">
                            عرض التفاصيل
                        </a>
                        <a href="{{ route('jobSeeker.edit', $seeker->id) }}" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm text-center">
                            تعديل
                        </a>
                        <form action="{{ route('jobSeeker.destroy', $seeker->id) }}" method="POST" onsubmit="return confirm('هل أنت متأكد من الحذف؟')" class="inline-block">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm">
                                حذف
                            </button>
                        </form>
                    </div>
                </div>
            @endforeach
        </div>
    @endif
</div>


<!-- قسم الوظائف -->
<div id="jobs-section" class="section-content">
    <div class="section-header">
        <h2 class="text-2xl font-bold text-gray-800">
            <i class="fas fa-briefcase ml-2 text-blue-600"></i>
            وظائفك المنشورة
        </h2>
    </div>

    <!-- عرض الوظائف المنشورة -->
    @if($myjobs->isEmpty())
        <div class="text-center bg-white shadow-lg rounded-xl p-10 max-w-xl mx-auto">
            <div class="text-6xl mb-4">📝</div>
            <h2 class="text-2xl font-bold text-gray-700 mb-3">لم تقم بنشر أي وظائف</h2>
            <p class="text-gray-500 mb-6">قم بإضافة وظيفة جديدة للبدء</p>
            <a href="{{ route('jobs.create') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg shadow-md transition duration-300">
                <i class="fas fa-plus ml-2"></i> إضافة وظيفة الآن
            </a>
        </div>
    @else
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            @foreach($myjobs as $job)
                <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition duration-300 card-hover">
                    <!-- معلومات المستخدم -->
                    <div class="p-4 bg-blue-50 border-b border-gray-200 flex items-center">
                        <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <h3 class="text-lg font-bold text-blue-700 mr-3">{{ $job->user->name ?? 'غير معروف' }}</h3>
                    </div>

                    <!-- رأس البطاقة -->
                    <div class="p-6 relative">
                        <span class="absolute top-2 left-2 bg-blue-500 text-white px-3 py-1 rounded-full text-xs">
                            📌 وظيفة
                        </span>
                        <h3 class="text-xl font-bold text-gray-800 mb-3 mt-2">{{ $job->job_title ?? $job->title }}</h3>
                        <p class="text-gray-600 mb-4 text-sm">{{ Str::limit($job->job_description ?? $job->description, 120, '...') }}</p>

                        <div class="mt-4 space-y-2">
                            <p class="text-sm text-gray-600">
                                <span class="ml-1">🏢</span>
                                <span><strong>اسم الجهة:</strong> {{ $job->company_name ?? 'غير محدد' }}</span>
                            </p>
                            <p class="text-sm text-gray-600">
                                <span class="ml-1">📍</span>
                                <span><strong>الموقع:</strong> {{ $job->location ?? 'غير محدد' }}</span>
                            </p>
                            <p class="text-sm text-gray-600">
                                <span class="ml-1">💰</span>
                                <span><strong>الراتب:</strong> {{ isset($job->salary) ? number_format($job->salary, 2) . ' ر.س' : 'غير محدد' }}</span>
                            </p>
                            <p class="text-sm text-gray-600">
                                <span class="ml-1">🗓️</span>
                                <span><strong>التاريخ:</strong> {{ isset($job->created_at) ? $job->created_at->format('Y-m-d') : 'غير محدد' }}</span>
                            </p>
                            <p class="text-sm text-gray-600">
                                <span class="ml-1">⏳</span>
                                <span><strong>الخبرة المطلوبة:</strong> {{ $job->experience_required ?? 'غير محدد' }} سنوات</span>
                            </p>
                        </div>
                    </div>

                    <!-- بيانات الاتصال -->
                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                        <h4 class="text-sm font-semibold text-gray-700 mb-2">بيانات الاتصال:</h4>
                        <div class="space-y-2 mb-4">
                            @if(isset($job->whatsapp))
                            <p class="flex items-center text-sm text-gray-600">
                                <i class="fab fa-whatsapp ml-2 text-green-500"></i>
                                <a href="https://wa.me/{{ $job->whatsapp }}" target="_blank" class="text-blue-600 hover:underline">{{ $job->whatsapp }}</a>
                            </p>
                            @endif

                            @if(isset($job->email))
                            <p class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-envelope ml-2 text-blue-500"></i>
                                <a href="mailto:{{ $job->email }}" class="text-blue-600 hover:underline">{{ $job->email }}</a>
                            </p>
                            @endif

                            @if(isset($job->phone_number))
                            <p class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-phone ml-2 text-gray-500"></i>
                                <span>{{ $job->phone_number }}</span>
                            </p>
                            @endif
                        </div>
                    </div>

                    <!-- قسم الأزرار -->
                    <div class="px-6 py-4 bg-white border-t border-gray-200 flex flex-wrap gap-2">
                        <a href=" " class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm flex-grow text-center">
                            عرض التفاصيل
                        </a>
                        <a href="{{ route('jobs.edit', $job->id) }}" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm text-center">
                            تعديل
                        </a>
                        <form action="{{ route('jobs.destroy', $job->id) }}" method="POST" onsubmit="return confirm('هل أنت متأكد من الحذف؟')" class="inline-block">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm">
                                حذف
                            </button>
                        </form>
                    </div>
                </div>
            @endforeach
        </div>
    @endif
</div>

<!-- قسم الإعلانات -->
<div id="ads-section" class="section-content" style="display: none;">
    <div class="section-header">
        <h2 class="text-2xl font-bold text-gray-800">
            <i class="fas fa-bullhorn ml-2 text-green-600"></i>
            إعلاناتك المنشورة
        </h2>
    </div>

    <!-- عرض الإعلانات المنشورة -->
    @if(isset($myads) && $myads->isEmpty())
        <div class="text-center bg-white shadow-lg rounded-xl p-10 max-w-xl mx-auto">
            <div class="text-6xl mb-4">📢</div>
            <h2 class="text-2xl font-bold text-gray-700 mb-3">لم تقم بنشر أي إعلانات</h2>
            <p class="text-gray-500 mb-6">قم بإضافة إعلان جديد للبدء</p>
            <a href="{{ route('ads.create') }}" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg shadow-md transition duration-300">
                <i class="fas fa-plus ml-2"></i> إضافة إعلان جديد
            </a>
        </div>
    @elseif(isset($myads))
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            @foreach($myads as $ad)
                <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition duration-300 card-hover">
                    <!-- معلومات المستخدم -->
                    <div class="p-4 bg-green-50 border-b border-gray-200 flex items-center">
                        <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                        <h3 class="text-lg font-bold text-green-700 mr-3">{{ $ad->user->name ?? 'غير معروف' }}</h3>
                    </div>

                    <!-- صورة الإعلان -->
                    <div class="relative h-40 overflow-hidden">
                        <img src="{{ $ad->getImageUrl() ?? asset('images/placeholder.jpg') }}" alt="{{ $ad->title }}"
                             class="w-full h-full object-cover"
                             onerror="this.src='https://png.pngtree.com/element_our/20190528/ourlarge/pngtree-no-photography-image_1128321.jpg'; this.style.objectFit='contain';">
                        @if($ad->is_featured)
                            <div class="absolute top-2 right-2 bg-yellow-400 text-white text-xs px-2 py-1 rounded z-20 animate-pulse">مميز</div>
                        @endif
                    </div>

                    <!-- رأس البطاقة -->
                    <div class="p-6 relative">
                        <span class="absolute top-2 left-2 bg-green-500 text-white px-3 py-1 rounded-full text-xs">
                            📢 إعلان
                        </span>
                        <h3 class="text-xl font-bold text-gray-800 mb-3 mt-2">{{ $ad->title }}</h3>
                        <p class="text-gray-600 mb-4 text-sm">{{ Str::limit($ad->description, 120, '...') }}</p>

                        <div class="mt-4 space-y-2">
                            <p class="text-sm text-gray-600">
                                <span class="ml-1">📍</span>
                                <span><strong>الموقع:</strong> {{ $ad->location ?? 'غير محدد' }}</span>
                            </p>
                            <p class="text-sm text-gray-600">
                                <span class="ml-1">💰</span>
                                <span><strong>السعر:</strong> {{ isset($ad->price) ? number_format($ad->price, 2) . ' ر.س' : 'غير محدد' }}</span>
                            </p>
                            <p class="text-sm text-gray-600">
                                <span class="ml-1">🗓️</span>
                                <span><strong>التاريخ:</strong> {{ isset($ad->created_at) ? $ad->created_at->format('Y-m-d') : 'غير محدد' }}</span>
                            </p>
                            <p class="text-sm text-gray-600">
                                <span class="ml-1">📋</span>
                                <span><strong>التصنيف:</strong> {{ $ad->category ?? 'غير محدد' }}</span>
                            </p>
                        </div>
                    </div>

                    <!-- بيانات الاتصال -->
                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                        <h4 class="text-sm font-semibold text-gray-700 mb-2">بيانات الاتصال:</h4>
                        <div class="space-y-2 mb-4">
                            @if(isset($ad->whatsapp))
                            <p class="flex items-center text-sm text-gray-600">
                                <i class="fab fa-whatsapp ml-2 text-green-500"></i>
                                <a href="https://wa.me/{{ $ad->whatsapp }}" target="_blank" class="text-blue-600 hover:underline">{{ $ad->whatsapp }}</a>
                            </p>
                            @endif

                            @if(isset($ad->email))
                            <p class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-envelope ml-2 text-blue-500"></i>
                                <a href="mailto:{{ $ad->email }}" class="text-blue-600 hover:underline">{{ $ad->email }}</a>
                            </p>
                            @endif

                            @if(isset($ad->phone))
                            <p class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-phone ml-2 text-gray-500"></i>
                                <span>{{ $ad->phone }}</span>
                            </p>
                            @endif
                        </div>
                    </div>

                    <!-- قسم الأزرار -->
                    <div class="px-6 py-4 bg-white border-t border-gray-200 flex flex-wrap gap-2">
                        <a href="{{ route('ads.show', $ad->id) }}" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm flex-grow text-center">
                            عرض التفاصيل
                        </a>
                        <a href="{{ route('ads.edit', $ad->id) }}" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm text-center">
                            تعديل
                        </a>
                        <form action="{{ route('ads.destroy', $ad->id) }}" method="POST" onsubmit="return confirm('هل أنت متأكد من الحذف؟')" class="inline-block">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm">
                                حذف
                            </button>
                        </form>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- ترقيم الصفحات للإعلانات -->
        @if(isset($myads) && $myads->hasPages())
            <div class="pagination-container mt-8 flex justify-center">
                {{ $myads->links() }}
            </div>
        @endif
    @else
        <div class="text-center bg-white shadow-lg rounded-xl p-10 max-w-xl mx-auto">
            <div class="text-6xl mb-4">⚠️</div>
            <h2 class="text-2xl font-bold text-gray-700 mb-3">حدث خطأ في تحميل الإعلانات</h2>
            <p class="text-gray-500 mb-6">يرجى تحديث الصفحة أو المحاولة لاحقًا</p>
        </div>
    @endif
</div>

</main>

<footer class="mt-12 py-6 bg-white border-t border-gray-200">
    <div class="container mx-auto px-4 text-center text-gray-500 text-sm">
        جميع الحقوق محفوظة © 2025 - نظام إدارة الوظائف والإعلانات والباحثين عن عمل
    </div>
</footer>

<!-- JavaScript للتبديل بين الأقسام -->
<script>
    // تحديد القسم الافتراضي عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        // إظهار قسم الوظائف افتراضيًا
        showSection('jobs');

        // تحديد زر التبويب النشط
        document.querySelector('button[onclick="showSection(\'jobs\')"]').classList.add('bg-blue-500', 'text-white');
    });

    // دالة لإظهار القسم المحدد وإخفاء الأقسام الأخرى
    function showSection(sectionId) {
        // إخفاء جميع الأقسام
        const sections = document.querySelectorAll('.section-content');
        sections.forEach(section => {
            section.style.display = 'none';
        });

        // إظهار القسم المحدد
        document.getElementById(sectionId + '-section').style.display = 'block';

        // تحديث أزرار التبويب
        const buttons = document.querySelectorAll('.tab-button');
        buttons.forEach(button => {
            button.classList.remove('bg-blue-500', 'bg-purple-500', 'bg-green-500', 'text-white');

            // إعادة الألوان الأصلية
            if (button.textContent.includes('الوظائف')) {
                button.classList.add('bg-blue-100', 'text-blue-800');
            } else if (button.textContent.includes('الباحثين')) {
                button.classList.add('bg-purple-100', 'text-purple-800');
            } else if (button.textContent.includes('الإعلانات')) {
                button.classList.add('bg-green-100', 'text-green-800');
            }
        });

        // تحديد الزر النشط
        const activeButton = document.querySelector(`button[onclick="showSection('${sectionId}')"]`);
        if (activeButton) {
            activeButton.classList.remove('bg-blue-100', 'bg-purple-100', 'bg-green-100', 'text-blue-800', 'text-purple-800', 'text-green-800');

            // إضافة لون مناسب للزر النشط
            if (sectionId === 'jobs') {
                activeButton.classList.add('bg-blue-500', 'text-white');
            } else if (sectionId === 'jobSeekers') {
                activeButton.classList.add('bg-purple-500', 'text-white');
            } else if (sectionId === 'ads') {
                activeButton.classList.add('bg-green-500', 'text-white');
            }
        }
    }
</script>

</body>
</html>