<?php

// ملف اختبار نظام المشاهدات
// تشغيل: php test_views_system.php

require_once 'vendor/autoload.php';

// تحميل إعدادات Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Ad;
use Illuminate\Support\Facades\Schema;

echo "🧪 اختبار نظام المشاهدات للإعلانات\n";
echo "=====================================\n\n";

try {
    // 1. فحص وجود عمود المشاهدات
    echo "1️⃣ فحص عمود المشاهدات:\n";
    
    if (Schema::hasColumn('ads', 'views')) {
        echo "   ✅ عمود 'views' موجود في جدول ads\n";
    } else {
        echo "   ❌ عمود 'views' غير موجود في جدول ads\n";
        echo "   🔧 تشغيل: php artisan migrate\n";
    }

    // 2. فحص نموذج Ad
    echo "\n2️⃣ فحص نموذج Ad:\n";
    
    $ad = new Ad();
    $fillable = $ad->getFillable();
    
    if (in_array('views', $fillable)) {
        echo "   ✅ 'views' موجود في fillable\n";
    } else {
        echo "   ❌ 'views' غير موجود في fillable\n";
    }
    
    // فحص الدوال الجديدة
    $methods = ['incrementViews', 'getFormattedViews', 'generateRealisticViews'];
    foreach ($methods as $method) {
        if (method_exists($ad, $method)) {
            echo "   ✅ دالة {$method} موجودة\n";
        } else {
            echo "   ❌ دالة {$method} مفقودة\n";
        }
    }

    // 3. فحص الإعلانات الموجودة
    echo "\n3️⃣ فحص الإعلانات الموجودة:\n";
    
    try {
        $totalAds = Ad::count();
        echo "   📊 إجمالي الإعلانات: {$totalAds}\n";
        
        if ($totalAds > 0) {
            $adsWithViews = Ad::where('views', '>', 0)->count();
            $adsWithoutViews = Ad::where('views', '=', 0)->count();
            
            echo "   👁️ إعلانات لديها مشاهدات: {$adsWithViews}\n";
            echo "   🔍 إعلانات بدون مشاهدات: {$adsWithoutViews}\n";
            
            if ($adsWithViews > 0) {
                $maxViews = Ad::max('views');
                $avgViews = Ad::avg('views');
                $totalViews = Ad::sum('views');
                
                echo "   📈 أعلى مشاهدات: " . number_format($maxViews) . "\n";
                echo "   📊 متوسط المشاهدات: " . number_format($avgViews, 1) . "\n";
                echo "   🎯 إجمالي المشاهدات: " . number_format($totalViews) . "\n";
            }
        }
        
    } catch (Exception $e) {
        echo "   ❌ خطأ في قراءة الإعلانات: {$e->getMessage()}\n";
    }

    // 4. اختبار دالة التنسيق
    echo "\n4️⃣ اختبار دالة تنسيق المشاهدات:\n";
    
    try {
        $testAd = new Ad();
        $testAd->views = 0;
        
        // اختبار أرقام مختلفة
        $testNumbers = [0, 5, 123, 1234, 12345, 123456, 1234567];
        
        foreach ($testNumbers as $number) {
            $testAd->views = $number;
            $formatted = $testAd->getFormattedViews();
            echo "   📊 {$number} → {$formatted}\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ خطأ في اختبار التنسيق: {$e->getMessage()}\n";
    }

    // 5. فحص Command
    echo "\n5️⃣ فحص Command توليد المشاهدات:\n";
    
    $commandFile = 'app/Console/Commands/GenerateRealisticViews.php';
    if (file_exists($commandFile)) {
        echo "   ✅ ملف Command موجود\n";
        
        // فحص محتوى الملف
        $content = file_get_contents($commandFile);
        if (strpos($content, 'ads:generate-views') !== false) {
            echo "   ✅ Command signature صحيح\n";
        } else {
            echo "   ❌ Command signature غير صحيح\n";
        }
        
    } else {
        echo "   ❌ ملف Command غير موجود\n";
    }

    // 6. فحص Views المحدثة
    echo "\n6️⃣ فحص Views المحدثة:\n";
    
    $viewFiles = [
        'resources/views/ads/show.blade.php' => 'getFormattedViews()',
        'resources/views/ads/index.blade.php' => 'getFormattedViews()'
    ];
    
    foreach ($viewFiles as $file => $searchText) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            if (strpos($content, $searchText) !== false) {
                echo "   ✅ {$file} محدث\n";
            } else {
                echo "   ❌ {$file} غير محدث\n";
            }
        } else {
            echo "   ❌ {$file} غير موجود\n";
        }
    }

    // 7. اختبار محاكاة زيادة المشاهدات
    echo "\n7️⃣ اختبار محاكاة زيادة المشاهدات:\n";
    
    try {
        $firstAd = Ad::first();
        if ($firstAd) {
            $oldViews = $firstAd->views ?? 0;
            echo "   📊 المشاهدات قبل الزيادة: {$oldViews}\n";
            
            // محاكاة زيادة المشاهدات (بدون حفظ فعلي)
            $newViews = $oldViews + 1;
            echo "   📈 المشاهدات بعد الزيادة: {$newViews}\n";
            echo "   ✅ دالة زيادة المشاهدات تعمل نظرياً\n";
        } else {
            echo "   ⚠️ لا توجد إعلانات لاختبار زيادة المشاهدات\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ خطأ في اختبار زيادة المشاهدات: {$e->getMessage()}\n";
    }

    echo "\n🎯 خلاصة الاختبار:\n";
    echo "==================\n";
    
    $allGood = true;
    
    // فحص العناصر الأساسية
    if (!Schema::hasColumn('ads', 'views')) {
        echo "❌ عمود المشاهدات غير موجود - تشغيل: php artisan migrate\n";
        $allGood = false;
    }
    
    if (!in_array('views', (new Ad())->getFillable())) {
        echo "❌ views غير موجود في fillable\n";
        $allGood = false;
    }
    
    if (!method_exists(new Ad(), 'getFormattedViews')) {
        echo "❌ دالة getFormattedViews مفقودة\n";
        $allGood = false;
    }
    
    if ($allGood) {
        echo "🎉 جميع مكونات نظام المشاهدات جاهزة!\n";
        echo "✅ عمود المشاهدات موجود\n";
        echo "✅ نموذج Ad محدث\n";
        echo "✅ دوال التنسيق تعمل\n";
        echo "✅ Views محدثة\n";
        echo "✅ Command جاهز للاستخدام\n\n";
        
        echo "📋 خطوات التشغيل:\n";
        echo "================\n";
        echo "1. php artisan migrate (إذا لم يتم تنفيذه)\n";
        echo "2. php artisan ads:generate-views --reset\n";
        echo "3. تصفح الإعلانات لرؤية المشاهدات الحقيقية\n";
    } else {
        echo "⚠️ هناك مشاكل تحتاج إلى حل قبل الاستخدام\n";
    }

} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
    echo "\nتفاصيل الخطأ:\n";
    echo $e->getTraceAsString() . "\n";
}

echo "\n📞 للمساعدة:\n";
echo "============\n";
echo "- راجع ملف ADS_VIEWS_SYSTEM.md\n";
echo "- تأكد من تشغيل: php artisan migrate\n";
echo "- استخدم: php artisan ads:generate-views --reset\n";

?>
