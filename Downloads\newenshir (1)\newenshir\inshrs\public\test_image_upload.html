<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رفع الصور</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .file-input {
            margin: 10px 0;
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 5px;
            text-align: center;
        }
        
        .preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        
        .preview-item {
            position: relative;
            width: 100px;
            height: 100px;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار رفع الصور</h1>
        
        <!-- اختبار 1: اختيار الصور -->
        <div class="test-section">
            <h3>1️⃣ اختبار اختيار الصور</h3>
            <div class="file-input">
                <input type="file" id="test-images" multiple accept="image/*">
                <p>اختر صور متعددة (حتى 5 صور)</p>
            </div>
            <div id="preview-container" class="preview-container"></div>
            <div id="selection-result"></div>
        </div>
        
        <!-- اختبار 2: محاكاة النموذج -->
        <div class="test-section">
            <h3>2️⃣ اختبار النموذج</h3>
            <form id="test-form" enctype="multipart/form-data">
                <input type="text" name="title" placeholder="عنوان الإعلان" required>
                <textarea name="description" placeholder="وصف الإعلان" required></textarea>
                <input type="file" name="images[]" id="form-images" multiple accept="image/*">
                <button type="submit">إرسال النموذج</button>
            </form>
            <div id="form-result"></div>
        </div>
        
        <!-- اختبار 3: إرسال إلى الخادم -->
        <div class="test-section">
            <h3>3️⃣ اختبار الإرسال للخادم</h3>
            <button onclick="testServerUpload()">اختبار رفع للخادم</button>
            <div id="server-result"></div>
        </div>
        
        <!-- سجل الأحداث -->
        <div class="test-section">
            <h3>4️⃣ سجل الأحداث</h3>
            <button onclick="clearLog()">مسح السجل</button>
            <div id="log" class="log-area"></div>
        </div>
    </div>

    <script>
        let selectedFiles = [];
        
        // دالة تسجيل الأحداث
        function log(message, type = 'info') {
            const logArea = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;
            
            console.log(`[Image Test] ${logEntry.trim()}`);
        }
        
        // مسح السجل
        function clearLog() {
            document.getElementById('log').textContent = '';
        }
        
        // إضافة نتيجة
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            container.appendChild(resultDiv);
            
            log(message, type);
        }
        
        // مسح النتائج
        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }
        
        // اختبار 1: اختيار الصور
        document.getElementById('test-images').addEventListener('change', function(e) {
            log('🖼️ تم اختيار صور جديدة');
            clearResults('selection-result');
            
            const files = Array.from(e.target.files);
            selectedFiles = files;
            
            log(`📊 عدد الصور المختارة: ${files.length}`);
            
            if (files.length === 0) {
                addResult('selection-result', 'لم يتم اختيار أي صور', 'warning');
                return;
            }
            
            if (files.length > 5) {
                addResult('selection-result', 'تم اختيار أكثر من 5 صور - سيتم استخدام أول 5 فقط', 'warning');
                selectedFiles = files.slice(0, 5);
            }
            
            // عرض معلومات كل صورة
            selectedFiles.forEach((file, index) => {
                log(`📷 صورة ${index + 1}: ${file.name} (${(file.size / 1024).toFixed(2)} KB, ${file.type})`);
                
                // التحقق من نوع الملف
                if (!file.type.startsWith('image/')) {
                    addResult('selection-result', `ملف ${file.name} ليس صورة`, 'error');
                    return;
                }
                
                // التحقق من حجم الملف (2MB)
                if (file.size > 2 * 1024 * 1024) {
                    addResult('selection-result', `صورة ${file.name} كبيرة جداً (${(file.size / 1024 / 1024).toFixed(2)} MB)`, 'warning');
                }
            });
            
            // عرض معاينة الصور
            displayPreviews();
            
            addResult('selection-result', `✅ تم اختيار ${selectedFiles.length} صورة بنجاح`, 'success');
        });
        
        // عرض معاينة الصور
        function displayPreviews() {
            const container = document.getElementById('preview-container');
            container.innerHTML = '';
            
            selectedFiles.forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const previewItem = document.createElement('div');
                    previewItem.className = 'preview-item';
                    
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.alt = `معاينة ${index + 1}`;
                    
                    previewItem.appendChild(img);
                    container.appendChild(previewItem);
                };
                reader.readAsDataURL(file);
            });
        }
        
        // اختبار 2: النموذج
        document.getElementById('test-form').addEventListener('submit', function(e) {
            e.preventDefault();
            log('📝 تم إرسال النموذج');
            clearResults('form-result');
            
            const formData = new FormData(this);
            
            // التحقق من البيانات
            const title = formData.get('title');
            const description = formData.get('description');
            const images = formData.getAll('images[]');
            
            log(`📋 بيانات النموذج: العنوان="${title}", الوصف="${description}", الصور=${images.length}`);
            
            if (!title || !description) {
                addResult('form-result', 'يجب ملء جميع الحقول المطلوبة', 'error');
                return;
            }
            
            if (images.length === 0 || (images.length === 1 && images[0].size === 0)) {
                addResult('form-result', 'لم يتم اختيار أي صور', 'warning');
            } else {
                images.forEach((file, index) => {
                    if (file.size > 0) {
                        log(`📷 صورة النموذج ${index + 1}: ${file.name} (${(file.size / 1024).toFixed(2)} KB)`);
                    }
                });
                addResult('form-result', `✅ النموذج جاهز مع ${images.filter(f => f.size > 0).length} صورة`, 'success');
            }
            
            // محاكاة الإرسال
            addResult('form-result', '🚀 سيتم إرسال النموذج إلى /ads (محاكاة)', 'info');
        });
        
        // اختبار 3: الإرسال للخادم
        function testServerUpload() {
            log('🌐 بدء اختبار الإرسال للخادم');
            clearResults('server-result');
            
            if (selectedFiles.length === 0) {
                addResult('server-result', 'يجب اختيار صور أولاً', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('title', 'اختبار رفع الصور');
            formData.append('description', 'هذا اختبار لرفع الصور');
            formData.append('category', 'other');
            formData.append('_token', 'test-token');
            
            selectedFiles.forEach((file, index) => {
                formData.append('images[]', file);
                log(`📤 إضافة صورة ${index + 1} إلى FormData: ${file.name}`);
            });
            
            addResult('server-result', '🔄 جاري الإرسال للخادم...', 'info');
            
            fetch('/ads', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                log(`📥 استجابة الخادم: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    addResult('server-result', '✅ تم الإرسال بنجاح', 'success');
                    return response.text();
                } else {
                    addResult('server-result', `❌ خطأ في الخادم: ${response.status}`, 'error');
                    return response.text();
                }
            })
            .then(data => {
                log(`📄 محتوى الاستجابة: ${data.substring(0, 200)}...`);
                
                if (data.includes('success') || data.includes('تم نشر الإعلان')) {
                    addResult('server-result', '🎉 تم رفع الصور بنجاح!', 'success');
                } else if (data.includes('error') || data.includes('خطأ')) {
                    addResult('server-result', '❌ فشل في رفع الصور', 'error');
                } else {
                    addResult('server-result', '⚠️ استجابة غير متوقعة من الخادم', 'warning');
                }
            })
            .catch(error => {
                log(`❌ خطأ في الشبكة: ${error.message}`);
                addResult('server-result', `❌ خطأ في الاتصال: ${error.message}`, 'error');
            });
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 تم تحميل صفحة اختبار رفع الصور');
            log('💡 اختر صور من القسم الأول ثم جرب الاختبارات');
            
            // اختبار دعم المتصفح
            if (typeof FormData === 'undefined') {
                addResult('selection-result', '❌ المتصفح لا يدعم FormData', 'error');
            } else {
                log('✅ المتصفح يدعم FormData');
            }
            
            if (typeof FileReader === 'undefined') {
                addResult('selection-result', '❌ المتصفح لا يدعم FileReader', 'error');
            } else {
                log('✅ المتصفح يدعم FileReader');
            }
        });
    </script>
</body>
</html>
