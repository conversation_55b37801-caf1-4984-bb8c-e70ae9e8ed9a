<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بيانات الباحث عن عمل</title>
    <!-- External Stylesheets -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* تنسيق الصندوق المنبثق */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            justify-content: center;
            align-items: center;
        }
        .modal-content {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 400px;
            text-align: right;
        }
    </style>
</head>
<body class="bg-gray-200 flex justify-center items-center min-h-screen px-4">
    <div class="container bg-white p-6 md:p-8 rounded-xl shadow-lg w-full max-w-lg text-right border border-gray-300">
        <!-- Header with Profile Image -->
        <div class="text-center mb-6">
            <div class="user-profile-section mb-4">
                @if($jobSeeker->user && $jobSeeker->user->hasProfileImage())
                    <img src="{{ $jobSeeker->user->getProfileImageUrl() }}" alt="صورة {{ $jobSeeker->user->name }}"
                         class="w-24 h-24 rounded-full object-cover border-4 border-blue-200 mx-auto mb-3 shadow-lg">
                @else
                    <img src="{{ $jobSeeker->user ? $jobSeeker->user->getDefaultAvatar() : 'https://ui-avatars.com/api/?name=باحث&background=random&color=fff&size=96&rounded=true' }}"
                         alt="صورة افتراضية" class="w-24 h-24 rounded-full object-cover border-4 border-blue-200 mx-auto mb-3 shadow-lg">
                @endif
                <h2 class="text-xl font-bold text-blue-700 mb-1">{{ $jobSeeker->user->name ?? 'غير متوفر' }}</h2>
                @if($jobSeeker->specialization)
                    <p class="text-gray-600 text-sm">{{ $jobSeeker->specialization }}</p>
                @endif
            </div>
            <h1 class="text-xl md:text-3xl font-bold text-blue-600">بيانات الباحث عن عمل</h1>
        </div>

        <!-- Seeker Details -->
        <div class="seeker-details space-y-4">
            <p class="flex items-center text-gray-700 text-base">
                <i class="fas fa-user ml-3 text-lg text-blue-500"></i>
                <strong class="w-32">الاسم:</strong>
                <span>{{ $jobSeeker->user->name ?? 'غير متوفر' }}</span>
            </p>
            <p class="flex items-center text-gray-700 text-base">
                <i class="fas fa-map-marker-alt ml-3 text-lg text-red-500"></i>
                <strong class="w-32">الموقع:</strong>
                <span>{{ $jobSeeker->location ?? 'غير محدد' }}</span>
            </p>
            <p class="flex items-center text-gray-700 text-base">
                <i class="fas fa-briefcase ml-3 text-lg text-green-500"></i>
                <strong class="w-32">الخبرة:</strong>
                <span>{{ $jobSeeker->experience ?? 'غير محدد' }} سنوات</span>
            </p>
            <p class="flex items-center text-gray-700 text-base">
                <i class="fas fa-info-circle ml-3 text-lg text-yellow-500"></i>
                <strong class="w-32">وصف مختصر:</strong>
                <span>{{ $jobSeeker->description ?? 'لا يوجد وصف' }}</span>
            </p>
        </div>

        <!-- Contact Details -->
        <div class="contact-details mt-6 space-y-4 bg-gray-100 p-4 rounded-lg border border-gray-300">
            <h2 class="text-lg font-semibold text-gray-800 border-b pb-2">بيانات الاتصال</h2>
            <p class="flex items-center text-gray-700 text-base">
                <i class="fab fa-whatsapp ml-3 text-lg text-green-500"></i>
                <strong class="w-32">واتساب:</strong>
                <a href="https://wa.me/{{ $jobSeeker->whatsapp ?? '0' }}" target="_blank" class="text-blue-600 hover:underline">
                    {{ $jobSeeker->whatsapp ?? 'غير متوفر' }}
                </a>
            </p>
            <p class="flex items-center text-gray-700 text-base">
                <i class="fas fa-envelope ml-3 text-lg text-blue-500"></i>
                <strong class="w-32">الإيميل:</strong>
                <a href="mailto:{{ $jobSeeker->user->email ?? '<EMAIL>' }}" class="text-blue-600 hover:underline">
                    {{ $jobSeeker->user->email ?? 'غير متوفر' }}
                </a>
            </p>
            <p class="flex items-center text-gray-700 text-base">
                <i class="fas fa-phone ml-3 text-lg text-gray-500"></i>
                <strong class="w-32">اتصال:</strong>
                <span>{{ $jobSeeker->phone ?? 'غير متوفر' }}</span>
            </p>
        </div>

        <!-- Chat and Save Buttons -->
        @if(Auth::check() && Auth::id() != $jobSeeker->user_id)
        <div class="mb-6">
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                @include('components.chat-button', ['userId' => $jobSeeker->user_id])
                @include('components.save-button', ['itemType' => 'job_seeker', 'itemId' => $jobSeeker->id])
            </div>
        </div>
        @endif

        <!-- Buttons -->
        <div class="buttons flex flex-col md:flex-row justify-center mt-6 space-y-2 md:space-y-0 md:space-x-4">
            @auth
                @if(Auth::id() != $jobSeeker->user_id)
                    <button class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-2 px-6 rounded-lg w-full md:w-auto transition duration-300" onclick="showReportModal()">تبليغ</button>
                @endif
            @else
                <a href="{{ route('login') }}" class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-2 px-6 rounded-lg w-full md:w-auto transition duration-300 text-center">سجل دخول للإبلاغ</a>
            @endauth
            <button class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-6 rounded-lg w-full md:w-auto transition duration-300" onclick="goBack()">رجوع</button>
        </div>
    </div>

    <!-- Report Modal -->
    <div id="reportModal" class="modal">
        <div class="modal-content">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">الإبلاغ عن الباحث عن عمل</h2>
            <form method="POST" action="{{ route('job-seekers.report', $jobSeeker->id) }}">
                @csrf
                <div class="mb-4">
                    <label for="report_type" class="block text-gray-700 font-semibold mb-2">نوع البلاغ:</label>
                    <select name="report_type" id="report_type" class="w-full p-2 border rounded-lg mb-2" required>
                        <option value="">اختر نوع البلاغ</option>
                        <option value="معلومات زائفة">معلومات زائفة</option>
                        <option value="محتوى غير لائق">محتوى غير لائق</option>
                        <option value="احتيال أو نصب">احتيال أو نصب</option>
                        <option value="انتحال شخصية">انتحال شخصية</option>
                        <option value="سبب آخر">سبب آخر</option>
                    </select>
                </div>
                <div class="mb-4">
                    <label for="reason" class="block text-gray-700 font-semibold mb-2">تفاصيل البلاغ:</label>
                    <textarea name="reason" id="reason" rows="4" class="w-full p-2 border rounded-lg" placeholder="يرجى توضيح سبب البلاغ بالتفصيل..." required></textarea>
                </div>
                <div class="bg-blue-50 p-3 rounded-lg mb-4 text-sm text-blue-800">
                    <i class="fas fa-info-circle ml-1"></i>
                    سيتم مراجعة البلاغ من قبل فريق الإدارة، وسيتم اتخاذ الإجراء المناسب.
                </div>
                <div class="flex justify-between">
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg transition duration-300">إرسال البلاغ</button>
                    <button type="button" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg transition duration-300" onclick="hideReportModal()">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        function showReportModal() {
            document.getElementById('reportModal').style.display = 'flex';
        }

        function hideReportModal() {
            document.getElementById('reportModal').style.display = 'none';
        }

        function goBack() {
            window.history.back();
        }
    </script>
</body>
</html>