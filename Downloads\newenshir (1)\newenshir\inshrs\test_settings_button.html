<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أيقونة الإعدادات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
            text-align: right;
            transition: all 0.3s ease;
        }
        body.dark-theme {
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .container {
            max-width: 1200px;
            margin: 2rem auto;
        }
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }
        .dark-theme .card {
            background-color: #2d2d2d;
            color: #ffffff;
        }
        .demo-section {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }
        .dark-theme .demo-section {
            background-color: #2d2d2d;
            color: #ffffff;
        }
        .settings-demo-button {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
            position: relative;
            overflow: hidden;
        }
        .settings-demo-button:hover {
            background: linear-gradient(135deg, #5a6268 0%, #343a40 100%);
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        }
        .settings-demo-icon {
            font-size: 1.5rem;
            transition: transform 0.3s ease;
        }
        .settings-demo-button:hover .settings-demo-icon {
            transform: rotate(90deg);
        }
        .feature-badge {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 50rem;
            font-size: 0.875rem;
            font-weight: 500;
            display: inline-block;
            margin-bottom: 1rem;
        }
        .demo-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
        }
        .inline-settings {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            border: none;
            border-radius: 50rem;
            padding: 0.75rem 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
        }
        .inline-settings:hover {
            background: linear-gradient(135deg, #5a6268 0%, #343a40 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
        }
        .icon-settings {
            background: none;
            border: none;
            color: #6c757d;
            font-size: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 0.5rem;
            border-radius: 50%;
        }
        .icon-settings:hover {
            background: rgba(108, 117, 125, 0.1);
            transform: scale(1.1) rotate(90deg);
        }
        .stats-card {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }
        .floating-demo {
            position: fixed;
            top: 80px;
            left: 20px;
            z-index: 1000;
        }
        .user-demo {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 0.75rem;
            margin-bottom: 1rem;
            border: 1px solid #dee2e6;
        }
        .dark-theme .user-demo {
            background: linear-gradient(135deg, #3d3d3d 0%, #2d2d2d 100%);
            border-color: #555;
        }
        .user-avatar-demo {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-left: 0.75rem;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
        }
        .settings-option-demo {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-radius: 0.5rem;
            margin-bottom: 0.5rem;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .settings-option-demo:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            transform: translateX(-5px);
            border-color: #dee2e6;
        }
        .dark-theme .settings-option-demo:hover {
            background: linear-gradient(135deg, #3d3d3d 0%, #2d2d2d 100%);
            border-color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-cog me-2"></i>
                    اختبار أيقونة الإعدادات
                </h4>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>تم إنشاء مكون الإعدادات بنجاح!</strong>
                    <br>
                    <small>تم إضافة أيقونة إعدادات شاملة مع قائمة منسدلة تفاعلية.</small>
                </div>
            </div>
        </div>

        <!-- زر الإعدادات العائم التجريبي -->
        <div class="floating-demo">
            <button class="settings-demo-button" onclick="demoSettings(this)" title="الإعدادات">
                <i class="settings-demo-icon fas fa-cog"></i>
            </button>
        </div>

        <!-- عرض أنواع أزرار الإعدادات -->
        <div class="demo-section">
            <div class="feature-badge">
                <i class="fas fa-star me-1"></i>
                أنواع أزرار الإعدادات
            </div>
            <h5 class="mb-4">
                <i class="fas fa-cog me-2"></i>
                أنماط مختلفة لزر الإعدادات
            </h5>
            
            <div class="row">
                <div class="col-md-4">
                    <h6 class="mb-3">الزر العائم</h6>
                    <div class="demo-buttons">
                        <button class="settings-demo-button" onclick="demoSettings(this)" title="الإعدادات">
                            <i class="settings-demo-icon fas fa-cog"></i>
                        </button>
                    </div>
                    <p class="text-muted mt-2 small">زر دائري عائم مع قائمة منسدلة</p>
                </div>
                
                <div class="col-md-4">
                    <h6 class="mb-3">الزر المدمج</h6>
                    <div class="demo-buttons">
                        <button class="inline-settings" onclick="demoSettings(this)">
                            <i class="settings-demo-icon fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </button>
                    </div>
                    <p class="text-muted mt-2 small">زر مع نص للاستخدام في الصفحات</p>
                </div>
                
                <div class="col-md-4">
                    <h6 class="mb-3">أيقونة فقط</h6>
                    <div class="demo-buttons">
                        <button class="icon-settings" onclick="demoSettings(this)" title="إعدادات">
                            <i class="settings-demo-icon fas fa-cog"></i>
                        </button>
                    </div>
                    <p class="text-muted mt-2 small">أيقونة بسيطة للاستخدام في الأشرطة</p>
                </div>
            </div>
        </div>

        <!-- محاكاة قائمة الإعدادات -->
        <div class="demo-section">
            <div class="feature-badge">
                <i class="fas fa-list me-1"></i>
                قائمة الإعدادات
            </div>
            <h5 class="mb-4">
                <i class="fas fa-user-cog me-2"></i>
                خيارات الإعدادات المتاحة
            </h5>
            
            <!-- معلومات المستخدم التجريبية -->
            <div class="user-demo">
                <div class="user-avatar-demo">
                    <i class="fas fa-user"></i>
                </div>
                <div>
                    <h6 class="mb-1">أحمد محمد</h6>
                    <p class="mb-0 text-muted small"><EMAIL></p>
                </div>
            </div>
            
            <!-- خيارات الإعدادات -->
            <div class="row">
                <div class="col-md-6">
                    <div class="settings-option-demo" onclick="demoAction('profile')">
                        <div style="width: 40px; height: 40px; border-radius: 50%; background: #2196f3; display: flex; align-items: center; justify-content: center; color: white; margin-left: 0.75rem;">
                            <i class="fas fa-user-edit"></i>
                        </div>
                        <div>
                            <div style="font-weight: 600; margin-bottom: 0.25rem;">الملف الشخصي</div>
                            <p style="font-size: 0.875rem; color: #6c757d; margin: 0;">تحرير المعلومات الشخصية</p>
                        </div>
                    </div>
                    
                    <div class="settings-option-demo" onclick="demoAction('privacy')">
                        <div style="width: 40px; height: 40px; border-radius: 50%; background: #4caf50; display: flex; align-items: center; justify-content: center; color: white; margin-left: 0.75rem;">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div>
                            <div style="font-weight: 600; margin-bottom: 0.25rem;">الخصوصية والأمان</div>
                            <p style="font-size: 0.875rem; color: #6c757d; margin: 0;">إدارة إعدادات الخصوصية</p>
                        </div>
                    </div>
                    
                    <div class="settings-option-demo" onclick="demoAction('notifications')">
                        <div style="width: 40px; height: 40px; border-radius: 50%; background: #ff9800; display: flex; align-items: center; justify-content: center; color: white; margin-left: 0.75rem;">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div>
                            <div style="font-weight: 600; margin-bottom: 0.25rem;">الإشعارات</div>
                            <p style="font-size: 0.875rem; color: #6c757d; margin: 0;">إدارة الإشعارات</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="settings-option-demo" onclick="demoAction('theme')">
                        <div style="width: 40px; height: 40px; border-radius: 50%; background: #9c27b0; display: flex; align-items: center; justify-content: center; color: white; margin-left: 0.75rem;">
                            <i class="fas fa-palette"></i>
                        </div>
                        <div>
                            <div style="font-weight: 600; margin-bottom: 0.25rem;">المظهر</div>
                            <p style="font-size: 0.875rem; color: #6c757d; margin: 0;">تغيير المظهر والألوان</p>
                        </div>
                    </div>
                    
                    <div class="settings-option-demo" onclick="demoAction('language')">
                        <div style="width: 40px; height: 40px; border-radius: 50%; background: #17a2b8; display: flex; align-items: center; justify-content: center; color: white; margin-left: 0.75rem;">
                            <i class="fas fa-language"></i>
                        </div>
                        <div>
                            <div style="font-weight: 600; margin-bottom: 0.25rem;">اللغة</div>
                            <p style="font-size: 0.875rem; color: #6c757d; margin: 0;">تغيير لغة الواجهة</p>
                        </div>
                    </div>
                    
                    <div class="settings-option-demo" onclick="demoAction('logout')">
                        <div style="width: 40px; height: 40px; border-radius: 50%; background: #f44336; display: flex; align-items: center; justify-content: center; color: white; margin-left: 0.75rem;">
                            <i class="fas fa-sign-out-alt"></i>
                        </div>
                        <div>
                            <div style="font-weight: 600; margin-bottom: 0.25rem;">تسجيل الخروج</div>
                            <p style="font-size: 0.875rem; color: #6c757d; margin: 0;">الخروج من الحساب</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- اختبار الوظائف -->
        <div class="demo-section">
            <div class="feature-badge">
                <i class="fas fa-flask me-1"></i>
                اختبار الوظائف
            </div>
            <h5 class="mb-4">
                <i class="fas fa-play me-2"></i>
                اختبار وظائف الإعدادات
            </h5>
            
            <div class="row g-3">
                <div class="col-md-3">
                    <button class="btn btn-primary w-100" onclick="toggleThemeDemo()">
                        <i class="fas fa-palette me-1"></i>
                        تبديل المظهر
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-warning w-100" onclick="toggleNotificationsDemo()">
                        <i class="fas fa-bell me-1"></i>
                        تبديل الإشعارات
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-info w-100" onclick="changeLanguageDemo()">
                        <i class="fas fa-language me-1"></i>
                        تغيير اللغة
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-danger w-100" onclick="logoutDemo()">
                        <i class="fas fa-sign-out-alt me-1"></i>
                        تسجيل الخروج
                    </button>
                </div>
            </div>
        </div>

        <!-- إحصائيات -->
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card">
                    <i class="fas fa-cog fa-3x mb-3"></i>
                    <h5>6</h5>
                    <p class="mb-0">خيارات إعدادات</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);">
                    <i class="fas fa-user-cog fa-3x mb-3"></i>
                    <h5>شخصي</h5>
                    <p class="mb-0">ملف مستخدم</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                    <i class="fas fa-shield-alt fa-3x mb-3"></i>
                    <h5>آمن</h5>
                    <p class="mb-0">حماية البيانات</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                    <i class="fas fa-mobile-alt fa-3x mb-3"></i>
                    <h5>متجاوب</h5>
                    <p class="mb-0">جميع الأجهزة</p>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <strong>تم تنفيذ المهمة الحادية عشرة بنجاح!</strong>
                <br>
                <small>تم إضافة أيقونة الإعدادات مع قائمة شاملة وتفاعلية.</small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function demoSettings(button) {
            const icon = button.querySelector('.settings-demo-icon');
            
            // تأثير الدوران
            icon.style.transform = 'rotate(180deg)';
            setTimeout(() => {
                icon.style.transform = '';
            }, 300);
            
            showNotification('تم فتح قائمة الإعدادات', 'info');
        }
        
        function demoAction(action) {
            const actions = {
                'profile': 'تم فتح إعدادات الملف الشخصي',
                'privacy': 'تم فتح إعدادات الخصوصية والأمان',
                'notifications': 'تم فتح إعدادات الإشعارات',
                'theme': 'تم فتح إعدادات المظهر',
                'language': 'تم فتح إعدادات اللغة',
                'logout': 'تم تسجيل الخروج بنجاح'
            };
            
            showNotification(actions[action] || 'تم تنفيذ الإجراء', 'success');
        }
        
        function toggleThemeDemo() {
            document.body.classList.toggle('dark-theme');
            const isDark = document.body.classList.contains('dark-theme');
            showNotification(`تم تغيير المظهر إلى ${isDark ? 'الداكن' : 'الفاتح'}`, 'info');
        }
        
        function toggleNotificationsDemo() {
            const enabled = Math.random() > 0.5;
            showNotification(enabled ? 'تم تفعيل الإشعارات' : 'تم إيقاف الإشعارات', enabled ? 'success' : 'warning');
        }
        
        function changeLanguageDemo() {
            showNotification('تم تغيير اللغة إلى العربية', 'info');
        }
        
        function logoutDemo() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                showNotification('تم تسجيل الخروج بنجاح', 'success');
            }
        }
        
        function showNotification(message, type = 'info') {
            const colors = {
                'success': '#28a745',
                'info': '#007bff',
                'warning': '#ffc107',
                'error': '#dc3545'
            };
            
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 140px;
                left: 20px;
                background: ${colors[type]};
                color: white;
                padding: 0.75rem 1rem;
                border-radius: 0.5rem;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                z-index: 1002;
                opacity: 0;
                transform: translateX(-100%);
                transition: all 0.3s ease;
                max-width: 280px;
            `;
            
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'info' ? 'info-circle' : type === 'warning' ? 'exclamation-triangle' : 'times-circle'} me-2"></i>
                ${message}
            `;
            
            document.body.appendChild(notification);
            
            // إظهار الإشعار
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            // إخفاء الإشعار
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(-100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 300);
            }, 3000);
        }
        
        // إضافة تأثيرات تفاعلية
        document.querySelectorAll('.settings-demo-button, .inline-settings, .icon-settings').forEach(button => {
            button.addEventListener('mouseenter', function() {
                const icon = this.querySelector('.settings-demo-icon');
                if (icon && !this.classList.contains('icon-settings')) {
                    icon.style.transform = 'rotate(90deg)';
                }
            });
            
            button.addEventListener('mouseleave', function() {
                const icon = this.querySelector('.settings-demo-icon');
                if (icon && !this.classList.contains('icon-settings')) {
                    icon.style.transform = '';
                }
            });
        });
    </script>
</body>
</html>
