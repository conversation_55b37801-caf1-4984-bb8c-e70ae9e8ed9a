<?php
namespace App\Http\Controllers;

use App\Models\PaymentTransaction;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PointController extends Controller
{
    protected $clientId;
    protected $clientSecret;
    protected $mode;
    protected $baseUrl;

    public function __construct()
    {
        $this->mode = config('services.paypal.mode');
        $this->clientId = config("services.paypal.{$this->mode}.client_id");
        $this->clientSecret = config("services.paypal.{$this->mode}.client_secret");
        $this->baseUrl = $this->mode === 'sandbox'
            ? 'https://api-m.sandbox.paypal.com'
            : 'https://api-m.paypal.com';
    }

    public function buy(Request $request)
    {
        $success = $request->query('success');

        if ($success) {
            session()->flash('success', urldecode($success));
        }

        return view('points.buy');
    }

    public function processBuy(Request $request)
    {
        $request->validate([
            'amount' => 'required|integer|in:5,10,20,50',
            'payment_method' => 'nullable|string|in:paypal,stcpay,bank_transfer',
            'transfer_receipt' => 'required_if:payment_method,bank_transfer|file|mimes:jpeg,png,jpg,pdf|max:2048',
            'transfer_notes' => 'nullable|string|max:500',
        ]);

        $amount = $request->amount;
        $user = Auth::user();
        $paymentMethod = $request->payment_method ?? 'paypal';

        // إذا كان طريقة الدفع هي STC Pay، قم بإعادة توجيه الطلب إلى وحدة تحكم Moyasar
        if ($paymentMethod === 'stcpay') {
            return app()->make('App\Http\Controllers\MoyasarController')->createPayment($request);
        }

        // إذا كان طريقة الدفع هي التحويل البنكي
        if ($paymentMethod === 'bank_transfer') {
            // التحقق من وجود الملف
            if ($request->hasFile('transfer_receipt')) {
                // حفظ إيصال التحويل
                $receiptPath = $request->file('transfer_receipt')->store('receipts', 'public');

                // إنشاء معرف فريد للمعاملة
                $transactionId = 'BANK-' . Str::random(10);

                // تسجيل المعاملة في قاعدة البيانات
                $transaction = new PaymentTransaction([
                    'user_id' => $user->id,
                    'transaction_id' => $transactionId,
                    'payment_method' => 'bank_transfer',
                    'amount' => number_format($amount * 0.01, 2, '.', ''),
                    'points_amount' => $amount,
                    'status' => 'PENDING',
                    'payment_details' => json_encode([
                        'receipt_path' => $receiptPath,
                        'notes' => $request->transfer_notes,
                        'amount' => $amount,
                    ]),
                ]);
                $transaction->save();

                // إعادة توجيه المستخدم مع رسالة نجاح
                return redirect()->route('points.buy')->with('success', 'تم استلام إشعار التحويل البنكي بنجاح. سيتم إضافة النقاط إلى حسابك بعد التحقق من التحويل.');
            }

            return redirect()->route('points.buy')->with('error', 'يرجى إرفاق إيصال التحويل البنكي.');
        }

        // إنشاء معرف فريد للطلب
        $orderId = 'POINTS-' . Str::random(10);

        // حفظ معلومات الطلب في الجلسة
        session([
            'paypal_order_id' => $orderId,
            'points_amount' => $amount,
            'user_id' => $user->id
        ]);

        // إنشاء طلب دفع في PayPal
        try {
            $response = $this->createPayPalOrder($amount, $orderId);

            if (isset($response['id'])) {
                // البحث عن رابط الموافقة
                foreach ($response['links'] as $link) {
                    if ($link['rel'] === 'approve') {
                        // إعادة توجيه المستخدم إلى صفحة الدفع
                        return redirect($link['href']);
                    }
                }
            }

            // إذا وصلنا إلى هنا، فهناك خطأ ما
            Log::error('PayPal Error: No approval link found', ['response' => $response]);
            return redirect()->route('points.buy')->with('error', 'حدث خطأ أثناء إنشاء طلب الدفع. يرجى المحاولة مرة أخرى.');
        } catch (\Exception $e) {
            Log::error('PayPal Exception: ' . $e->getMessage());
            return redirect()->route('points.buy')->with('error', 'حدث خطأ أثناء الاتصال بـ PayPal. يرجى المحاولة مرة أخرى.');
        }
    }

    public function paypalSuccess(Request $request)
    {
        $paypalOrderId = $request->input('token');
        $pointsAmount = session('points_amount');
        $userId = session('user_id');

        if (!$paypalOrderId || !$pointsAmount || !$userId) {
            return redirect()->route('points.buy')->with('error', 'معلومات الطلب غير صالحة. يرجى المحاولة مرة أخرى.');
        }

        try {
            // التحقق من وجود المعاملة مسبقًا
            $existingTransaction = PaymentTransaction::where('transaction_id', $paypalOrderId)->first();

            if ($existingTransaction) {
                // إذا كانت المعاملة مكتملة بالفعل
                if ($existingTransaction->isCompleted()) {
                    session()->forget(['paypal_order_id', 'points_amount', 'user_id']);
                    return redirect()->route('points.buy')->with('success', "تم إضافة {$pointsAmount} نقطة إلى حسابك بنجاح!");
                }

                // إذا كانت المعاملة فاشلة
                if ($existingTransaction->isFailed()) {
                    session()->forget(['paypal_order_id', 'points_amount', 'user_id']);
                    return redirect()->route('points.buy')->with('error', 'فشلت عملية الدفع. يرجى المحاولة مرة أخرى.');
                }
            }

            // التحقق من حالة الدفع وإكمال المعاملة
            $response = $this->capturePayPalOrder($paypalOrderId);

            if (isset($response['status']) && $response['status'] === 'COMPLETED') {
                // تحديث نقاط المستخدم
                $user = User::find($userId);
                if ($user) {
                    // إنشاء أو تحديث المعاملة
                    if (!$existingTransaction) {
                        $transaction = new PaymentTransaction([
                            'user_id' => $userId,
                            'transaction_id' => $paypalOrderId,
                            'payment_method' => 'paypal',
                            'amount' => number_format($pointsAmount * 0.01, 2, '.', ''),
                            'points_amount' => $pointsAmount,
                            'status' => 'COMPLETED',
                            'payment_details' => $response,
                            'completed_at' => now(),
                        ]);
                        $transaction->save();
                    } else {
                        $existingTransaction->status = 'COMPLETED';
                        $existingTransaction->payment_details = $response;
                        $existingTransaction->completed_at = now();
                        $existingTransaction->save();
                    }

                    // إضافة النقاط للمستخدم
                    $user->points = ($user->points ?? 0) + $pointsAmount;
                    $user->save();

                    // مسح بيانات الجلسة
                    session()->forget(['paypal_order_id', 'points_amount', 'user_id']);

                    return redirect()->route('points.buy')->with('success', "تم إضافة {$pointsAmount} نقطة إلى حسابك بنجاح!");
                }
            } else {
                // تسجيل المعاملة الفاشلة
                if (!$existingTransaction) {
                    $transaction = new PaymentTransaction([
                        'user_id' => $userId,
                        'transaction_id' => $paypalOrderId,
                        'payment_method' => 'paypal',
                        'amount' => number_format($pointsAmount * 0.01, 2, '.', ''),
                        'points_amount' => $pointsAmount,
                        'status' => $response['status'] ?? 'FAILED',
                        'payment_details' => $response,
                    ]);
                    $transaction->save();
                } else {
                    $existingTransaction->status = $response['status'] ?? 'FAILED';
                    $existingTransaction->payment_details = $response;
                    $existingTransaction->save();
                }
            }

            Log::error('PayPal Capture Error', ['response' => $response]);
            return redirect()->route('points.buy')->with('error', 'لم يتم إكمال عملية الدفع بنجاح. يرجى المحاولة مرة أخرى.');
        } catch (\Exception $e) {
            Log::error('PayPal Capture Exception: ' . $e->getMessage());
            return redirect()->route('points.buy')->with('error', 'حدث خطأ أثناء التحقق من الدفع. يرجى الاتصال بالدعم.');
        }
    }

    public function paypalCancel()
    {
        session()->forget(['paypal_order_id', 'points_amount', 'user_id']);
        return redirect()->route('points.buy')->with('info', 'تم إلغاء عملية الدفع.');
    }

    /**
     * عرض سجل معاملات المستخدم
     */
    public function transactions()
    {
        $user = Auth::user();
        $transactions = PaymentTransaction::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return view('points.transactions', compact('transactions'));
    }

    /**
     * Show the form for adding points to a user.
     */
    public function create()
    {
        return view('admin.points.add');
    }

    /**
     * Add points to a user based on email or ID.
     */
    public function addPoints(Request $request)
    {
        $request->validate([
            'identifier' => 'required|string',
            'points' => 'required|integer|min:1',
        ]);

        $identifier = $request->identifier;
        $points = $request->points;

        // Find the user by email or ID
        $user = User::where('email', $identifier)
                    ->orWhere('id', $identifier)
                    ->first();

        if (!$user) {
            return redirect()->back()->with('error', 'User not found.');
        }

        // Add points to the user
        $user->points = ($user->points ?? 0) + $points;
        $user->save();

        return redirect()->back()->with('success', "{$points} points added to user {$user->email} (ID: {$user->id}).");
    }

    public function processPayPalPayment(Request $request)
    {
        // تسجيل البيانات الواردة للتصحيح
        Log::info('PayPal Payment Request', $request->all());

        // التحقق من البيانات
        $request->validate([
            'amount' => 'required|integer|in:5,10,20,50,100',
            'transaction_id' => 'required|string',
            'payment_status' => 'required|string',
        ]);

        try {
            $user = Auth::user();
            $transactionId = $request->transaction_id;
            $amount = $request->amount;
            $status = $request->payment_status;

            Log::info('Processing PayPal Payment', [
                'user_id' => $user->id,
                'transaction_id' => $transactionId,
                'amount' => $amount,
                'status' => $status
            ]);

            // التحقق من وجود المعاملة مسبقًا
            $existingTransaction = PaymentTransaction::where('transaction_id', $transactionId)->first();
            if ($existingTransaction) {
                Log::info('Found existing transaction', [
                    'transaction_id' => $existingTransaction->id,
                    'status' => $existingTransaction->status
                ]);

                // إذا كانت المعاملة مكتملة بالفعل
                if ($existingTransaction->isCompleted()) {
                    return response()->json([
                        'success' => true,
                        'message' => "تم إضافة {$amount} نقطة إلى حسابك بنجاح!"
                    ]);
                }

                // إذا كانت المعاملة فاشلة
                if ($existingTransaction->isFailed()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'فشلت عملية الدفع. يرجى المحاولة مرة أخرى.'
                    ]);
                }

                // إذا كانت المعاملة معلقة، نتحقق من حالتها الحالية
                if ($existingTransaction->isPending()) {
                    // التحقق من حالة المعاملة في PayPal
                    $paypalStatus = $this->checkPayPalTransactionStatus($transactionId);
                    Log::info('Checked PayPal transaction status', ['status' => $paypalStatus]);

                    if ($paypalStatus === 'COMPLETED') {
                        // تحديث حالة المعاملة وإضافة النقاط
                        $existingTransaction->status = 'COMPLETED';
                        $existingTransaction->completed_at = now();
                        $existingTransaction->save();

                        // إضافة النقاط للمستخدم
                        $user->points = ($user->points ?? 0) + $amount;
                        $user->save();

                        Log::info('Updated existing transaction to completed', [
                            'transaction_id' => $existingTransaction->id,
                            'points_added' => $amount
                        ]);

                        return response()->json([
                            'success' => true,
                            'message' => "تم إضافة {$amount} نقطة إلى حسابك بنجاح!"
                        ]);
                    } else if (in_array($paypalStatus, ['FAILED', 'DECLINED', 'EXPIRED'])) {
                        // تحديث حالة المعاملة إلى فاشلة
                        $existingTransaction->status = $paypalStatus;
                        $existingTransaction->save();

                        Log::info('Updated existing transaction to failed', [
                            'transaction_id' => $existingTransaction->id,
                            'status' => $paypalStatus
                        ]);

                        return response()->json([
                            'success' => false,
                            'message' => 'فشلت عملية الدفع. يرجى المحاولة مرة أخرى.'
                        ]);
                    } else {
                        // لا تزال المعاملة معلقة
                        Log::info('Transaction still pending', [
                            'transaction_id' => $existingTransaction->id,
                            'status' => $paypalStatus
                        ]);

                        return response()->json([
                            'success' => false,
                            'message' => 'لا تزال عملية الدفع قيد المعالجة. يرجى الانتظار أو التحقق لاحقًا.'
                        ]);
                    }
                }
            }

            // إنشاء معاملة جديدة
            $transaction = new PaymentTransaction([
                'user_id' => $user->id,
                'transaction_id' => $transactionId,
                'payment_method' => 'paypal',
                'amount' => number_format($amount * 0.01, 2, '.', ''),
                'points_amount' => $amount,
                'status' => $status,
                'payment_details' => $request->all(),
            ]);

            Log::info('Creating new transaction', [
                'user_id' => $user->id,
                'transaction_id' => $transactionId,
                'amount' => $amount,
                'status' => $status
            ]);

            // التحقق من حالة الدفع
            if ($status === 'COMPLETED') {
                $transaction->completed_at = now();
                $transaction->save();

                // إضافة النقاط للمستخدم
                $user->points = ($user->points ?? 0) + $amount;
                $user->save();

                Log::info('Payment completed successfully', [
                    'transaction_id' => $transaction->id,
                    'points_added' => $amount
                ]);

                return response()->json([
                    'success' => true,
                    'message' => "تم إضافة {$amount} نقطة إلى حسابك بنجاح!"
                ]);
            } else {
                // حفظ المعاملة بحالتها الحالية
                $transaction->save();

                Log::info('Payment not completed', [
                    'transaction_id' => $transaction->id,
                    'status' => $status
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'لم يتم إكمال عملية الدفع بنجاح. الحالة: ' . $status
                ]);
            }
        } catch (\Exception $e) {
            // تسجيل الخطأ بالتفصيل
            Log::error('PayPal Payment Processing Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء معالجة الدفع: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * التحقق من حالة معاملة PayPal
     */
    protected function checkPayPalTransactionStatus($transactionId)
    {
        try {
            $accessToken = $this->getAccessToken();

            $response = Http::withToken($accessToken)
                ->get("{$this->baseUrl}/v2/checkout/orders/{$transactionId}");

            if ($response->successful()) {
                $data = $response->json();
                return $data['status'] ?? 'UNKNOWN';
            }

            Log::error('PayPal Transaction Status Check Error', ['response' => $response->json()]);
            return 'ERROR';
        } catch (\Exception $e) {
            Log::error('PayPal Transaction Status Check Exception: ' . $e->getMessage());
            return 'ERROR';
        }
    }

    protected function getAccessToken()
    {
        // التحقق من وجود بيانات الاعتماد
        if (empty($this->clientId) || empty($this->clientSecret)) {
            Log::error('PayPal Credentials Missing', [
                'clientId' => $this->clientId ? 'set' : 'missing',
                'clientSecret' => $this->clientSecret ? 'set' : 'missing'
            ]);
            throw new \Exception('بيانات اعتماد PayPal غير مكتملة');
        }

        // استخدام withHeaders بدلاً من withBasicAuth
        $response = Http::withHeaders([
                'Authorization' => 'Basic ' . base64_encode($this->clientId . ':' . $this->clientSecret)
            ])
            ->asForm()
            ->post("{$this->baseUrl}/v1/oauth2/token", [
                'grant_type' => 'client_credentials'
            ]);

        if ($response->successful()) {
            return $response->json()['access_token'];
        }

        Log::error('PayPal Access Token Error', ['response' => $response->json()]);
        throw new \Exception('فشل في الحصول على رمز الوصول من PayPal');
    }

    protected function createPayPalOrder($amount, $orderId)
    {
        $accessToken = $this->getAccessToken();

        $response = Http::withToken($accessToken)
            ->post("{$this->baseUrl}/v2/checkout/orders", [
                'intent' => 'CAPTURE',
                'purchase_units' => [
                    [
                        'reference_id' => $orderId,
                        'description' => "شراء {$amount} نقطة",
                        'amount' => [
                            'currency_code' => 'USD',
                            'value' => number_format($amount * 0.01, 2, '.', '')
                        ]
                    ]
                ],
                'application_context' => [
                    'return_url' => route('points.paypal.success', [], true),
                    'cancel_url' => route('points.paypal.cancel', [], true),
                ],
            ]);

        if ($response->successful()) {
            return $response->json();
        }

        Log::error('PayPal Order Creation Error', ['response' => $response->json()]);
        throw new \Exception('فشل في إنشاء طلب PayPal');
    }

    protected function capturePayPalOrder($orderId)
    {
        $accessToken = $this->getAccessToken();

        $response = Http::withToken($accessToken)
            ->post("{$this->baseUrl}/v2/checkout/orders/{$orderId}/capture");

        if ($response->successful()) {
            return $response->json();
        }

        Log::error('PayPal Order Capture Error', ['response' => $response->json()]);
        throw new \Exception('فشل في إكمال طلب PayPal');
    }
}
