const translations = {
    en: {
        title: 'Title:',
        description: 'Description:',
        requirements: 'Requirements:',
        salary: 'Salary:',
        submit: 'Submit',
        postJob: 'Post a Job'
    },
    ar: {
        title: 'العنوان:',
        description: 'الوصف:',
        requirements: 'المتطلبات:',
        salary: 'الراتب:',
        submit: 'إرسال',
        postJob: 'نشر وظيفة'
    }
};

let currentLanguage = 'en';

function translatePage() {
    const elements = document.querySelectorAll('[data-translate]');
    elements.forEach(element => {
        const key = element.getAttribute('data-translate');
        element.textContent = translations[currentLanguage][key];
    });

    document.body.dir = currentLanguage === 'ar' ? 'rtl' : 'ltr';
}

document.getElementById('language-toggle').addEventListener('click', function() {
    currentLanguage = currentLanguage === 'en' ? 'ar' : 'en';
    translatePage();
});

translatePage();