<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Settings
    |--------------------------------------------------------------------------
    |
    | Set some default values. It is possible to add all defines that can be set
    | in dompdf_config.inc.php. You can also override the entire config file.
    |
    */
    'show_warnings' => false,   // Throw an Exception on warnings from dompdf

    /*
    |--------------------------------------------------------------------------
    | Options
    |--------------------------------------------------------------------------
    |
    | Array of options for the DomPDF instance.
    */
    'options' => [
        'font_dir' => storage_path('fonts/'), // Directory where font files are stored
        'font_cache' => storage_path('fonts/'), // Directory where font cache is stored
        'temp_dir' => sys_get_temp_dir(), // Directory for temporary files
        'chroot' => realpath(base_path()), // Directory for the renderer to chroot to
        'allowed_protocols' => [
            'file://' => ['rules' => []],
            'http://' => ['rules' => []],
            'https://' => ['rules' => []],
        ],
        'log_output_file' => null, // Log output file
        'enable_font_subsetting' => false, // Enable font subsetting
        'pdf_backend' => 'CPDF', // PDF rendering backend to use
        'default_media_type' => 'screen', // Default media type
        'default_paper_size' => 'a4', // Default paper size
        'default_paper_orientation' => 'portrait', // Default paper orientation
        'default_font' => 'sans-serif', // Default font
        'dpi' => 96, // DPI setting
        'enable_php' => false, // Enable PHP in HTML
        'enable_javascript' => true, // Enable JavaScript
        'enable_remote' => true, // Enable remote file access
        'font_height_ratio' => 1.1, // Font height ratio
        'enable_html5_parser' => true, // Enable HTML5 parser
        'is_remote_enabled' => true, // Enable remote file access
        'is_html5_parser_enabled' => true, // Enable HTML5 parser
        'enable_css_float' => true, // Enable CSS float
        'enable_css_line_break' => true, // Enable CSS line break
        'enable_css_counter' => true, // Enable CSS counter
        'enable_css_background' => true, // Enable CSS background
        'enable_css_border' => true, // Enable CSS border
        'enable_css_margin' => true, // Enable CSS margin
        'enable_css_padding' => true, // Enable CSS padding
        'enable_css_position' => true, // Enable CSS position
        'enable_css_display' => true, // Enable CSS display
        'enable_css_visibility' => true, // Enable CSS visibility
        'enable_css_overflow' => true, // Enable CSS overflow
        'enable_css_z_index' => true, // Enable CSS z-index
        'enable_css_text_align' => true, // Enable CSS text-align
        'enable_css_text_decoration' => true, // Enable CSS text-decoration
        'enable_css_text_transform' => true, // Enable CSS text-transform
        'enable_css_text_indent' => true, // Enable CSS text-indent
        'enable_css_text_spacing' => true, // Enable CSS text-spacing
        'enable_css_text_shadow' => true, // Enable CSS text-shadow
        'enable_css_text_rendering' => true, // Enable CSS text-rendering
        'enable_css_text_overflow' => true, // Enable CSS text-overflow
        'enable_css_text_justify' => true, // Enable CSS text-justify
        'enable_css_text_emphasis' => true, // Enable CSS text-emphasis
        'enable_css_text_combine_upright' => true, // Enable CSS text-combine-upright
        'enable_css_text_orientation' => true, // Enable CSS text-orientation
        'enable_css_text_underline_position' => true, // Enable CSS text-underline-position
        'enable_css_text_underline_offset' => true, // Enable CSS text-underline-offset
        'enable_css_text_decoration_skip' => true, // Enable CSS text-decoration-skip
        'enable_css_text_decoration_skip_ink' => true, // Enable CSS text-decoration-skip-ink
        'enable_css_text_decoration_line' => true, // Enable CSS text-decoration-line
        'enable_css_text_decoration_color' => true, // Enable CSS text-decoration-color
        'enable_css_text_decoration_style' => true, // Enable CSS text-decoration-style
        'enable_css_text_decoration_thickness' => true, // Enable CSS text-decoration-thickness
        'enable_css_text_align_last' => true, // Enable CSS text-align-last
        'enable_css_text_justify_trim' => true, // Enable CSS text-justify-trim
        'enable_css_text_kashida_space' => true, // Enable CSS text-kashida-space
        'enable_css_text_autospace' => true, // Enable CSS text-autospace
        'enable_css_text_wrap' => true, // Enable CSS text-wrap
        'enable_css_text_overflow_mode' => true, // Enable CSS text-overflow-mode
        'enable_css_text_overflow_ellipsis' => true, // Enable CSS text-overflow-ellipsis
        'enable_css_text_overflow_ellipsis_word' => true, // Enable CSS text-overflow-ellipsis-word
        'enable_css_text_overflow_ellipsis_position' => true, // Enable CSS text-overflow-ellipsis-position
        'enable_css_text_overflow_ellipsis_position_word' => true, // Enable CSS text-overflow-ellipsis-position-word
        'enable_css_text_overflow_ellipsis_position_line' => true, // Enable CSS text-overflow-ellipsis-position-line
        'enable_css_text_overflow_ellipsis_position_line_word' => true, // Enable CSS text-overflow-ellipsis-position-line-word
        'enable_css_text_overflow_ellipsis_position_line_word_char' => true, // Enable CSS text-overflow-ellipsis-position-line-word-char
    ],

    /*
    |--------------------------------------------------------------------------
    | Chroot
    |--------------------------------------------------------------------------
    |
    | Enable or disable the chroot environment.
    */
    'chroot' => base_path(),
];
