<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $testData['title'] ?? 'اختبار البريد الإلكتروني' }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }
        .header .logo {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        .content {
            padding: 2rem;
        }
        .success-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 50rem;
            display: inline-block;
            margin-bottom: 1.5rem;
            font-weight: 500;
        }
        .message-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 2px solid #2196f3;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1.5rem 0;
        }
        .info-item {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            border-right: 4px solid #007bff;
        }
        .info-label {
            font-weight: 600;
            color: #007bff;
            margin-bottom: 0.5rem;
        }
        .info-value {
            color: #333;
        }
        .features {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        .features h3 {
            color: #007bff;
            margin-bottom: 1rem;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #dee2e6;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list li::before {
            content: "✅";
            margin-left: 0.5rem;
        }
        .footer {
            background: #343a40;
            color: white;
            padding: 1.5rem;
            text-align: center;
        }
        .footer a {
            color: #007bff;
            text-decoration: none;
        }
        .footer a:hover {
            text-decoration: underline;
        }
        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            text-decoration: none;
            border-radius: 50rem;
            font-weight: 500;
            margin: 1rem 0;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
            transform: translateY(-2px);
        }
        @media (max-width: 600px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
            .container {
                margin: 0;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">📧</div>
            <h1>{{ $testData['app_name'] ?? 'منصة انشر' }}</h1>
            <p>اختبار نظام البريد الإلكتروني</p>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="success-badge">
                ✅ تم الإرسال بنجاح
            </div>

            <h2>{{ $testData['title'] ?? 'اختبار البريد الإلكتروني' }}</h2>

            <div class="message-box">
                <p><strong>{{ $testData['message'] ?? 'تم إرسال هذا البريد بنجاح من منصة انشر!' }}</strong></p>
                <p>إذا وصلك هذا البريد، فهذا يعني أن إعدادات البريد الإلكتروني تعمل بشكل صحيح.</p>
            </div>

            <!-- معلومات الاختبار -->
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">وقت الإرسال</div>
                    <div class="info-value">{{ $testData['timestamp'] ?? now()->format('Y-m-d H:i:s') }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">حالة الإرسال</div>
                    <div class="info-value">نجح ✅</div>
                </div>
                <div class="info-item">
                    <div class="info-label">مزود البريد</div>
                    <div class="info-value">{{ config('mail.default') ?? 'SMTP' }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">خادم البريد</div>
                    <div class="info-value">{{ config('mail.mailers.smtp.host') ?? 'غير محدد' }}</div>
                </div>
            </div>

            <!-- الميزات المفعلة -->
            <div class="features">
                <h3>الميزات المفعلة في النظام:</h3>
                <ul class="feature-list">
                    <li>إرسال إيميلات التحقق</li>
                    <li>إشعارات الوظائف الجديدة</li>
                    <li>تنبيهات الرسائل</li>
                    <li>تأكيدات العمليات</li>
                    <li>تقارير النشاط</li>
                    <li>رسائل الترحيب</li>
                </ul>
            </div>

            <div style="text-align: center;">
                <a href="{{ config('app.url') }}" class="btn">زيارة الموقع</a>
            </div>

            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 1rem; margin: 1.5rem 0;">
                <strong>ملاحظة مهمة:</strong>
                <p>هذا بريد اختبار للتأكد من عمل نظام البريد الإلكتروني. إذا لم تطلب هذا الاختبار، يمكنك تجاهل هذا البريد.</p>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>&copy; {{ date('Y') }} {{ $testData['app_name'] ?? 'منصة انشر' }}. جميع الحقوق محفوظة.</p>
            <p>
                <a href="{{ config('app.url') }}">الموقع الرئيسي</a> |
                <a href="{{ config('app.url') }}/contact">اتصل بنا</a> |
                <a href="{{ config('app.url') }}/privacy">سياسة الخصوصية</a>
            </p>
            <p style="font-size: 0.875rem; color: #adb5bd; margin-top: 1rem;">
                تم إرسال هذا البريد تلقائياً من نظام {{ $testData['app_name'] ?? 'منصة انشر' }}
            </p>
        </div>
    </div>
</body>
</html>
