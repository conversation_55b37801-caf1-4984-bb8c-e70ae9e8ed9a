@php
    use Illuminate\Support\Facades\Schema;
@endphp
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ $ad->title }} | تفاصيل الإعلان</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <style>
        :root {
            --primary-color: #0d6efd;
            --secondary-color: #6c757d;
            --success-color: #198754;
            --info-color: #0dcaf0;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --border-radius: 1rem;
            --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        body {
            background-color: #f1f3f5;
            font-family: 'Tajawal', sans-serif;
            direction: rtl;
            text-align: right;
            color: #333;
            line-height: 1.6;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-color), #0a58ca);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 var(--border-radius) var(--border-radius);
            box-shadow: var(--box-shadow);
        }

        .page-title {
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }

        .breadcrumb-item a:hover {
            color: white;
            text-decoration: underline;
        }

        .breadcrumb-item.active {
            color: white;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            color: rgba(255, 255, 255, 0.6);
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 1.25rem 1.5rem;
        }

        .card-header h4 {
            margin-bottom: 0;
            font-weight: 600;
            color: var(--primary-color);
            display: flex;
            align-items: center;
        }

        .card-header h4 i {
            margin-left: 0.75rem;
            opacity: 0.8;
        }

        .card-body {
            padding: 1.5rem;
        }

        .info-item {
            padding: 1rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .info-item:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }

        .info-item i {
            width: 2rem;
            text-align: center;
            font-size: 1.25rem;
            margin-left: 0.75rem;
        }

        .info-item strong {
            margin-left: 0.5rem;
            color: var(--dark-color);
        }

        .info-value {
            flex: 1;
        }

        .badge {
            font-weight: 500;
            padding: 0.5rem 0.75rem;
            border-radius: 50rem;
        }

        .btn {
            border-radius: 50rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .btn i {
            margin-left: 0.5rem;
        }

        /* أنماط معرض الصور */
        .ad-gallery {
            position: relative;
        }

        .ad-image-container {
            width: 100%;
            height: 450px;
            overflow: hidden;
            border-radius: 0.75rem;
            box-shadow: var(--box-shadow);
            background-color: var(--light-color);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .ad-image-full {
            width: 100%;
            height: 100%;
            object-fit: contain;
            transition: transform 0.3s ease;
        }

        .ad-image-container:hover .ad-image-full:not(.no-image) {
            transform: scale(1.03);
        }

        .no-image {
            max-width: 80%;
            max-height: 80%;
            opacity: 0.7;
        }

        /* أنماط سلايدر الصور */
        .swiper {
            width: 100%;
            height: 450px;
            border-radius: 0.75rem;
            box-shadow: var(--box-shadow);
            background-color: var(--light-color);
        }

        .swiper-slide {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .swiper-slide img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .swiper-button-next, .swiper-button-prev {
            color: var(--primary-color);
            background-color: rgba(255, 255, 255, 0.7);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .swiper-button-next:after, .swiper-button-prev:after {
            font-size: 18px;
            font-weight: bold;
        }

        .swiper-pagination-bullet-active {
            background-color: var(--primary-color);
        }

        .featured-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background-color: var(--warning-color);
            color: var(--dark-color);
            font-weight: 600;
            padding: 0.5rem 1rem;
            border-radius: 50rem;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .ad-meta {
            display: flex;
            justify-content: space-between;
            padding: 1rem 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            margin-bottom: 1.5rem;
        }

        .ad-meta-item {
            display: flex;
            align-items: center;
            color: var(--secondary-color);
            font-size: 0.9rem;
        }

        .ad-meta-item i {
            margin-left: 0.5rem;
            opacity: 0.8;
        }

        .ad-price {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--success-color);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .ad-price i {
            margin-left: 0.75rem;
            font-size: 1.25rem;
            opacity: 0.8;
        }

        .ad-description {
            line-height: 1.8;
            color: #555;
            margin-bottom: 1.5rem;
            white-space: pre-line;
        }

        /* شبكة أزرار الإجراءات الرئيسية */
        .action-buttons-grid {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 1rem;
            align-items: start;
        }

        @media (max-width: 768px) {
            .action-buttons-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }
        }

        /* شبكة أزرار التواصل */
        .contact-buttons-grid {
            display: grid;
            gap: 0.75rem;
            margin-top: 1rem;
        }

        .contact-button {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 12px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
            width: 100%;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .contact-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .contact-button:hover::before {
            opacity: 1;
        }

        .contact-icon {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 1rem;
            transition: all 0.3s ease;
        }

        .contact-button:hover .contact-icon {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .contact-icon i {
            font-size: 1.25rem;
            color: white;
        }

        .contact-info {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            text-align: right;
            flex: 1;
        }

        .contact-label {
            font-size: 1rem;
            font-weight: 600;
            line-height: 1.2;
            margin-bottom: 0.25rem;
            color: white;
        }

        .contact-number {
            font-size: 0.85rem;
            opacity: 0.9;
            font-weight: 400;
            line-height: 1;
            color: white;
        }

        .contact-button.phone {
            background: linear-gradient(135deg, #198754 0%, #157347 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(25, 135, 84, 0.3);
        }

        .contact-button.whatsapp {
            background: linear-gradient(135deg, #25D366 0%, #1DA851 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
        }

        .contact-button.email {
            background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
        }

        .contact-button:hover {
            transform: translateY(-2px);
            color: white;
        }

        .contact-button.phone:hover {
            box-shadow: 0 6px 20px rgba(25, 135, 84, 0.4);
        }

        .contact-button.whatsapp:hover {
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        }

        .contact-button.email:hover {
            box-shadow: 0 6px 20px rgba(13, 110, 253, 0.4);
        }

        /* فاصل التواصل */
        .contact-divider {
            margin: 1.5rem 0;
            border: none;
            height: 1px;
            background: linear-gradient(to left, transparent, rgba(0,0,0,0.1), transparent);
        }

        /* زر الإبلاغ */
        .report-button {
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #dc3545 0%, #b02a37 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            text-decoration: none;
            font-size: 0.9rem;
            box-shadow: 0 2px 8px rgba(220, 53, 69, 0.2);
        }

        .report-button:hover {
            background: linear-gradient(135deg, #b02a37 0%, #8b1e2b 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
            color: white;
        }

        .report-button i {
            margin-left: 0.5rem;
            font-size: 0.9rem;
        }

        .advertiser-info {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 0.75rem;
            background-color: rgba(0, 0, 0, 0.02);
            margin-top: 1.5rem;
        }

        .advertiser-avatar {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.25rem;
            margin-left: 1rem;
        }

        .advertiser-details {
            flex: 1;
        }

        .advertiser-name {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 0.25rem;
        }

        .advertiser-date {
            color: var(--secondary-color);
            font-size: 0.9rem;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
        }

        @media (max-width: 768px) {
            .ad-image-container {
                height: 300px;
            }

            .ad-meta {
                flex-direction: column;
                gap: 0.5rem;
            }

            .action-buttons {
                flex-direction: column;
            }
        }

        /* تنسيقات التعليقات */
        .comments-list {
            margin-top: 2rem;
        }

        .comment-item {
            border: 1px solid #eee;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
            background-color: #fff;
        }

        .comment-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.75rem;
        }

        .comment-user {
            display: flex;
            align-items: center;
        }

        .comment-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            font-weight: 600;
            margin-left: 0.75rem;
            overflow: hidden;
        }

        .comment-profile-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .advertiser-profile-image {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 50%;
        }

        .comment-name {
            font-weight: 600;
            font-size: 0.9375rem;
            margin-bottom: 0.125rem;
        }

        .comment-date {
            color: var(--secondary-color);
            font-size: 0.8125rem;
        }

        .comment-actions {
            display: flex;
            gap: 0.5rem;
        }

        .comment-content {
            line-height: 1.5;
            white-space: pre-line;
        }

        .no-comments {
            text-align: center;
            padding: 2rem 0;
            color: var(--secondary-color);
        }

        .no-comments i {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
            color: #dee2e6;
        }
    </style>
</head>
<body>
    <!-- رأس الصفحة -->
    <header class="page-header">
        <div class="container">
            <h1 class="page-title">{{ $ad->title }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('ads.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('ads.index', ['category' => $ad->category]) }}">
                        @php
                            $categories = [
                                'cars' => 'سيارات',
                                'realestate' => 'عقارات',
                                'devices' => 'أجهزة',
                                'animals' => 'حيوانات',
                                'furniture' => 'اثاث',
                                'jobs' => 'وظائف',
                                'services' => 'خدمات',
                                'fashion' => 'ازياء',
                                'games' => 'العاب',
                                'rarities' => 'نوادر',
                                'art' => 'الفنون',
                                'trips' => 'الرحلات',
                                'food' => 'اطعمة',
                                'gardens' => 'الحدائق',
                                'occasions' => 'مناسبات',
                                'tourism' => 'سياحة',
                                'lost' => 'مفقودات',
                                'coach' => 'تدريب',
                                'code' => 'برمجة',
                                'fund' => 'مشاريع واستثمارات',
                                'more' => 'المزيد'
                            ];
                        @endphp
                        {{ $categories[$ad->category] ?? $ad->category }}
                    </a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ $ad->title }}</li>
                </ol>
            </nav>
        </div>
    </header>

    <div class="container">
        <div class="row">
            <!-- القسم الرئيسي -->
            <div class="col-lg-8">
                <!-- معرض الصور -->
                <div class="card">
                    <div class="card-body p-0">
                        <div class="ad-gallery">
                            @if($ad->is_featured)
                                <div class="featured-badge">
                                    <i class="fas fa-star"></i> إعلان مميز
                                </div>
                            @endif

                            @php
                                $adImages = $ad->getAllImages();
                            @endphp

                            @if(count($adImages) > 1)
                                <!-- Swiper -->
                                <div class="swiper adImagesSwiper">
                                    <div class="swiper-wrapper">
                                        @foreach($adImages as $image)
                                            <div class="swiper-slide">
                                                <img src="{{ $image->url }}" alt="{{ $ad->title }}">
                                            </div>
                                        @endforeach
                                    </div>

                                    <!-- Add Pagination -->
                                    <div class="swiper-pagination"></div>

                                    <!-- Add Navigation -->
                                    <div class="swiper-button-next"></div>
                                    <div class="swiper-button-prev"></div>
                                </div>
                            @else
                                <div class="ad-image-container">
                                    <img src="{{ $ad->getImageUrl() }}" alt="{{ $ad->title }}" class="ad-image-full">
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الإعلان -->
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-info-circle"></i> تفاصيل الإعلان</h4>
                    </div>
                    <div class="card-body">
                        <div class="ad-meta">
                            <div class="ad-meta-item">
                                <i class="fas fa-tag"></i>
                                <span>
                                @php
                                    $categories = [
                                        'cars' => 'سيارات',
                                        'realestate' => 'عقارات',
                                        'devices' => 'أجهزة',
                                        'animals' => 'حيوانات',
                                        'furniture' => 'اثاث',
                                        'jobs' => 'وظائف',
                                        'services' => 'خدمات',
                                        'fashion' => 'ازياء',
                                        'games' => 'العاب',
                                        'rarities' => 'نوادر',
                                        'art' => 'الفنون',
                                        'trips' => 'الرحلات',
                                        'food' => 'اطعمة',
                                        'gardens' => 'الحدائق',
                                        'occasions' => 'مناسبات',
                                        'tourism' => 'سياحة',
                                        'lost' => 'مفقودات',
                                        'coach' => 'تدريب',
                                        'code' => 'برمجة',
                                        'fund' => 'مشاريع واستثمارات',
                                        'more' => 'المزيد'
                                    ];
                                @endphp
                                {{ $categories[$ad->category] ?? $ad->category ?? 'غير محدد' }}
                                </span>
                            </div>
                            <div class="ad-meta-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>{{ $ad->location ?? 'غير محدد' }}</span>
                            </div>
                            <div class="ad-meta-item">
                                <i class="far fa-clock"></i>
                                <span>{{ $ad->created_at->format('Y/m/d') }}</span>
                            </div>
                            <div class="ad-meta-item">
                                <i class="fas fa-eye"></i>
                                <span>{{ $ad->getFormattedViews() }} مشاهدة</span>
                            </div>
                        </div>

                        @if($ad->price)
                            <div class="ad-price">
                                <i class="fas fa-tag"></i>
                                <span>{{ number_format($ad->price, 2) }} ريال سعودي</span>
                            </div>
                        @endif

                        <div class="ad-description">
                            {{ $ad->description }}
                        </div>

                        <div class="advertiser-info">
                            <div class="advertiser-avatar">
                                @if($ad->user && $ad->user->hasProfileImage())
                                    <img src="{{ $ad->user->getProfileImageUrl() }}" alt="صورة {{ $ad->user->name }}"
                                         class="advertiser-profile-image">
                                @else
                                    <img src="{{ $ad->user ? $ad->user->getDefaultAvatar() : 'https://ui-avatars.com/api/?name=مستخدم&background=random&color=fff&size=50&rounded=true' }}"
                                         alt="صورة افتراضية" class="advertiser-profile-image">
                                @endif
                            </div>
                            <div class="advertiser-details">
                                <div class="advertiser-name">{{ $ad->user->name ?? 'مستخدم' }}</div>
                                <div class="advertiser-date">عضو منذ {{ $ad->user->created_at->format('Y/m/d') ?? 'غير معروف' }}</div>
                            </div>
                        </div>

                        @if ($ad->user_id === auth()->id())
                            <div class="action-buttons">
                                <a href="{{ route('ads.edit', $ad->id) }}" class="btn btn-warning">
                                    <i class="fas fa-edit"></i> تعديل الإعلان
                                </a>
                                <form action="{{ route('ads.destroy', $ad->id) }}" method="POST" class="d-inline-block w-100" onsubmit="return confirm('هل أنت متأكد من حذف هذا الإعلان؟ لا يمكن التراجع عن هذه العملية.');">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger w-100">
                                        <i class="fas fa-trash"></i> حذف الإعلان
                                    </button>
                                </form>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- قسم التعليقات -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h4><i class="fas fa-comments"></i> التعليقات</h4>
                    </div>
                    <div class="card-body">
                        @if(session('error'))
                            <div class="alert alert-danger">
                                {{ session('error') }}
                            </div>
                        @endif

                        @if(session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif

                        <!-- التحقق من إعدادات التعليقات لصاحب الإعلان -->
                        @php
                            $adOwnerAllowsComments = $ad->user && $ad->user->allow_comments;
                        @endphp

                        <!-- تنبيه عدم قبول التعليقات -->
                        @if(!$adOwnerAllowsComments)
                            <div class="alert alert-warning mb-4">
                                <i class="fas fa-comment-slash me-2"></i>
                                <strong>صاحب الإعلان لا يقبل التعليقات</strong>
                                <br>
                                <small class="text-muted">تم إيقاف التعليقات على هذا الإعلان من قبل صاحب الإعلان.</small>
                            </div>
                        @endif

                        <!-- نموذج إضافة تعليق -->
                        @auth
                            @php
                                $commentsCount = 0;
                                $canComment = true;
                                try {
                                    if (Schema::hasTable('comments')) {
                                        $commentsCount = Auth::user()->comments()->where('ad_id', $ad->id)->count();
                                        $canComment = $commentsCount < 5 && $adOwnerAllowsComments;
                                    } else {
                                        $canComment = $adOwnerAllowsComments;
                                    }
                                } catch (\Exception $e) {
                                    $canComment = $adOwnerAllowsComments;
                                }
                            @endphp

                            @if($canComment && $adOwnerAllowsComments)
                                <form action="{{ route('comments.store', $ad->id) }}" method="POST" class="mb-4">
                                    @csrf
                                    <div class="form-group">
                                        <label for="content">أضف تعليقك</label>
                                        <textarea name="content" id="content" rows="3" class="form-control @error('content') is-invalid @enderror" required>{{ old('content') }}</textarea>
                                        @error('content')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <button type="submit" class="btn btn-primary mt-2">
                                        <i class="fas fa-paper-plane"></i> إرسال
                                    </button>
                                </form>
                            @elseif(!$adOwnerAllowsComments)
                                <!-- لا نعرض شيء هنا لأن التنبيه معروض أعلاه -->
                            @else
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    لقد وصلت إلى الحد الأقصى من التعليقات المسموح بها (5 تعليقات) على هذا الإعلان.
                                </div>
                            @endif
                        @else
                            @if($adOwnerAllowsComments)
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    يرجى <a href="{{ route('login') }}">تسجيل الدخول</a> لإضافة تعليق.
                                </div>
                            @endif
                        @endauth

                        <!-- قائمة التعليقات -->
                        <div class="comments-list mt-4">
                            @php
                                $commentsCount = 0;
                                $comments = collect([]);
                                try {
                                    if (Schema::hasTable('comments')) {
                                        $comments = $ad->comments;
                                        $commentsCount = $comments->count();
                                    }
                                } catch (\Exception $e) {
                                    // تجاهل الخطأ
                                }
                            @endphp

                            <h5>{{ $commentsCount }} تعليقات</h5>

                            @if($commentsCount > 0)
                                @foreach($comments as $comment)
                                    <div class="comment-item" id="comment-{{ $comment->id }}">
                                        <div class="comment-header">
                                            <div class="comment-user">
                                                <div class="comment-avatar">
                                                    @if($comment->user && $comment->user->hasProfileImage())
                                                        <img src="{{ $comment->user->getProfileImageUrl() }}" alt="صورة {{ $comment->user->name }}"
                                                             class="comment-profile-image">
                                                    @else
                                                        <img src="{{ $comment->user ? $comment->user->getDefaultAvatar() : 'https://ui-avatars.com/api/?name=مستخدم&background=random&color=fff&size=40&rounded=true' }}"
                                                             alt="صورة افتراضية" class="comment-profile-image">
                                                    @endif
                                                </div>
                                                <div class="comment-info">
                                                    <div class="comment-name">{{ $comment->user->name }}</div>
                                                    <div class="comment-date">{{ $comment->created_at->diffForHumans() }}</div>
                                                </div>
                                            </div>

                                            @if(Auth::check() && (Auth::id() == $comment->user_id || (method_exists(Auth::user(), 'hasRole') && Auth::user()->hasRole('admin'))))
                                                <div class="comment-actions">
                                                    @if(Auth::id() == $comment->user_id)
                                                        <button type="button" class="btn btn-sm btn-link edit-comment-btn" data-comment-id="{{ $comment->id }}" data-comment-content="{{ $comment->content }}">
                                                            <i class="fas fa-edit"></i> تعديل
                                                        </button>
                                                    @endif

                                                    <form action="{{ route('comments.destroy', $comment->id) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-link text-danger" onclick="return confirm('هل أنت متأكد من حذف هذا التعليق؟ لا يمكن التراجع عن هذه العملية.')">
                                                            <i class="fas fa-trash"></i> حذف
                                                        </button>
                                                    </form>
                                                </div>
                                            @endif
                                        </div>

                                        <div class="comment-content" id="comment-content-{{ $comment->id }}">
                                            {{ $comment->content }}
                                        </div>

                                        <div class="comment-edit-form" id="comment-edit-form-{{ $comment->id }}" style="display: none;">
                                            <form action="{{ route('comments.update', $comment->id) }}" method="POST">
                                                @csrf
                                                @method('PUT')
                                                <div class="form-group">
                                                    <textarea name="content" rows="3" class="form-control" required>{{ $comment->content }}</textarea>
                                                </div>
                                                <div class="mt-2">
                                                    <button type="submit" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-save"></i> حفظ
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-secondary cancel-edit-btn" data-comment-id="{{ $comment->id }}">
                                                        <i class="fas fa-times"></i> إلغاء
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="no-comments">
                                    <i class="far fa-comment-dots"></i>
                                    <p>لا توجد تعليقات حتى الآن. كن أول من يعلق!</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- القسم الجانبي -->
            <div class="col-lg-4">
                <!-- معلومات الاتصال -->
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-address-card"></i> معلومات الاتصال</h4>
                    </div>
                    <div class="card-body">
                        <!-- أزرار الإجراءات الرئيسية -->
                        <div class="action-buttons-grid mb-4">
                            <!-- زر المحادثة - الأولوية الأولى -->
                            @include('components.chat-button', ['userId' => $ad->user_id, 'adId' => $ad->id])

                            <!-- زر الحفظ -->
                            @include('components.save-button', ['itemType' => 'ad', 'itemId' => $ad->id])
                        </div>

                        <!-- أزرار التواصل الأخرى -->
                        <div class="contact-buttons-grid">
                            @if($ad->whatsapp)
                                <a href="https://wa.me/{{ preg_replace('/[^0-9]/', '', $ad->whatsapp) }}" target="_blank" class="contact-button whatsapp">
                                    <div class="contact-icon">
                                        <i class="fab fa-whatsapp"></i>
                                    </div>
                                    <div class="contact-info">
                                        <span class="contact-label">واتساب</span>
                                        <small class="contact-number">{{ $ad->whatsapp }}</small>
                                    </div>
                                </a>
                            @endif

                            @if($ad->phone)
                                <a href="tel:{{ $ad->phone }}" class="contact-button phone">
                                    <div class="contact-icon">
                                        <i class="fas fa-phone"></i>
                                    </div>
                                    <div class="contact-info">
                                        <span class="contact-label">اتصال</span>
                                        <small class="contact-number">{{ $ad->phone }}</small>
                                    </div>
                                </a>
                            @endif

                            @if($ad->email)
                                <a href="mailto:{{ $ad->email }}" class="contact-button email">
                                    <div class="contact-icon">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <div class="contact-info">
                                        <span class="contact-label">إيميل</span>
                                        <small class="contact-number">{{ Str::limit($ad->email, 20) }}</small>
                                    </div>
                                </a>
                            @endif
                        </div>

                        <!-- فاصل -->
                        <hr class="contact-divider">

                        <!-- زر الإبلاغ -->
                        @auth
                            @if($ad->user_id !== auth()->id())
                                <button type="button" class="report-button" data-bs-toggle="modal" data-bs-target="#reportAdModal">
                                    <i class="fas fa-flag"></i>
                                    <span>الإبلاغ عن هذا الإعلان</span>
                                </button>
                            @endif
                        @else
                            <a href="{{ route('login') }}" class="report-button">
                                <i class="fas fa-flag"></i>
                                <span>سجل دخول للإبلاغ عن هذا الإعلان</span>
                            </a>
                        @endauth
                    </div>
                </div>

                <!-- إعلانات مشابهة -->
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-list"></i> إعلانات مشابهة</h4>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            @if($similarAds->count() > 0)
                                @foreach($similarAds as $similarAd)
                                    <a href="{{ route('ads.show', $similarAd->id) }}" class="list-group-item list-group-item-action d-flex align-items-center p-3">
                                        <div class="flex-shrink-0 me-3" style="width: 60px; height: 60px; overflow: hidden; border-radius: 0.5rem;">
                                            <img src="{{ $similarAd->getImageUrl() }}" alt="{{ $similarAd->title }}" class="w-100 h-100 object-fit-cover">
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1 text-truncate">{{ $similarAd->title }}</h6>
                                            <small class="text-muted">{{ $similarAd->price ? number_format($similarAd->price, 0) . ' ريال' : 'السعر غير محدد' }}</small>
                                        </div>
                                    </a>
                                @endforeach
                            @else
                                <div class="text-center py-3 text-muted">
                                    <i class="fas fa-info-circle mb-2 d-block" style="font-size: 2rem;"></i>
                                    لا توجد إعلانات مشابهة حاليًا
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- زر العودة -->
                <a href="{{ route('ads.index') }}" class="btn btn-primary w-100 mb-4">
                    <i class="fas fa-arrow-right"></i> العودة إلى قائمة الإعلانات
                </a>
            </div>
        </div>
    </div>

    <!-- نافذة الإبلاغ عن الإعلان -->
    @auth
    <div class="modal fade" id="reportAdModal" tabindex="-1" aria-labelledby="reportAdModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="reportAdModalLabel">الإبلاغ عن إعلان</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <form action="{{ route('ads.report', $ad->id) }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="report_type" class="form-label">نوع البلاغ</label>
                            <select name="report_type" id="report_type" class="form-select" required>
                                <option value="">اختر نوع البلاغ</option>
                                <option value="محتوى غير لائق">محتوى غير لائق</option>
                                <option value="إعلان مكرر">إعلان مكرر</option>
                                <option value="معلومات خاطئة">معلومات خاطئة</option>
                                <option value="احتيال">احتيال</option>
                                <option value="انتهاك حقوق الملكية">انتهاك حقوق الملكية</option>
                                <option value="سبب آخر">سبب آخر</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="reason" class="form-label">تفاصيل البلاغ</label>
                            <textarea name="reason" id="reason" rows="4" class="form-control" placeholder="يرجى توضيح سبب البلاغ بالتفصيل..." required></textarea>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-1"></i>
                            سيتم مراجعة البلاغ من قبل فريق الإدارة، وسيتم اتخاذ الإجراء المناسب.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">إرسال البلاغ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    @endauth

    <!-- تذييل الصفحة -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>موقع الإعلانات</h5>
                    <p>المنصة الأفضل للإعلانات المبوبة في المملكة العربية السعودية</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>&copy; {{ date('Y') }} جميع الحقوق محفوظة</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- سكريبت Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- سكريبت Swiper -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

    <!-- سكريبت تعديل التعليقات وتهيئة Swiper -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة سلايدر الصور
            if (document.querySelector('.adImagesSwiper')) {
                const swiper = new Swiper('.adImagesSwiper', {
                    // تكوين السلايدر
                    loop: true,
                    autoplay: {
                        delay: 5000,
                        disableOnInteraction: false,
                    },
                    pagination: {
                        el: '.swiper-pagination',
                        clickable: true,
                    },
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev',
                    },
                });
            }

            // أزرار تعديل التعليقات
            const editButtons = document.querySelectorAll('.edit-comment-btn');
            const cancelButtons = document.querySelectorAll('.cancel-edit-btn');

            // تفعيل زر التعديل
            editButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const commentId = this.getAttribute('data-comment-id');
                    const commentContent = document.getElementById('comment-content-' + commentId);
                    const commentEditForm = document.getElementById('comment-edit-form-' + commentId);

                    // إخفاء محتوى التعليق وإظهار نموذج التعديل
                    commentContent.style.display = 'none';
                    commentEditForm.style.display = 'block';
                });
            });

            // تفعيل زر إلغاء التعديل
            cancelButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const commentId = this.getAttribute('data-comment-id');
                    const commentContent = document.getElementById('comment-content-' + commentId);
                    const commentEditForm = document.getElementById('comment-edit-form-' + commentId);

                    // إظهار محتوى التعليق وإخفاء نموذج التعديل
                    commentContent.style.display = 'block';
                    commentEditForm.style.display = 'none';
                });
            });
        });
    </script>
</body>
</html>
