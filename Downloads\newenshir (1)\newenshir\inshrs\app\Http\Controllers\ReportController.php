<?php

namespace App\Http\Controllers;

use App\Models\Ad;
use App\Models\AdReport;
use App\Models\JobPosting;
use App\Models\JobReport;
use App\Models\JobSeeker;
use App\Models\JobSeekerReport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ReportController extends Controller
{
    /**
     * إنشاء بلاغ عن إعلان
     */
    public function reportAd(Request $request, $id)
    {
        $request->validate([
            'report_type' => 'required|string|max:255',
            'reason' => 'required|string|max:1000',
        ]);

        // التحقق من وجود الإعلان
        $ad = Ad::findOrFail($id);

        // إنشاء البلاغ
        AdReport::create([
            'ad_id' => $id,
            'user_id' => Auth::id(),
            'report_type' => $request->report_type,
            'reason' => $request->reason,
        ]);

        return back()->with('success', 'تم إرسال البلاغ بنجاح، سيتم مراجعته من قبل الإدارة.');
    }

    /**
     * إنشاء بلاغ عن وظيفة
     */
    public function reportJob(Request $request, $id)
    {
        $request->validate([
            'report_type' => 'required|string|max:255',
            'reason' => 'required|string|max:1000',
        ]);

        // التحقق من وجود الوظيفة
        $job = JobPosting::findOrFail($id);

        // إنشاء البلاغ
        JobReport::create([
            'job_id' => $id,
            'user_id' => Auth::id(),
            'report_type' => $request->report_type,
            'reason' => $request->reason,
        ]);

        return back()->with('success', 'تم إرسال البلاغ بنجاح، سيتم مراجعته من قبل الإدارة.');
    }

    /**
     * إنشاء بلاغ عن باحث عن عمل
     */
    public function reportJobSeeker(Request $request, $id)
    {
        $request->validate([
            'report_type' => 'required|string|max:255',
            'reason' => 'required|string|max:1000',
        ]);

        // التحقق من وجود الباحث عن عمل
        $jobSeeker = JobSeeker::findOrFail($id);

        // إنشاء البلاغ
        JobSeekerReport::create([
            'job_seeker_id' => $id,
            'user_id' => Auth::id(),
            'report_type' => $request->report_type,
            'reason' => $request->reason,
        ]);

        return back()->with('success', 'تم إرسال البلاغ بنجاح، سيتم مراجعته من قبل الإدارة.');
    }
}
