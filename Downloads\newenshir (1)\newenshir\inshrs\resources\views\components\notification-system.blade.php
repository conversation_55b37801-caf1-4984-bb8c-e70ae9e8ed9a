@props([
    'type' => 'info', // success, error, warning, info
    'title' => null,
    'message' => '',
    'dismissible' => true,
    'autoHide' => false,
    'duration' => 5000,
    'position' => 'top-right', // top-right, top-left, bottom-right, bottom-left, center
    'icon' => null,
    'actions' => [],
    'id' => null
])

@php
    $notificationId = $id ?? 'notification-' . uniqid();
    $icons = [
        'success' => 'fas fa-check-circle',
        'error' => 'fas fa-times-circle',
        'warning' => 'fas fa-exclamation-triangle',
        'info' => 'fas fa-info-circle'
    ];
    $colors = [
        'success' => ['bg' => '#d4edda', 'border' => '#28a745', 'text' => '#155724'],
        'error' => ['bg' => '#f8d7da', 'border' => '#dc3545', 'text' => '#721c24'],
        'warning' => ['bg' => '#fff3cd', 'border' => '#ffc107', 'text' => '#856404'],
        'info' => ['bg' => '#d1ecf1', 'border' => '#17a2b8', 'text' => '#0c5460']
    ];
    $currentIcon = $icon ?? $icons[$type] ?? $icons['info'];
    $currentColors = $colors[$type] ?? $colors['info'];
@endphp

<div id="{{ $notificationId }}" 
     class="notification-alert notification-{{ $type }} notification-{{ $position }}"
     style="display: none;"
     data-auto-hide="{{ $autoHide ? 'true' : 'false' }}"
     data-duration="{{ $duration }}">
    
    <style>
        .notification-alert {
            position: fixed;
            z-index: 9999;
            min-width: 300px;
            max-width: 500px;
            padding: 1rem 1.25rem;
            border-radius: 0.75rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            border: 2px solid;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
        }
        
        .notification-alert.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* المواضع */
        .notification-top-right {
            top: 20px;
            left: 20px;
        }
        
        .notification-top-left {
            top: 20px;
            right: 20px;
        }
        
        .notification-bottom-right {
            bottom: 20px;
            left: 20px;
        }
        
        .notification-bottom-left {
            bottom: 20px;
            right: 20px;
        }
        
        .notification-center {
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        .notification-center.show {
            transform: translate(-50%, -50%);
        }
        
        /* الألوان */
        .notification-success {
            background: linear-gradient(135deg, {{ $currentColors['bg'] }} 0%, rgba(40, 167, 69, 0.1) 100%);
            border-color: {{ $currentColors['border'] }};
            color: {{ $currentColors['text'] }};
        }
        
        .notification-error {
            background: linear-gradient(135deg, {{ $currentColors['bg'] }} 0%, rgba(220, 53, 69, 0.1) 100%);
            border-color: {{ $currentColors['border'] }};
            color: {{ $currentColors['text'] }};
        }
        
        .notification-warning {
            background: linear-gradient(135deg, {{ $currentColors['bg'] }} 0%, rgba(255, 193, 7, 0.1) 100%);
            border-color: {{ $currentColors['border'] }};
            color: {{ $currentColors['text'] }};
        }
        
        .notification-info {
            background: linear-gradient(135deg, {{ $currentColors['bg'] }} 0%, rgba(23, 162, 184, 0.1) 100%);
            border-color: {{ $currentColors['border'] }};
            color: {{ $currentColors['text'] }};
        }
        
        .notification-header {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .notification-icon {
            font-size: 1.25rem;
            margin-left: 0.75rem;
            flex-shrink: 0;
        }
        
        .notification-title {
            font-weight: 600;
            font-size: 1rem;
            margin: 0;
            flex: 1;
        }
        
        .notification-close {
            background: none;
            border: none;
            font-size: 1.25rem;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.3s ease;
            color: inherit;
            padding: 0;
            margin-right: 0.5rem;
        }
        
        .notification-close:hover {
            opacity: 1;
        }
        
        .notification-message {
            font-size: 0.9rem;
            line-height: 1.5;
            margin: 0;
        }
        
        .notification-actions {
            margin-top: 1rem;
            display: flex;
            gap: 0.5rem;
            justify-content: flex-start;
        }
        
        .notification-action {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .notification-action.primary {
            background: {{ $currentColors['border'] }};
            color: white;
        }
        
        .notification-action.primary:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        
        .notification-action.secondary {
            background: transparent;
            color: {{ $currentColors['text'] }};
            border: 1px solid {{ $currentColors['border'] }};
        }
        
        .notification-action.secondary:hover {
            background: {{ $currentColors['border'] }};
            color: white;
        }
        
        .notification-progress {
            position: absolute;
            bottom: 0;
            right: 0;
            height: 3px;
            background: {{ $currentColors['border'] }};
            border-radius: 0 0 0.75rem 0.75rem;
            transition: width linear;
        }
        
        @media (max-width: 768px) {
            .notification-alert {
                min-width: 280px;
                max-width: calc(100vw - 40px);
                margin: 0 20px;
            }
            
            .notification-top-right,
            .notification-top-left {
                top: 10px;
                left: 20px;
                right: 20px;
            }
            
            .notification-bottom-right,
            .notification-bottom-left {
                bottom: 10px;
                left: 20px;
                right: 20px;
            }
        }
    </style>
    
    <div class="notification-content">
        @if($title || $dismissible)
            <div class="notification-header">
                <i class="notification-icon {{ $currentIcon }}"></i>
                @if($title)
                    <h6 class="notification-title">{{ $title }}</h6>
                @endif
                @if($dismissible)
                    <button type="button" class="notification-close" onclick="hideNotification('{{ $notificationId }}')">
                        <i class="fas fa-times"></i>
                    </button>
                @endif
            </div>
        @else
            <div style="display: flex; align-items: flex-start;">
                <i class="notification-icon {{ $currentIcon }}"></i>
                <div style="flex: 1;">
        @endif
        
        @if($message)
            <p class="notification-message">{!! $message !!}</p>
        @endif
        
        {{ $slot }}
        
        @if(!$title && !$dismissible)
                </div>
            </div>
        @endif
        
        @if(count($actions) > 0)
            <div class="notification-actions">
                @foreach($actions as $action)
                    <button type="button" 
                            class="notification-action {{ $action['type'] ?? 'secondary' }}"
                            onclick="{{ $action['onclick'] ?? '' }}">
                        @if(isset($action['icon']))
                            <i class="{{ $action['icon'] }} me-1"></i>
                        @endif
                        {{ $action['text'] }}
                    </button>
                @endforeach
            </div>
        @endif
    </div>
    
    @if($autoHide)
        <div class="notification-progress" id="{{ $notificationId }}-progress"></div>
    @endif
</div>

<script>
// نظام إدارة التنبيهات
window.NotificationSystem = window.NotificationSystem || {
    notifications: new Map(),
    
    show: function(id, options = {}) {
        const notification = document.getElementById(id);
        if (!notification) return;
        
        // إظهار التنبيه
        notification.style.display = 'block';
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // تسجيل التنبيه
        this.notifications.set(id, {
            element: notification,
            options: options,
            startTime: Date.now()
        });
        
        // الإخفاء التلقائي
        if (notification.dataset.autoHide === 'true') {
            const duration = parseInt(notification.dataset.duration) || 5000;
            this.startAutoHide(id, duration);
        }
        
        // إضافة مستمع للنقر خارج التنبيه (للتنبيهات المركزية)
        if (notification.classList.contains('notification-center')) {
            this.addOutsideClickListener(id);
        }
    },
    
    hide: function(id) {
        const notification = document.getElementById(id);
        if (!notification) return;
        
        notification.classList.remove('show');
        setTimeout(() => {
            notification.style.display = 'none';
            this.notifications.delete(id);
        }, 400);
        
        // إزالة مستمع النقر خارج التنبيه
        this.removeOutsideClickListener(id);
    },
    
    startAutoHide: function(id, duration) {
        const notification = this.notifications.get(id);
        if (!notification) return;
        
        const progressBar = document.getElementById(id + '-progress');
        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.style.transition = `width ${duration}ms linear`;
            setTimeout(() => {
                progressBar.style.width = '0%';
            }, 10);
        }
        
        setTimeout(() => {
            this.hide(id);
        }, duration);
    },
    
    addOutsideClickListener: function(id) {
        const notification = document.getElementById(id);
        if (!notification) return;
        
        const listener = (event) => {
            if (!notification.contains(event.target)) {
                this.hide(id);
            }
        };
        
        setTimeout(() => {
            document.addEventListener('click', listener);
            notification.dataset.outsideListener = 'true';
        }, 100);
    },
    
    removeOutsideClickListener: function(id) {
        // سيتم إزالة المستمع تلقائياً عند حذف التنبيه
    }
};

// دالة عامة لإظهار التنبيه
function showNotification(id, options = {}) {
    window.NotificationSystem.show(id, options);
}

// دالة عامة لإخفاء التنبيه
function hideNotification(id) {
    window.NotificationSystem.hide(id);
}

// إظهار التنبيه تلقائياً إذا كان مرئياً في الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const notification = document.getElementById('{{ $notificationId }}');
    if (notification && notification.style.display !== 'none') {
        showNotification('{{ $notificationId }}');
    }
});
</script>
