<?php
/**
 * سكريبت لنقل الصور من storage/app/public إلى public/images
 * وتحديث قاعدة البيانات بالمسارات الجديدة
 */

// تحديد المسارات
$sourceDir = __DIR__ . '/storage/app/public';
$destDir = __DIR__ . '/public/images';

// التأكد من وجود المجلدات
if (!is_dir($sourceDir)) {
    die("المجلد المصدر غير موجود: $sourceDir\n");
}

if (!is_dir($destDir)) {
    if (!mkdir($destDir, 0755, true)) {
        die("فشل في إنشاء المجلد الهدف: $destDir\n");
    }
    echo "تم إنشاء المجلد الهدف: $destDir\n";
}

// التأكد من وجود مجلد ads في المجلد الهدف
$adsDestDir = $destDir . '/ads';
if (!is_dir($adsDestDir)) {
    if (!mkdir($adsDestDir, 0755, true)) {
        die("فشل في إنشاء مجلد الإعلانات في المجلد الهدف: $adsDestDir\n");
    }
    echo "تم إنشاء مجلد الإعلانات في المجلد الهدف: $adsDestDir\n";
}

// تضمين ملفات Laravel الأساسية للوصول إلى قاعدة البيانات
require __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

// الحصول على اتصال قاعدة البيانات
$db = $app->make('db');

// الحصول على جميع الإعلانات التي لها صور في المجلد القديم
$ads = $db->table('ads')
    ->whereNotNull('image')
    ->where('image', 'not like', 'images/ads/%')
    ->get();

echo "تم العثور على " . count($ads) . " إعلان بصور في المجلد القديم.\n";

// عداد للإحصائيات
$successCount = 0;
$errorCount = 0;
$skippedCount = 0;

// نقل كل صورة وتحديث قاعدة البيانات
foreach ($ads as $ad) {
    $oldRelativePath = $ad->image; // مثال: ads/12345_image.jpg
    $fileName = basename($oldRelativePath);
    $oldFullPath = $sourceDir . '/' . $oldRelativePath;
    $newFullPath = $adsDestDir . '/' . $fileName;
    $newRelativePath = 'images/ads/' . $fileName;
    
    echo "معالجة الإعلان ID: {$ad->id}, الصورة: {$oldRelativePath}\n";
    
    // التحقق من وجود الملف المصدر
    if (!file_exists($oldFullPath)) {
        echo "  خطأ: الملف المصدر غير موجود: {$oldFullPath}\n";
        $errorCount++;
        continue;
    }
    
    // التحقق مما إذا كان الملف الهدف موجودًا بالفعل
    if (file_exists($newFullPath)) {
        echo "  تخطي: الملف الهدف موجود بالفعل: {$newFullPath}\n";
        
        // تحديث قاعدة البيانات فقط
        $db->table('ads')
            ->where('id', $ad->id)
            ->update(['image' => $newRelativePath]);
            
        echo "  تم تحديث قاعدة البيانات للإعلان ID: {$ad->id}\n";
        $skippedCount++;
        continue;
    }
    
    // نسخ الملف
    if (copy($oldFullPath, $newFullPath)) {
        echo "  تم نسخ الملف بنجاح إلى: {$newFullPath}\n";
        
        // تحديث قاعدة البيانات
        $db->table('ads')
            ->where('id', $ad->id)
            ->update(['image' => $newRelativePath]);
            
        echo "  تم تحديث قاعدة البيانات للإعلان ID: {$ad->id}\n";
        $successCount++;
    } else {
        echo "  خطأ: فشل في نسخ الملف إلى: {$newFullPath}\n";
        $errorCount++;
    }
}

// عرض الإحصائيات النهائية
echo "\nتم الانتهاء من نقل الصور.\n";
echo "الإحصائيات:\n";
echo "  تم نقل وتحديث: {$successCount}\n";
echo "  تم تخطي (موجود بالفعل): {$skippedCount}\n";
echo "  فشل: {$errorCount}\n";
echo "المجموع: " . ($successCount + $skippedCount + $errorCount) . "\n";

// إنشاء ملف index.html في مجلد الصور لمنع استعراض المحتويات
$indexContent = '<html><head><title>403 Forbidden</title></head><body><h1>Directory access is forbidden.</h1></body></html>';
$indexFile = $adsDestDir . '/index.html';

if (!file_exists($indexFile)) {
    if (file_put_contents($indexFile, $indexContent)) {
        echo "\nتم إنشاء ملف index.html لحماية مجلد الصور.\n";
    } else {
        echo "\nفشل في إنشاء ملف index.html لحماية مجلد الصور.\n";
    }
} else {
    echo "\nملف index.html موجود بالفعل في مجلد الصور.\n";
}

echo "\nتم الانتهاء من عملية النقل بنجاح.\n";
