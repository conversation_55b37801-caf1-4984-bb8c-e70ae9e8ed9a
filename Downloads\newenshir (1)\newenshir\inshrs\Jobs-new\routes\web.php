<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\JobController;

use App\Http\Controllers\JobSeekerController;
use App\Http\Controllers\BasicInfoController;
use App\Http\Controllers\PostController;
use App\Http\Controllers\SupportController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/






use App\Http\Controllers\PointController;
use App\Http\Controllers\MoyasarController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\Admin\BankTransferController;

Route::middleware(['auth'])->group(function () {
    // مسارات النقاط والدفع
    Route::get('/points/buy', [PointController::class, 'buy'])->name('points.buy');
    Route::post('/points/buy', [PointController::class, 'processBuy'])->name('points.buy.process');
    Route::get('/points/paypal/success', [PointController::class, 'paypalSuccess'])->name('points.paypal.success');
    Route::get('/points/paypal/cancel', [PointController::class, 'paypalCancel'])->name('points.paypal.cancel');
    Route::post('/points/paypal/process', [PointController::class, 'processPayPalPayment'])->name('points.paypal.process');
    Route::get('/points/transactions', [PointController::class, 'transactions'])->name('points.transactions');

    // مسارات Moyasar للدفع عبر STC Pay
    Route::post('/points/stcpay', [MoyasarController::class, 'createPayment'])->name('moyasar.create');
    Route::get('/points/stcpay/callback', [MoyasarController::class, 'callback'])->name('moyasar.callback');

    // مسارات الإشعارات
    Route::get('/notifications', [NotificationController::class, 'index'])->name('notifications.index');
    Route::post('/notifications/{id}/mark-as-read', [NotificationController::class, 'markAsRead'])->name('notifications.mark-as-read');
    Route::post('/notifications/mark-all-as-read', [NotificationController::class, 'markAllAsRead'])->name('notifications.mark-all-as-read');
    Route::delete('/notifications/{id}', [NotificationController::class, 'destroy'])->name('notifications.destroy');
});

// مسارات الدعم للمستخدمين العاديين
Route::middleware(['auth'])->group(function () {
    Route::get('/support', [SupportController::class, 'create'])->name('support.create');
    Route::post('/support', [SupportController::class, 'store'])->name('support.store');
});

// مسارات لوحة تحكم المسؤول
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\PermissionController;
use App\Http\Controllers\Admin\UserRoleController;

Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // مسارات لوحة التحكم الرئيسية
    Route::get('/', [AdminController::class, 'dashboard'])->name('dashboard');
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard'); // إضافة مسار إضافي للوحة التحكم
    Route::get('/users', [AdminController::class, 'users'])->name('users');
    Route::get('/ads', [AdminController::class, 'ads'])->name('ads');
    Route::get('/jobs', [AdminController::class, 'jobs'])->name('jobs');
    Route::get('/job-seekers', [AdminController::class, 'jobSeekers'])->name('job-seekers');

    // مسارات إدارة المستخدمين
    Route::post('/users/{id}/make-admin', [AdminController::class, 'makeAdmin'])->name('users.make-admin');
    Route::post('/users/{id}/remove-admin', [AdminController::class, 'removeAdmin'])->name('users.remove-admin');
    Route::delete('/users/{id}', [AdminController::class, 'deleteUser'])->name('users.delete');

    // مسارات إدارة المحتوى
    Route::delete('/ads/{id}', [AdminController::class, 'deleteAd'])->name('ads.delete');
    Route::delete('/jobs/{id}', [AdminController::class, 'deleteJob'])->name('jobs.delete');

    // مسارات الإشعارات
    Route::post('/notifications/send', [AdminController::class, 'sendNotification'])->name('notifications.send');

    // مسارات إدارة طلبات التحويل البنكي
    Route::get('/bank-transfers', [BankTransferController::class, 'index'])->name('bank-transfers.index');
    Route::get('/bank-transfers/{id}', [BankTransferController::class, 'show'])->name('bank-transfers.show');
    Route::post('/bank-transfers/{id}/approve', [BankTransferController::class, 'approve'])->name('bank-transfers.approve');
    Route::post('/bank-transfers/{id}/reject', [BankTransferController::class, 'reject'])->name('bank-transfers.reject');

    // مسارات إدارة الأدوار والصلاحيات
    Route::resource('roles', RoleController::class);
    Route::resource('permissions', PermissionController::class);

    // مسارات إدارة أدوار المستخدمين
    Route::get('/user-roles', [UserRoleController::class, 'index'])->name('user-roles.index');
    Route::get('/user-roles/create', [UserRoleController::class, 'create'])->name('user-roles.create');
    Route::post('/user-roles', [UserRoleController::class, 'store'])->name('user-roles.store');
    Route::get('/user-roles/{id}/edit', [UserRoleController::class, 'edit'])->name('user-roles.edit');
    Route::put('/user-roles/{id}', [UserRoleController::class, 'update'])->name('user-roles.update');
    Route::delete('/user-roles/{id}', [UserRoleController::class, 'destroy'])->name('user-roles.destroy');
    // مسارات إدارة نقاط المستخدمين
    Route::get('/points/add', [PointController::class, 'create'])->name('points.add');
    Route::post('/points/add', [PointController::class, 'addPoints'])->name('points.store');

    // مسارات إدارة الدعم
    Route::get('/supp_manag', function () {
        return view('admin/Officials/supp_manag');
    })->name('support.manage');
});




use App\Http\Controllers\AdController;

Route::resource('ads', AdController::class);
Route::post('/ads/nearby', [AdController::class, 'nearby'])->name('ads.nearby');





Route::post('/jobs/{id}/report', [JobSeekerController::class, 'report'])->name('jobs.report');



// routes/web.php
Route::get('/jobs/{id}/edit', [JobController::class, 'edit'])->name('jobs.edit');
Route::post('/jobs/{id}/update', [JobController::class, 'update'])->name('jobs.update');
Route::delete('jobs/{id}', [JobController::class, 'destroy'])->name('jobs.destroy');


Route::get('/job-seekers/{id}/edit', [JobSeekerController::class, 'edit'])->name('jobSeeker.edit');
Route::put('/job-seekers/{id}/update', [JobSeekerController::class, 'update'])->name('jobSeeker.update');
Route::delete('my-job-seekers/{id}', [JobSeekerController::class, 'destroy'])->name('jobSeeker.destroy');


use App\Http\Controllers\DashboardController;


Route::get('/my-job-seekers', [DashboardController::class, 'myJobSeekers'])->name('myJobSeekers');


Route::get('/my-jobs', [JobController::class, 'myJobs'])->name('jobs.myJobs')->middleware('auth');



// Route::get('/users/profile/UserProfileShow', [BasicInfoController::class, 'show'])->name('UserProfileShow.show');
Route::get('/users/profile/basic_info', [BasicInfoController::class, 'edit'])->name('basic_info.edit');
Route::put('/users/profile/basic_info', [BasicInfoController::class, 'update'])->name('basic_info.update');

Route::get('jobs', [JobController::class, 'index'])->name('jobs.index');

Route::get('/jobs/show_job_company/{id}', [JobController::class, 'show'])->name('jobs.show');

 Route::get('/show-job-user/{id}',[JobSeekerController::class,'show'])->name('jobSeekers.show');

Route::middleware(['auth'])->group(function () {
    Route::get('/jobs/post_job_company', [JobController::class, 'create'])->name('jobs.create');
    Route::post('/jobs/store', [JobController::class, 'store'])->name('jobs.store');
});


 Route::get('/show-job-company', function () {
     return view('/jobs/show_job_company');
  })->name('show-job-company');


Route::get('/Officials', function () {
    return view('admin/Officials/Officials_Dash');
})->name('Officials');


Route::get('/admin', function () {
    return redirect('/admin/dashboard');
})->name('admin');

// Custom login route is commented out to use Laravel's built-in auth routes
// Route::get('/login', function () {
//     return view('users/auth/login/login-singup');
// })->name('login');


Route::get('/dash', function () {
    return view('users/profile/dash');
})->name('dash');


Route::get('/companydash', function () {
    return view('company/companydash');
})->name('companydash');




Route::get('/form-page', function () {
    return view('specialsend/form_page');
})->name('form-page');



Route::get('/show-job-user', function () {
    return view('jobs/show_job_user');
})->name('show-job-user');



Route::get('/edit_job_user', function () {
    return view('Jobs/edit_job_user');
})->name('edit_job_user');



Route::get('/show-job-application', function () {
    return view('jobs/show_job_application');
})->name('show-job-application');



Route::get('/main', function () {
    return view('main/home');
})->name('main.home');



Route::get('/UserjobsManagement', function () {
    return view('jobs/UserjobsManagement');
})->name('UserjobsManagement');



Route::get('/price', function () {
    return view('pricing/price');
})->name('price');



Route::get('/post-job-user', function () {
    return view('jobs/post_job_user');
})->name('post-job-user');


// Route::get('/post-job-company', function () {
//     return view('jobs/post_job_company');
// })->name('post-job-company');




Route::get('/Apply-job-company', function () {
    return view('jobs/Apply_job_company');
})->name('Apply-job-company');




Route::get('/jobsSeekers', function () {
    return view('jobs/jobsSeekers');
})->name('jobsSeekers');




Route::get('/UserProfileShow', function () {
    return view('users/profile/UserProfileShow');
})->name('/UserProfileShow');




Route::get('/UserProfileupdate', function () {
    return view('users/profile/UserProfileUpdate');
})->name('/UserProfile');


Route::get('/userNotificationAllShow', function () {
    return view('Notification/userNotificationAllShow');
})->name('userNotificationAllShow');



Route::get('/settings-User', function () {
    return view('jobs/setting_User');
})->name('settings-User');


Route::get('/cv-show', function () {
    return view('cv/show');
});


Route::get('/pricingC', function () {
    return view('pricing/priceCompany');
});




// Route::get('/User-Profile-Show', function () {
//     return view('users/profile/User_Profile_Show');
// });



Route::get('/User_Profile_updateblade', function () {
    return view('profile/User_Profile_updateblade');
});



Route::get('/jobsSearch', function () {
    return view('jobs/jobsSearch');
});



Route::get('/NotificationAllShow', function () {
    return view('Notification/NotificationAllShow');
});


Route::get('/CompanyProfileShow', function () {
    return view('company/CompanyProfileShow');
});

Route::get('/company/basic_info', function () {
    return view('company/basic_info');
});

Route::get('/company/CompanyProfileShow', function () {
    return view('company/CompanyProfileShow');
})->name('CompanyProfileShow');

Route::get('/company/CompanyProfileUpdate', function () {
    return view('company/CompanyProfileUpdate');
})->name('CompanyProfileUpdate');



Route::get('/UserProfileUpdate', function () {
    return view('profile/UserProfileUpdate');
});



Route::get('/Permissions ', function () {
    return view('admin/Permissions ');
});


Route::get('/Officials-Dash', function () {
    return view('admin/Officials/Officials_Dash');
});




Route::get('/ads-admin', function () {
    return view('ads/ads_admin');
});




Route::get('/ads-user', function () {
    return view('ads/ads_user');
});




Route::get('/jobsManagement', function () {
    return view('jobs/jobsManagement');
});

Route::get('/jobs-seekers', function () {
    return view('jobs/jobsSeekers');
})->name('jobs-seekers');



Route::get('/pricingU', function () {
    return view('pricing/price');
});


Route::get('/', function () {
    return view('main/home');
}) ;







Route::get('/specialsend', [PostController::class, 'send'])->name('specialsend.send');

Route::get('profile/dash', [PostController::class, 'dash'])->name('profile.dash');



  Route::get('/edit-job', function () {
    return view('jobs/edit_job');
});

Route::get('/job-applications', function () {
    return view('jobs/job_applications');
});

Route::get('/basic-info', function () {
    return view('users/profile/basic_info');
})->name('basic-info');

Route::get('/company-basic-info', function () {
    return view('company/basic_info');
})->middleware('auth');

Route::get('/admin/reports', function () {
    return view('admin/reports');
})->name('admin.reports');
Route::get('/CompanyProfileShow', function () {
    return view('company/CompanyProfileShow');
})->name('/CompanyProfileShow');

Route::get('/sections', function () {
    return view('main/Sections');
})->name('sections');











Route::get('/jobs/create' ,function(){
return  "dfdfdsf";
});








// Route::get('/index', function () {
//     return view('index');
// });



Route::get('/users/profile/UserProfileShow', [ProfileController::class, 'showProfile'])->middleware('auth');


Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});



Route::get('/jobSeekers', [JobSeekerController::class, 'index'])->name('job_seekers.index');



Route::get('/job-seekers', [JobSeekerController::class, 'create'])->name('job_seekers.create');
Route::post('/job-seekers/store', [JobSeekerController::class, 'store'])->name('job_seekers.store');
Route::get('/job-seekers/{id}', [JobSeekerController::class, 'show'])->name('job-seekers.show');


require __DIR__.'/auth.php';



// Temporary route to test SupportController
Route::get('/test-support-controller', function () {
    dd(App\Http\Controllers\SupportController::class);
});
