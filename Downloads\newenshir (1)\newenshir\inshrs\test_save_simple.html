<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>اختبار زر الحفظ البسيط</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .save-button-container {
            display: inline-block;
            position: relative;
            margin: 20px;
        }
        
        .save-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            box-shadow: 0 2px 6px rgba(108, 117, 125, 0.3);
        }
        
        .save-button.saved {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            box-shadow: 0 2px 6px rgba(40, 167, 69, 0.3);
        }
        
        .save-button.loading {
            pointer-events: none;
            opacity: 0.8;
        }
        
        .save-icon {
            font-size: 1rem;
            margin-left: 0.5rem;
        }
        
        .save-text {
            font-size: 1rem;
            font-weight: 600;
        }
        
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            margin-top: 20px;
        }
        
        .test-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .clear-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار زر الحفظ البسيط</h1>
        
        <div class="test-info">
            <h3>📋 معلومات الاختبار:</h3>
            <p><strong>الهدف:</strong> تشخيص مشكلة زر الحفظ</p>
            <p><strong>التعليمات:</strong> اضغط على زر الحفظ وراقب الرسائل أدناه</p>
        </div>
        
        <!-- زر الحفظ التجريبي -->
        <div class="save-button-container" data-item-type="ad" data-item-id="1">
            <button type="button" class="save-button" title="حفظ العنصر">
                <i class="save-icon fas fa-bookmark"></i>
                <span class="save-text">حفظ</span>
            </button>
        </div>
        
        <!-- سجل الأحداث -->
        <button class="clear-btn" onclick="clearLog()">مسح السجل</button>
        <div id="log" class="log-area">جاري تحميل النظام...\n</div>
    </div>

    <script>
        // دالة تسجيل الأحداث
        function log(message, type = 'info') {
            const logArea = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;
            
            console.log(`[Test] ${logEntry.trim()}`);
        }
        
        // مسح السجل
        function clearLog() {
            document.getElementById('log').textContent = '';
            log('تم مسح السجل', 'info');
        }
        
        // اختبار أولي
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 تم تحميل الصفحة', 'success');
            
            // فحص CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (csrfToken && csrfToken.getAttribute('content')) {
                log('✅ CSRF Token موجود: ' + csrfToken.getAttribute('content').substring(0, 10) + '...', 'success');
            } else {
                log('❌ CSRF Token غير موجود', 'error');
            }
            
            // فحص زر الحفظ
            const saveButton = document.querySelector('.save-button-container');
            if (saveButton) {
                log('✅ زر الحفظ موجود', 'success');
                log('📋 بيانات الزر: ' + JSON.stringify({
                    itemType: saveButton.dataset.itemType,
                    itemId: saveButton.dataset.itemId
                }), 'info');
            } else {
                log('❌ زر الحفظ غير موجود', 'error');
            }
            
            // إضافة مستمع الأحداث
            document.addEventListener('click', function(e) {
                if (e.target.closest('.save-button')) {
                    e.preventDefault();
                    log('🖱️ تم الضغط على زر الحفظ', 'info');
                    
                    const container = e.target.closest('.save-button-container');
                    if (container) {
                        testSaveFunction(container);
                    } else {
                        log('❌ لم يتم العثور على container', 'error');
                    }
                }
            });
            
            log('✅ تم تفعيل مستمع الأحداث', 'success');
        });
        
        // دالة اختبار الحفظ
        function testSaveFunction(container) {
            log('🔄 بدء اختبار عملية الحفظ', 'info');
            
            const itemType = container.dataset.itemType;
            const itemId = container.dataset.itemId;
            const button = container.querySelector('.save-button');
            const icon = button.querySelector('.save-icon');
            const text = button.querySelector('.save-text');
            
            log('📋 بيانات العملية: ' + JSON.stringify({ itemType, itemId }), 'info');
            
            // التحقق من العناصر
            if (!button) {
                log('❌ زر الحفظ غير موجود', 'error');
                return;
            }
            
            if (!icon || !text) {
                log('❌ عناصر الزر غير مكتملة', 'error');
                return;
            }
            
            // التحقق من CSRF token
            const csrfTokenElement = document.querySelector('meta[name="csrf-token"]');
            if (!csrfTokenElement) {
                log('❌ CSRF token element غير موجود', 'error');
                return;
            }
            
            const csrfToken = csrfTokenElement.getAttribute('content');
            if (!csrfToken) {
                log('❌ CSRF token فارغ', 'error');
                return;
            }
            
            log('✅ جميع الفحوصات نجحت', 'success');
            
            // إضافة حالة التحميل
            button.classList.add('loading');
            icon.classList.add('fa-spinner', 'fa-spin');
            icon.classList.remove('fa-bookmark');
            text.textContent = 'جاري الحفظ...';
            
            log('🔄 تم تفعيل حالة التحميل', 'info');
            log('📡 إرسال طلب إلى /saved/toggle', 'info');
            
            // إرسال الطلب
            fetch('/saved/toggle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    item_type: itemType,
                    item_id: parseInt(itemId)
                })
            })
            .then(response => {
                log('📥 استجابة الخادم: ' + response.status + ' ' + response.statusText, 'info');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return response.json();
            })
            .then(data => {
                log('✅ بيانات الاستجابة: ' + JSON.stringify(data), 'success');
                
                // إزالة حالة التحميل
                button.classList.remove('loading');
                icon.classList.remove('fa-spinner', 'fa-spin');
                icon.classList.add('fa-bookmark');
                
                if (data.success) {
                    if (data.saved) {
                        button.classList.add('saved');
                        text.textContent = 'محفوظ';
                        icon.classList.remove('far');
                        icon.classList.add('fas');
                        log('✅ تم حفظ العنصر بنجاح', 'success');
                    } else {
                        button.classList.remove('saved');
                        text.textContent = 'حفظ';
                        icon.classList.remove('fas');
                        icon.classList.add('far');
                        log('🗑️ تم إلغاء حفظ العنصر', 'success');
                    }
                } else {
                    throw new Error(data.message || 'فشل في العملية');
                }
            })
            .catch(error => {
                log('❌ خطأ في عملية الحفظ: ' + error.message, 'error');
                
                // إزالة حالة التحميل
                button.classList.remove('loading');
                icon.classList.remove('fa-spinner', 'fa-spin');
                icon.classList.add('fa-bookmark');
                text.textContent = 'حفظ';
                
                // تحليل نوع الخطأ
                if (error.message.includes('404')) {
                    log('💡 المشكلة: الرابط /saved/toggle غير موجود', 'warning');
                    log('🔧 الحل: تحقق من routes/web.php', 'warning');
                } else if (error.message.includes('419')) {
                    log('💡 المشكلة: CSRF Token غير صحيح', 'warning');
                    log('🔧 الحل: أعد تحميل الصفحة', 'warning');
                } else if (error.message.includes('500')) {
                    log('💡 المشكلة: خطأ في الخادم', 'warning');
                    log('🔧 الحل: تحقق من logs', 'warning');
                } else if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
                    log('💡 المشكلة: مشكلة في الاتصال', 'warning');
                    log('🔧 الحل: تحقق من الخادم', 'warning');
                }
            });
        }
    </script>
</body>
</html>
