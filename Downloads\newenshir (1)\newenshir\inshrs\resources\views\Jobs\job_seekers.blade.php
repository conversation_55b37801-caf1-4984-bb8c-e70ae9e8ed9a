<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الباحثين عن عمل</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700&display=swap');
        body {
            font-family: 'Almarai', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #e9ecef 100%);
        }
        .card-hover {
            transition: all 0.3s ease-in-out;
        }
        .card-hover:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-gray-50 flex flex-col min-h-screen">
    <!-- Header (كما في النسخة السابقة) -->
    <header class="bg-white shadow-md">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="/logo.png" alt="Logo" class="h-10 w-10">
                <h1 class="text-2xl font-bold text-blue-700">منصة التوظيف</h1>
            </div>
            <nav>
                <ul class="flex space-x-6">
                    <li><a href="#" class="text-gray-700 hover:text-blue-600 transition-colors">الرئيسية</a></li>
                    <li><a href="#" class="text-gray-700 hover:text-blue-600 transition-colors">الوظائف</a></li>
                    <li><a href="#" class="text-gray-700 hover:text-blue-600 transition-colors">الباحثين</a></li>
                    <li><a href="#" class="text-gray-700 hover:text-blue-600 transition-colors">اتصل بنا</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="flex-grow container mx-auto px-4 py-8">
        <!-- البحث والفلترة (كما في النسخة السابقة) -->
        <div class="mb-12 bg-white rounded-2xl shadow-lg p-6">
            <form class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label class="block text-gray-700 mb-2">التخصص</label>
                    <select class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300">
                        <option>كل التخصصات</option>
                        <option>برمجة</option>
                        <option>تصميم</option>
                        <option>إدارة</option>
                    </select>
                </div>
                <div>
                    <label class="block text-gray-700 mb-2">الخبرة</label>
                    <select class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300">
                        <option>كل مستويات الخبرة</option>
                        <option>0-2 سنة</option>
                        <option>2-5 سنوات</option>
                        <option>5+ سنوات</option>
                    </select>
                </div>
                <div>
                    <label class="block text-gray-700 mb-2">البحث</label>
                    <div class="flex">
                        <input type="text" placeholder="ابحث عن مهارات أو اسم" class="w-full p-3 border border-gray-300 rounded-r-lg focus:ring-2 focus:ring-blue-300">
                        <button class="bg-blue-500 text-white px-6 rounded-l-lg hover:bg-blue-600 transition-colors">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- عنوان الصفحة -->
        <h1 class="text-4xl font-bold text-center text-gray-800 mb-12 tracking-wide bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-teal-400">الباحثين عن عمل</h1>
&lt;a href="{{ route('job_seekers.create') }}" class="btn btn-signup">إضافة باحث عن عمل&lt;/a&gt;
&lt;a href="{{ route('jobs.create') }}" class="btn btn-signup">إضافة وظيفة جديدة&lt;/a&gt;

        @if($jobSeekers->isEmpty())
            <!-- حالة عدم وجود نتائج (كما في النسخة السابقة) -->
            <div class="text-center bg-white shadow-2xl rounded-2xl p-12 max-w-xl mx-auto">
                <svg class="mx-auto h-24 w-24 text-blue-300 mb-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p class="mt-4 text-2xl text-gray-500 font-bold">لا يوجد باحثين عن عمل حاليًا</p>
            </div>
        @else


            <!-- قسم البطاقات -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                @foreach($jobSeekers as $seeker)
                    <div class="bg-white rounded-2xl shadow-lg card-hover overflow-hidden border border-gray-100">
                        <div class="p-6 space-y-5">
                            <div class="flex items-center space-x-4 reverse-x">
                                <div class="flex-shrink-0">
                                    @if($seeker->user && $seeker->user->hasProfileImage())
                                        <img src="{{ $seeker->user->getProfileImageUrl() }}"
                                             alt="{{ $seeker->user->name ?? 'باحث عن عمل' }}"
                                             class="w-20 h-20 rounded-full object-cover ring-4 ring-blue-100">
                                    @else
                                        <img src="{{ $seeker->user ? $seeker->user->getDefaultAvatar() : 'https://ui-avatars.com/api/?name=باحث&background=random&color=fff&size=80&rounded=true' }}"
                                             alt="صورة افتراضية"
                                             class="w-20 h-20 rounded-full object-cover ring-4 ring-blue-100">
                                    @endif
                                </div>
                                <div>
                                    <h3 class="text-2xl font-bold text-blue-700">{{ $seeker->user->name ?? 'غير معروف' }}</h3>
                                    <p class="text-sm text-gray-500">{{ $seeker->specialization ?? 'التخصص غير محدد' }}</p>
                                </div>
                            </div>

                            <div class="space-y-3">
                                <div class="flex items-center space-x-2 reverse-x">
                                    <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                    <span class="text-gray-700">الخبرة: {{ $seeker->experience ? $seeker->experience . ' سنوات' : 'بدون خبرة' }}</span>
                                </div>

                                <div class="flex items-center space-x-2 reverse-x">
                                    <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                    </svg>
                                    <span class="text-gray-700">المهارات: {{ $seeker->skills ?? 'غير محدد' }}</span>
                                </div>

                                <div class="flex items-center space-x-2 reverse-x">
                                    <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    <span class="text-gray-700">{{ $seeker->location ?? 'الموقع غير محدد' }}</span>
                                </div>
                            </div>

                            <div class="bg-blue-50 p-4 rounded-lg border-r-4 border-blue-300">
                                <p class="text-sm text-gray-700">{{ Str::limit($seeker->description ?? 'لم يتم إضافة وصف', 100) }}</p>
                            </div>
                        </div>

                        <div class="bg-gray-100 p-4 border-t">
                        <a href="{{ route('jobs.show', ['id' => $job->id]) }}" class="btn btn-apply">تفاصيل</a>


                        <a href="#" class="block w-full text-center bg-gradient-to-r from-blue-500 to-teal-400 text-white py-3 rounded-lg hover:from-blue-600 hover:to-teal-500 transition-all duration-300 transform hover:-translate-y-1 shadow-md hover:shadow-lg">
                                عرض التفاصيل
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- ترقيم الصفحات وأزرار التنقل -->
            <div class="flex justify-center items-center space-x-4 bg-white rounded-2xl shadow-lg p-4">
                <div class="flex items-center space-x-2">
                    <!-- زر السابق -->
                    <a href="{{ $jobSeekers->previousPageUrl() }}" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 {{ $jobSeekers->onFirstPage() ? 'pointer-events-none opacity-50' : '' }}">
                        السابق
                    </a>

                    <!-- أرقام الصفحات -->
                    @for ($i = 1; $i <= $jobSeekers->lastPage(); $i++)
                        <a href="{{ $jobSeekers->url($i) }}" class="px-4 py-2 {{ $jobSeekers->currentPage() == $i ? 'bg-blue-600 text-white' : 'bg-white text-blue-500 border' }} rounded-lg hover:bg-blue-100">
                            {{ $i }}
                        </a>
                    @endfor

                    <!-- زر التالي -->
                    <a href="{{ $jobSeekers->nextPageUrl() }}" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 {{ $jobSeekers->hasMorePages() ? '' : 'pointer-events-none opacity-50' }}">
                        التالي
                    </a>
                </div>
            </div>
        @endif
    <a href="{{ route('jobs.create') }}" class="btn btn-signup">إضافة وظيفة جديدة</a>
    <a href="{{ route('job_seekers.create') }}" class="btn btn-signup">إضافة باحث عن عمل</a>
    </main>

    <!-- Footer (كما في النسخة السابقة) -->
    <footer class="bg-gray-800 text-white py-12">
        <!-- محتوى الفوتر كما في النسخة السابقة -->
        <div class="container mx-auto px-4 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
                <h3 class="text-xl font-bold mb-4">عن المنصة</h3>
                <p class="text-gray-300">منصة توظيف متكاملة تربط الباحثين عن عمل بأفضل الفرص الوظيفية</p>
            </div>
            <div>
                <h3 class="text-xl font-bold mb-4">روابط سريعة</h3>
                <ul class="space-y-2">
                    <li><a href="#" class="text-gray-300 hover:text-white">الرئيسية</a></li>
                    <li><a href="#" class="text-gray-300 hover:text-white">الوظائف</a></li>
                    <li><a href="#" class="text-gray-300 hover:text-white">الباحثين</a></li>
                    <li><a href="#" class="text-gray-300 hover:text-white">اتصل بنا</a></li>
                </ul>
            </div>
            <div>
                <h3 class="text-xl font-bold mb-4">تواصل معنا</h3>
                <div class="flex space-x-4">
                    <a href="#" class="text-blue-400 hover:text-blue-300">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                        </svg>
                    </a>
                    <a href="#" class="text-blue-400 hover:text-blue-300">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 14.002-7.496 14.002-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
                        </svg>
                    </a>
                    <a href="#" class="text-blue-400 hover:text-blue-300">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 0c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm-2 16h-2v-6h2v6zm-1-6.891c-.607 0-1.1-.496-1.1-1.109 0-.612.492-1.109 1.1-1.109s1.1.496 1.1 1.109c0 .612-.492 1.109-1.1 1.109zm8 6.891h-1.998v-2.861c0-1.881-2.002-1.722-2.002 0v2.861h-2v-6h2v1.042c.276-.595 1.112-1.042 2.002-1.042 1.849 0 1.998 1.968 1.998 2.568v3.432z" />
                        </svg>
                    </a>
                </div>
                <div class="mt-4 text-gray-300">
                    <p>البريد الإلكتروني: <EMAIL></p>
                    <p>الهاتف: 966-55-555-5555</p>
                </div>
            </div>
        </div>
        <div class="text-center mt-8 border-t border-gray-700 pt-4">
            <p class="text-gray-400">&copy; 2024 منصة التوظيف. جميع الحقوق محفوظة.</p>
        </div>
    </footer>
</body>
</html>