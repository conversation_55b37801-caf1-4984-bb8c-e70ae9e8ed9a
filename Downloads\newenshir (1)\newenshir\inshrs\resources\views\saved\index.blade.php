@extends('layouts.dashboard')

@section('title', 'العناصر المحفوظة')

@section('content')
<div class="container mx-auto px-4 py-8 font-sans">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div>
                <h1 class="text-4xl font-bold text-gray-800 mb-2 flex items-center">
                    <i class="fas fa-bookmark text-indigo-600 ml-3"></i>
                    العناصر المحفوظة
                </h1>
                <p class="text-gray-600 text-lg">جميع الإعلانات والوظائف والملفات الشخصية التي قمت بحفظها.</p>
            </div>
            
            <!-- إحصائيات سريعة -->
            <div class="flex flex-wrap gap-3 mt-6 md:mt-0">
                <div class="bg-indigo-50 text-indigo-700 px-5 py-3 rounded-lg shadow-sm flex items-center">
                    <i class="fas fa-archive ml-2"></i>
                    <div>
                        <span class="font-semibold text-xl">{{ $stats['total'] }}</span>
                        <span class="text-sm block">إجمالي</span>
                    </div>
                </div>
                <div class="bg-sky-50 text-sky-700 px-5 py-3 rounded-lg shadow-sm flex items-center">
                    <i class="fas fa-bullhorn ml-2"></i>
                    <div>
                        <span class="font-semibold text-xl">{{ $stats['ads'] }}</span>
                        <span class="text-sm block">إعلان</span>
                    </div>
                </div>
                <div class="bg-teal-50 text-teal-700 px-5 py-3 rounded-lg shadow-sm flex items-center">
                    <i class="fas fa-briefcase ml-2"></i>
                    <div>
                        <span class="font-semibold text-xl">{{ $stats['jobs'] }}</span>
                        <span class="text-sm block">وظيفة</span>
                    </div>
                </div>
                <div class="bg-amber-50 text-amber-700 px-5 py-3 rounded-lg shadow-sm flex items-center">
                    <i class="fas fa-user-tie ml-2"></i>
                    <div>
                        <span class="font-semibold text-xl">{{ $stats['job_seekers'] }}</span>
                        <span class="text-sm block">باحث</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر وأزرار الإجراءات -->
    <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
        <div class="flex flex-col md:flex-row justify-between items-center gap-4">
            <!-- تبويبات الفلترة -->
            <div class="flex flex-wrap gap-3">
                <a href="{{ route('saved.index', ['type' => 'all']) }}" 
                   class="filter-tab-pro {{ $type === 'all' ? 'active' : '' }}">
                    <i class="fas fa-list-ul ml-2"></i>
                    الكل ({{ $stats['total'] }})
                </a>
                <a href="{{ route('saved.index', ['type' => 'ad']) }}" 
                   class="filter-tab-pro {{ $type === 'ad' ? 'active' : '' }}">
                    <i class="fas fa-bullhorn ml-2"></i>
                    الإعلانات ({{ $stats['ads'] }})
                </a>
                <a href="{{ route('saved.index', ['type' => 'job']) }}" 
                   class="filter-tab-pro {{ $type === 'job' ? 'active' : '' }}">
                    <i class="fas fa-briefcase ml-2"></i>
                    الوظائف ({{ $stats['jobs'] }})
                </a>
                <a href="{{ route('saved.index', ['type' => 'job_seeker']) }}" 
                   class="filter-tab-pro {{ $type === 'job_seeker' ? 'active' : '' }}">
                    <i class="fas fa-user-tie ml-2"></i>
                    الباحثين ({{ $stats['job_seekers'] }})
                </a>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="flex gap-3 mt-4 md:mt-0">
                @if($stats['total'] > 0)
                    <button onclick="clearAllSaved()" class="btn-pro btn-danger-pro">
                        <i class="fas fa-trash-alt ml-2"></i>
                        حذف الكل
                    </button>
                    <button onclick="exportSaved()" class="btn-pro btn-secondary-pro">
                        <i class="fas fa-file-export ml-2"></i>
                        تصدير
                    </button>
                @endif
            </div>
        </div>
    </div>

    <!-- العناصر المحفوظة -->
    @if($savedItems->count() > 0)
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
            @foreach($savedItems as $savedItem)
                @php
                    $item = $savedItem->item;
                @endphp
                
                @if($item)
                    <div class="saved-item-card-pro bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-2xl transform hover:-translate-y-1">
                        <div class="p-5">
                            <div class="flex justify-between items-start mb-3">
                                <span class="item-type-badge-pro {{ $savedItem->item_type }}">
                                    @if($savedItem->item_type === 'ad')
                                        <i class="fas fa-bullhorn"></i>
                                        إعلان
                                    @elseif($savedItem->item_type === 'job')
                                        <i class="fas fa-briefcase"></i>
                                        وظيفة
                                    @else
                                        <i class="fas fa-user-tie"></i>
                                        باحث عن عمل
                                    @endif
                                </span>
                                
                                <button onclick="unsaveItem('{{ $savedItem->item_type }}', {{ $savedItem->item_id }})" 
                                        class="unsave-btn-pro" title="إلغاء الحفظ">
                                    <i class="fas fa-bookmark"></i>
                                </button>
                            </div>

                            <h3 class="text-xl font-semibold text-gray-800 mb-2 line-clamp-2">
                                <a href="{{ $savedItem->item_url }}" class="hover:text-indigo-600 transition-colors">
                                    {{ $savedItem->item_title }}
                                </a>
                            </h3>
                            
                            @if($savedItem->item_type === 'ad' && $item->description)
                                <p class="text-gray-600 text-sm mb-4 line-clamp-3">
                                    {{ Str::limit(strip_tags($item->description), 120) }}
                                </p>
                            @elseif($savedItem->item_type === 'job' && $item->description)
                                <p class="text-gray-600 text-sm mb-4 line-clamp-3">
                                    {{ Str::limit(strip_tags($item->description), 120) }}
                                </p>
                            @elseif($savedItem->item_type === 'job_seeker' && $item->description)
                                <p class="text-gray-600 text-sm mb-4 line-clamp-3">
                                    {{ Str::limit(strip_tags($item->description), 120) }}
                                </p>
                            @endif

                            <div class="flex justify-between items-center text-sm text-gray-500 mb-4">
                                @if($savedItem->item_type === 'ad' && $item->price)
                                    <span class="text-green-600 font-bold text-lg">
                                        {{ number_format($item->price) }} ريال
                                    </span>
                                @elseif($savedItem->item_type === 'job' && $item->salary)
                                    <span class="text-green-600 font-bold text-lg">
                                        {{ number_format($item->salary) }} ريال
                                    </span>
                                @else
                                    <span></span> <!-- يحافظ على التخطيط -->
                                @endif
                                
                                <span class="flex items-center">
                                    <i class="fas fa-clock ml-1 text-gray-400"></i>
                                    حُفظ {{ $savedItem->saved_at->diffForHumans() }}
                                </span>
                            </div>
                        </div>
                        <div class="bg-gray-50 p-4 border-t border-gray-200">
                            <a href="{{ $savedItem->item_url }}" 
                               class="btn-pro btn-primary-pro w-full text-center">
                                <i class="fas fa-external-link-alt ml-2"></i>
                                عرض التفاصيل
                            </a>
                        </div>
                    </div>
                @else
                    <!-- عنصر محذوف -->
                    <div class="saved-item-card-pro bg-gray-100 rounded-xl shadow-lg overflow-hidden border border-dashed border-gray-300">
                        <div class="p-6 text-center flex flex-col items-center justify-center h-full">
                            <i class="fas fa-ghost text-gray-400 text-5xl mb-4"></i>
                            <h3 class="text-xl font-semibold text-gray-700 mb-2">عنصر غير متاح</h3>
                            <p class="text-gray-500 text-sm mb-5">هذا العنصر تم حذفه أو لم يعد متوفراً.</p>
                            <button onclick="unsaveItem('{{ $savedItem->item_type }}', {{ $savedItem->item_id }})" 
                                    class="btn-pro btn-danger-pro btn-sm-pro">
                                <i class="fas fa-times-circle ml-2"></i>
                                إزالة من المحفوظات
                            </button>
                        </div>
                    </div>
                @endif
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="mt-10">
            {{ $savedItems->links('vendor.pagination.tailwind-custom') }} <!-- استخدام تصميم مخصص للـ pagination -->
        </div>
    @else
        <!-- رسالة فارغة -->
        <div class="bg-white rounded-xl shadow-lg p-12 text-center min-h-[400px] flex flex-col justify-center items-center">
            <svg class="w-24 h-24 text-gray-300 mb-6" fill="none" stroke="currentColor" viewBox="0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path></svg>
            <h3 class="text-2xl font-semibold text-gray-700 mb-3">
                @if($type === 'all')
                    قائمة المحفوظات فارغة حالياً
                @elseif($type === 'ad')
                    لا توجد إعلانات محفوظة بعد
                @elseif($type === 'job')
                    لا توجد وظائف محفوظة بعد
                @else
                    لا توجد ملفات باحثين محفوظة بعد
                @endif
            </h3>
            <p class="text-gray-500 mb-8 max-w-md">يبدو أنك لم تقم بحفظ أي عناصر حتى الآن. ابدأ بتصفح الموقع وحفظ ما يثير اهتمامك للعودة إليه لاحقاً.</p>
            <div class="flex flex-col sm:flex-row justify-center gap-4">
                <a href="{{ route('ads.index') }}" class="btn-pro btn-primary-pro">
                    <i class="fas fa-search-dollar ml-2"></i>
                    تصفح الإعلانات المميزة
                </a>
                <a href="{{ route('jobs.index') }}" class="btn-pro btn-secondary-pro">
                    <i class="fas fa-briefcase ml-2"></i>
                    اكتشف أحدث الوظائف
                </a>
            </div>
        </div>
    @endif
</div>


<script>
function unsaveItem(itemType, itemId) {
    if (!confirm('هل أنت متأكد من إلغاء حفظ هذا العنصر؟')) {
        return;
    }
    
    fetch('/saved/toggle', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            item_type: itemType,
            item_id: itemId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'حدث خطأ');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}

function clearAllSaved() {
    if (!confirm('هل أنت متأكد من حذف جميع العناصر المحفوظة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        return;
    }
    
    const type = '{{ $type }}';
    
    fetch('/saved/clear-all?type=' + type, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('حدث خطأ');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}

function exportSaved() {
    const type = '{{ $type }}';
    window.open('/saved/export?type=' + type, '_blank');
}
</script>
@endsection
