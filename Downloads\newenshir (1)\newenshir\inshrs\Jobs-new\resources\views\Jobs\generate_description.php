<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $jobTitle = $_POST['jobTitle'];
    $companyName = $_POST['companyName'];
    $location = $_POST['location'];
    $jobType = $_POST['jobType'];

    $apiKey = 'AIzaSyA_5uqRMBCcFKGgCaVbKc54YEW5KqlrHwY'; // استبدل بمفتاح API الخاص بك
    $apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=' . $apiKey;

    $prompt = "انشئ وصف وظيفة لمنصب $jobTitle في شركة $companyName في $location. نوع الوظيفة: $jobType.";

    $data = [
        'contents' => [
            [
                'parts' => [
                    ['text' => $prompt]
                ]
            ]
        ]
    ];

    $ch = curl_init($apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

    $response = curl_exec($ch);

    if (curl_errno($ch)) {
        echo 'حدث خطأ: ' . curl_error($ch);
    } else {
        $decodedResponse = json_decode($response, true);
        if (isset($decodedResponse['candidates'][0]['content']['parts'][0]['text'])) {
            echo $decodedResponse['candidates'][0]['content']['parts'][0]['text'];
        } else {
            echo 'حدث خطأ أثناء معالجة الطلب.';
        }
    }

    curl_close($ch);
}
?>
