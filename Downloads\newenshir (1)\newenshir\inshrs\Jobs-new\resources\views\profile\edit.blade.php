<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - الملف الشخصي</title>

    <!-- الخطوط -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Alpine.js -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <header class="top-header">
        <div class="header-container">
            <div class="logo-container">
                <a href="{{ url('/') }}">
                    <img src="{{ asset('images/logo.png') }}" alt="Logo" class="logo" onerror="this.src='https://via.placeholder.com/150x50?text=LOGO'">
                </a>
            </div>
            <div class="header-actions">
                <div class="language-switch">
                    <a href="#" class="lang-btn">English</a>
                </div>
                <div class="user-menu" id="userMenuToggle">
                    <i class="fas fa-user-circle user-icon"></i>
                    <span class="user-name-display">{{ Auth::user()->name }}</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </div>
    </header>

    <div class="main-container">
        <!-- القائمة الجانبية -->
        <div class="side-menu">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="user-details">
                    <div class="user-name">{{ Auth::user()->name }}</div>
                    <div class="user-id">{{ Auth::user()->email }}</div>
                </div>
            </div>

            <div class="menu-title">القائمة الرئيسية</div>

            <div class="menu-items">
                <a href="{{ url('/dashboard') }}" class="menu-item">
                    <div class="menu-icon green-icon"><i class="fas fa-chart-line"></i></div>
                    <div class="menu-text">لوحة التحكم</div>
                </a>

                <a href="{{ route('profile.edit') }}" class="menu-item active">
                    <div class="menu-icon green-icon"><i class="fas fa-user-circle"></i></div>
                    <div class="menu-text">الملف الشخصي</div>
                </a>

                <a href="{{ url('/jobs') }}" class="menu-item">
                    <div class="menu-icon green-icon"><i class="fas fa-briefcase"></i></div>
                    <div class="menu-text">الوظائف</div>
                </a>

                <a href="{{ url('/ads') }}" class="menu-item">
                    <div class="menu-icon green-icon"><i class="fas fa-ad"></i></div>
                    <div class="menu-text">الإعلانات</div>
                </a>

                <a href="{{ route('jobs.create') }}" class="menu-item">
                    <div class="menu-icon green-icon"><i class="fas fa-plus-circle"></i></div>
                    <div class="menu-text">نشر وظيفة</div>
                </a>

                <a href="{{ route('ads.create') }}" class="menu-item">
                    <div class="menu-icon green-icon"><i class="fas fa-plus"></i></div>
                    <div class="menu-text">نشر إعلان</div>
                </a>

                <a href="{{ url('/my-jobs') }}" class="menu-item">
                    <div class="menu-icon green-icon"><i class="fas fa-tasks"></i></div>
                    <div class="menu-text">إدارة الوظائف</div>
                </a>

                <a href="{{ url('/ads-user') }}" class="menu-item">
                    <div class="menu-icon green-icon"><i class="fas fa-clipboard-list"></i></div>
                    <div class="menu-text">إعلاناتي</div>
                </a>

                <a href="{{ url('/points/buy') }}" class="menu-item">
                    <div class="menu-icon green-icon"><i class="fas fa-tags"></i></div>
                    <div class="menu-text">الاشتراكات</div>
                </a>

                <a href="{{ route('notifications.index') }}" class="menu-item">
                    <div class="menu-icon green-icon"><i class="fas fa-bell"></i></div>
                    <div class="menu-text">الإشعارات</div>
                    @php
                        $unreadCount = Auth::check() ? \App\Models\Notification::where('user_id', Auth::id())->where('is_read', false)->count() : 0;
                    @endphp
                    @if($unreadCount > 0)
                        <span class="badge">{{ $unreadCount }}</span>
                    @endif
                </a>

                <a href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();" class="logout-button">
                    تسجيل الخروج
                </a>
                <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                    @csrf
                </form>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="content-area">
            <div class="page-header">
                <h1 class="page-title">الملف الشخصي</h1>
                <p class="page-description">إدارة معلومات حسابك وتفضيلات الأمان</p>
            </div>

            <div class="profile-tabs">
                <div class="tab-buttons">
                    <button class="tab-button active" data-tab="profile-info">
                        <i class="fas fa-user"></i> معلومات الملف الشخصي
                    </button>
                    <button class="tab-button" data-tab="password">
                        <i class="fas fa-lock"></i> تغيير كلمة المرور
                    </button>
                    <button class="tab-button" data-tab="delete-account">
                        <i class="fas fa-trash"></i> حذف الحساب
                    </button>
                </div>

                <div class="tab-content">
                    <div class="tab-pane active" id="profile-info">
                        <div class="profile-card">
                            @include('profile.partials.update-profile-information-form')
                        </div>
                    </div>

                    <div class="tab-pane" id="password">
                        <div class="profile-card">
                            @include('profile.partials.update-password-form')
                        </div>
                    </div>

                    <div class="tab-pane" id="delete-account">
                        <div class="profile-card">
                            @include('profile.partials.delete-user-form')
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

<style>
    /* تنسيقات عامة */
    body {
        font-family: 'Figtree', sans-serif;
        background-color: #f5f7fa;
        direction: rtl;
        color: #333;
        margin: 0;
        padding: 0;
    }

    /* شريط التنقل العلوي */
    .top-header {
        background-color: #fff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        padding: 0.75rem 1rem;
        position: fixed;
        top: 0;
        right: 0;
        left: 0;
        z-index: 100;
    }

    .header-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        max-width: 1200px;
        margin: 0 auto;
    }

    .logo-container img {
        height: 40px;
    }

    .header-actions {
        display: flex;
        align-items: center;
    }

    .language-switch {
        margin-left: 1.5rem;
    }

    .lang-btn {
        background-color: #f3f4f6;
        color: #4a90e2;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 600;
    }

    .user-menu {
        display: flex;
        align-items: center;
        cursor: pointer;
    }

    .user-icon {
        font-size: 1.5rem;
        color: #4a90e2;
        margin-left: 0.5rem;
    }

    .user-name-display {
        font-size: 0.875rem;
        font-weight: 600;
        margin-left: 0.5rem;
    }

    /* حاوية المحتوى الرئيسي */
    .main-container {
        display: flex;
        margin-top: 60px; /* ارتفاع الهيدر */
        min-height: calc(100vh - 60px);
    }

    /* منطقة المحتوى */
    .content-area {
        flex: 1;
        padding: 1.5rem;
        margin-right: 280px; /* عرض القائمة الجانبية */
        transition: margin-right 0.3s ease;
    }

    /* القائمة الجانبية */
    .side-menu {
        position: fixed;
        top: 60px; /* ارتفاع الهيدر */
        right: 0;
        width: 280px;
        height: calc(100vh - 60px);
        background-color: #fff;
        box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
        z-index: 90;
        overflow-y: auto;
    }

    .user-info {
        padding: 1.5rem;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #eee;
    }

    .user-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        overflow: hidden;
        margin-left: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f3f4f6;
        color: #4a90e2;
        font-size: 2rem;
    }

    .user-name {
        font-weight: 600;
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }

    .user-id {
        font-size: 0.875rem;
        color: #666;
    }

    .menu-title {
        padding: 1rem 1.5rem;
        font-weight: 600;
        color: #666;
        border-bottom: 1px solid #eee;
    }

    .menu-items {
        padding: 1rem 0;
    }

    .menu-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 1.5rem;
        text-decoration: none;
        color: #333;
        transition: background-color 0.3s ease;
        position: relative;
    }

    .menu-item.active {
        background-color: #f0f7ff;
        color: #4a90e2;
        border-right: 3px solid #4a90e2;
    }

    .menu-item:hover {
        background-color: #f5f7fa;
    }

    .menu-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        color: #666;
    }

    .green-icon {
        color: #4a90e2;
    }

    .menu-text {
        font-size: 0.9375rem;
    }

    .badge {
        position: absolute;
        left: 1.5rem;
        background-color: #e53e3e;
        color: white;
        font-size: 0.75rem;
        padding: 0.1rem 0.5rem;
        border-radius: 10px;
        min-width: 1.5rem;
        text-align: center;
    }

    .logout-button {
        display: block;
        margin: 1rem auto;
        padding: 0.75rem 1.5rem;
        background-color: #f8f8f8;
        color: #e53e3e;
        border: 1px solid #e53e3e;
        border-radius: 4px;
        text-align: center;
        text-decoration: none;
        font-weight: 600;
        width: 80%;
        transition: background-color 0.3s ease, color 0.3s ease;
    }

    .logout-button:hover {
        background-color: #e53e3e;
        color: white;
    }

    /* زر فتح القائمة الجانبية للجوال */
    .menu-toggle {
        position: fixed;
        top: 0.75rem;
        right: 0.75rem;
        width: 40px;
        height: 40px;
        background-color: #4a90e2;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        z-index: 1001;
        cursor: pointer;
        font-size: 1.25rem;
        display: none;
    }

    /* تنسيقات صفحة الملف الشخصي */
    .page-header {
        margin-bottom: 2rem;
    }

    .page-title {
        font-size: 1.75rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
    }

    .page-description {
        font-size: 1rem;
        color: #666;
    }

    .profile-tabs {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }

    .tab-buttons {
        display: flex;
        border-bottom: 1px solid #eee;
        background-color: #f9fafb;
    }

    .tab-button {
        padding: 1rem 1.5rem;
        background: none;
        border: none;
        font-size: 1rem;
        font-weight: 500;
        color: #666;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
    }

    .tab-button i {
        margin-left: 0.5rem;
        font-size: 1.125rem;
    }

    .tab-button.active {
        color: #4a90e2;
        background-color: #fff;
        border-bottom: 2px solid #4a90e2;
    }

    .tab-button:hover:not(.active) {
        background-color: #f0f0f0;
    }

    .tab-content {
        padding: 1.5rem;
    }

    .tab-pane {
        display: none;
    }

    .tab-pane.active {
        display: block;
    }

    .profile-card {
        background-color: #fff;
        border-radius: 8px;
    }

    /* تنسيق النماذج */
    section {
        margin-bottom: 2rem;
    }

    section header {
        margin-bottom: 1.5rem;
    }

    section header h2 {
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
    }

    section header p {
        font-size: 0.875rem;
        color: #666;
    }

    form {
        margin-top: 1.5rem;
    }

    label {
        display: block;
        font-size: 0.875rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
    }

    input[type="text"],
    input[type="email"],
    input[type="password"],
    textarea,
    select {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 1rem;
        color: #333;
        transition: border-color 0.3s ease;
        margin-bottom: 1rem;
    }

    input[type="text"]:focus,
    input[type="email"]:focus,
    input[type="password"]:focus,
    textarea:focus,
    select:focus {
        border-color: #4a90e2;
        outline: none;
        box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
    }

    button {
        padding: 0.75rem 1.5rem;
        background-color: #4a90e2;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    button:hover {
        background-color: #3a7bc8;
    }

    .text-red-600 {
        color: #e53e3e;
    }

    .text-green-600 {
        color: #10b981;
    }

    /* تنسيقات متجاوبة للجوال */
    @media (max-width: 767px) {
        .top-header {
            padding: 0.5rem 0.75rem;
        }

        .logo-container img {
            height: 30px;
        }

        .language-switch {
            margin-left: 1rem;
        }

        .lang-btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        .user-name-display {
            display: none;
        }

        .main-container {
            margin-top: 50px; /* ارتفاع الهيدر في الجوال */
        }

        .side-menu {
            top: 50px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .side-menu.active {
            transform: translateX(0);
        }

        .content-area {
            margin-right: 0;
            padding: 1rem;
        }

        .menu-toggle {
            display: block;
        }

        .tab-buttons {
            flex-direction: column;
        }

        .tab-button {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #eee;
        }

        .tab-button.active {
            border-bottom: 1px solid #eee;
            border-right: 3px solid #4a90e2;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة زر فتح القائمة الجانبية للجوال
        const menuToggle = document.createElement('div');
        menuToggle.className = 'menu-toggle';
        menuToggle.innerHTML = '<i class="fas fa-bars"></i>';
        document.body.appendChild(menuToggle);

        // تفعيل زر فتح القائمة الجانبية
        menuToggle.addEventListener('click', function() {
            const sideMenu = document.querySelector('.side-menu');
            sideMenu.classList.toggle('active');
        });

        // تفعيل قائمة المستخدم في الهيدر
        const userMenuToggle = document.getElementById('userMenuToggle');
        if (userMenuToggle) {
            userMenuToggle.addEventListener('click', function() {
                const sideMenu = document.querySelector('.side-menu');
                sideMenu.classList.toggle('active');
            });
        }

        // تفعيل التبويبات
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabPanes = document.querySelectorAll('.tab-pane');

        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                // إزالة الفئة النشطة من جميع الأزرار
                tabButtons.forEach(btn => btn.classList.remove('active'));
                // إضافة الفئة النشطة للزر المنقور
                this.classList.add('active');

                // إخفاء جميع المحتويات
                tabPanes.forEach(pane => pane.classList.remove('active'));

                // إظهار المحتوى المطلوب
                const tabId = this.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });
    });
</script>