<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
   // database/migrations/xxxx_xx_xx_xxxxxx_create_training_courses_table.php

public function up()
{
    Schema::create('training_courses', function (Blueprint $table) {
        $table->id();  // معرف الدورة التدريبية
        $table->foreignId('basic_info_id')->constrained('basic_info')->onDelete('cascade');  // ارتباط مع جدول basic_info
        $table->string('course_name');  // اسم الدورة التدريبية
        $table->string('provider');  // مقدم الدورة التدريبية
        $table->date('completion_date')->nullable();  // تاريخ الانتهاء
        $table->timestamps();  // تاريخ الإنشاء والتعديل
    });
}

public function down()
{
    Schema::dropIfExists('training_courses');
}

};
