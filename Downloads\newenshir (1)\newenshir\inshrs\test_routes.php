<?php

// ملف اختبار Routes
// تشغيل: php test_routes.php

require_once 'vendor/autoload.php';

// تحميل إعدادات Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\Route;

echo "🔍 اختبار Routes المتعلقة بـ Job Seekers\n";
echo "=========================================\n\n";

try {
    // قائمة Routes المطلوبة
    $requiredRoutes = [
        'job-seekers.show',
        'job_seekers.index',
        'job_seekers.create',
        'job_seekers.store',
        'jobSeeker.edit',
        'jobSeeker.update',
        'jobSeeker.destroy'
    ];

    echo "📋 فحص Routes المطلوبة:\n";
    echo "========================\n";

    foreach ($requiredRoutes as $routeName) {
        try {
            $route = Route::getRoutes()->getByName($routeName);
            if ($route) {
                echo "   ✅ {$routeName} - {$route->uri()}\n";
            } else {
                echo "   ❌ {$routeName} - غير موجود\n";
            }
        } catch (Exception $e) {
            echo "   ❌ {$routeName} - خطأ: {$e->getMessage()}\n";
        }
    }

    echo "\n🔗 اختبار توليد URLs:\n";
    echo "=====================\n";

    // اختبار توليد URLs
    $testRoutes = [
        ['job-seekers.show', ['id' => 1]],
        ['job_seekers.index', []],
        ['job_seekers.create', []],
        ['jobSeeker.edit', ['id' => 1]]
    ];

    foreach ($testRoutes as [$routeName, $params]) {
        try {
            $url = route($routeName, $params);
            echo "   ✅ {$routeName} - {$url}\n";
        } catch (Exception $e) {
            echo "   ❌ {$routeName} - خطأ: {$e->getMessage()}\n";
        }
    }

    echo "\n📊 إحصائيات Routes:\n";
    echo "==================\n";

    $allRoutes = Route::getRoutes();
    $jobSeekerRoutes = [];

    foreach ($allRoutes as $route) {
        $name = $route->getName();
        $uri = $route->uri();
        
        if ($name && (
            str_contains($name, 'job') || 
            str_contains($name, 'seeker') || 
            str_contains($uri, 'job-seeker') ||
            str_contains($uri, 'jobSeeker')
        )) {
            $jobSeekerRoutes[] = [
                'name' => $name,
                'uri' => $uri,
                'methods' => implode('|', $route->methods())
            ];
        }
    }

    echo "   📈 إجمالي Routes المتعلقة بـ Job Seekers: " . count($jobSeekerRoutes) . "\n\n";

    echo "📝 قائمة Routes المتعلقة بـ Job Seekers:\n";
    echo "=====================================\n";

    foreach ($jobSeekerRoutes as $route) {
        echo "   🔗 {$route['name']} - {$route['methods']} - {$route['uri']}\n";
    }

    echo "\n🧪 اختبار الوصول للـ Controller:\n";
    echo "===============================\n";

    try {
        $controller = new \App\Http\Controllers\JobSeekerController();
        echo "   ✅ JobSeekerController - يمكن إنشاؤه\n";
        
        // فحص Methods
        $methods = ['index', 'show', 'create', 'store', 'edit', 'update', 'destroy'];
        foreach ($methods as $method) {
            if (method_exists($controller, $method)) {
                echo "   ✅ Method {$method} - موجود\n";
            } else {
                echo "   ❌ Method {$method} - مفقود\n";
            }
        }
        
    } catch (Exception $e) {
        echo "   ❌ JobSeekerController - خطأ: {$e->getMessage()}\n";
    }

    echo "\n🔍 فحص JobSeeker Model:\n";
    echo "======================\n";

    try {
        $jobSeeker = \App\Models\JobSeeker::first();
        if ($jobSeeker) {
            echo "   ✅ JobSeeker Model - يعمل\n";
            echo "   📊 عدد Job Seekers: " . \App\Models\JobSeeker::count() . "\n";
            echo "   👤 مثال: {$jobSeeker->job_title} - {$jobSeeker->user->name}\n";
        } else {
            echo "   ⚠️ JobSeeker Model - لا توجد بيانات\n";
        }
    } catch (Exception $e) {
        echo "   ❌ JobSeeker Model - خطأ: {$e->getMessage()}\n";
    }

    echo "\n🎯 خلاصة الاختبار:\n";
    echo "==================\n";

    $workingRoutes = 0;
    $totalRoutes = count($requiredRoutes);

    foreach ($requiredRoutes as $routeName) {
        try {
            $route = Route::getRoutes()->getByName($routeName);
            if ($route) {
                $workingRoutes++;
            }
        } catch (Exception $e) {
            // Route لا يعمل
        }
    }

    echo "   📊 Routes تعمل: {$workingRoutes}/{$totalRoutes}\n";
    
    if ($workingRoutes == $totalRoutes) {
        echo "   🎉 جميع Routes تعمل بشكل صحيح!\n";
    } else {
        echo "   ⚠️ بعض Routes لا تعمل، تحقق من routes/web.php\n";
    }

    echo "\n📋 التوصيات:\n";
    echo "============\n";

    if ($workingRoutes < $totalRoutes) {
        echo "   🔧 شغل: php artisan route:clear\n";
        echo "   🔧 شغل: php artisan config:clear\n";
        echo "   🔧 تحقق من routes/web.php\n";
        echo "   🔧 تأكد من وجود JobSeekerController\n";
    }

    echo "   🌐 اختبر الصفحات:\n";
    echo "      - /jobSeekers (قائمة الباحثين)\n";
    echo "      - /job-seekers/1 (تفاصيل باحث)\n";
    echo "      - /job-seekers (إضافة باحث جديد)\n";

} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
    echo "\nتفاصيل الخطأ:\n";
    echo $e->getTraceAsString() . "\n";
}

echo "\n📞 للمساعدة:\n";
echo "============\n";
echo "- تحقق من ملف routes/web.php\n";
echo "- راجع JobSeekerController.php\n";
echo "- تأكد من وجود JobSeeker Model\n";
echo "- اختبر الصفحات مباشرة في المتصفح\n";

?>
