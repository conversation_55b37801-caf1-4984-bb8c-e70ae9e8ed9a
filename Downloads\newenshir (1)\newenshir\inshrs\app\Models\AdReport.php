<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdReport extends Model
{
    use HasFactory;

    protected $fillable = [
        'ad_id',
        'user_id',
        'report_type',
        'reason',
        'status',
        'admin_notes',
        'reviewed_at'
    ];

    protected $casts = [
        'reviewed_at' => 'datetime',
    ];

    /**
     * العلاقة مع الإعلان
     */
    public function ad()
    {
        return $this->belongsTo(Ad::class);
    }

    /**
     * العلاقة مع المستخدم المبلغ
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * نطاق للبلاغات المعلقة
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * نطاق للبلاغات التي تمت مراجعتها
     */
    public function scopeReviewed($query)
    {
        return $query->where('status', 'reviewed');
    }

    /**
     * نطاق للبلاغات المرفوضة
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }
}
