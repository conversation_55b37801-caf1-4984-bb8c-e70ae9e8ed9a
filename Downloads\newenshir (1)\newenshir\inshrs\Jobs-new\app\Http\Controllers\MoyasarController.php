<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Models\PaymentTransaction;
use App\Models\User;

class MoyasarController extends Controller
{
    protected $apiKey;
    protected $secretKey;
    protected $mode;
    protected $baseUrl;

    public function __construct()
    {
        $this->apiKey = config('services.moyasar.key');
        $this->secretKey = config('services.moyasar.secret');
        $this->mode = config('services.moyasar.mode');
        $this->baseUrl = 'https://api.moyasar.com/v1';
    }

    /**
     * إنشاء صفحة الدفع
     */
    public function createPayment(Request $request)
    {
        $request->validate([
            'amount' => 'required|integer|in:5,10,20,50,100',
        ]);

        $user = Auth::user();
        $amount = $request->amount;

        // تحويل المبلغ إلى هللات (1 ريال = 100 هللة)
        $amountInHalalas = $amount * 100; // 0.01 ريال لكل نقطة × 100 هللة

        // إنشاء معرف فريد للمعاملة
        $orderId = 'order_' . time() . '_' . $user->id;

        try {
            // إنشاء رابط الدفع باستخدام HTTP Client
            $response = Http::withHeaders([
                    'Authorization' => 'Basic ' . base64_encode($this->apiKey . ':')
                ])
                ->post($this->baseUrl . '/payments', [
                    'amount' => $amountInHalalas,
                    'currency' => 'SAR',
                    'description' => "شراء {$amount} نقطة",
                    'callback_url' => route('moyasar.callback'),
                    'source' => [
                        'type' => 'stcpay'
                    ],
                    'metadata' => [
                        'user_id' => $user->id,
                        'points_amount' => $amount,
                        'order_id' => $orderId
                    ]
                ]);

            if ($response->failed()) {
                Log::error('Moyasar API Error', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return redirect()->route('points.buy')->with('error', 'حدث خطأ أثناء الاتصال بخدمة الدفع. يرجى المحاولة مرة أخرى.');
            }

            $payment = $response->json();

            // تسجيل المعاملة في قاعدة البيانات
            $transaction = new PaymentTransaction([
                'user_id' => $user->id,
                'transaction_id' => $payment['id'],
                'payment_method' => 'stcpay',
                'amount' => number_format($amount * 0.01, 2, '.', ''),
                'points_amount' => $amount,
                'status' => 'PENDING',
                'payment_details' => json_encode($payment),
            ]);
            $transaction->save();

            // إعادة توجيه المستخدم إلى صفحة الدفع
            if (isset($payment['source']['transaction_url'])) {
                return redirect($payment['source']['transaction_url']);
            } else {
                Log::error('Moyasar Payment URL not found', ['payment' => $payment]);
                return redirect()->route('points.buy')->with('error', 'لم يتم العثور على رابط الدفع. يرجى المحاولة مرة أخرى.');
            }

        } catch (\Exception $e) {
            Log::error('Moyasar Error: ' . $e->getMessage());
            return redirect()->route('points.buy')->with('error', 'حدث خطأ أثناء إنشاء طلب الدفع. يرجى المحاولة مرة أخرى.');
        }
    }

    /**
     * معالجة استجابة الدفع
     */
    public function callback(Request $request)
    {
        $paymentId = $request->id;

        try {
            // استرجاع تفاصيل الدفع من Moyasar
            $response = Http::withHeaders([
                    'Authorization' => 'Basic ' . base64_encode($this->apiKey . ':')
                ])
                ->get($this->baseUrl . '/payments/' . $paymentId);

            if ($response->failed()) {
                Log::error('Moyasar API Error', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                return redirect()->route('points.buy')->with('error', 'حدث خطأ أثناء التحقق من حالة الدفع. يرجى المحاولة مرة أخرى.');
            }

            $payment = $response->json();

            // البحث عن المعاملة في قاعدة البيانات
            $transaction = PaymentTransaction::where('transaction_id', $paymentId)->first();

            if (!$transaction) {
                Log::error('Moyasar Callback: Transaction not found', ['payment_id' => $paymentId]);
                return redirect()->route('points.buy')->with('error', 'لم يتم العثور على المعاملة. يرجى الاتصال بالدعم.');
            }

            // تحديث حالة المعاملة
            if ($payment['status'] === 'paid') {
                // تحديث المعاملة
                $transaction->status = 'COMPLETED';
                $transaction->payment_details = json_encode($payment);
                $transaction->completed_at = now();
                $transaction->save();

                // إضافة النقاط للمستخدم
                $user = User::find($transaction->user_id);
                if ($user) {
                    $user->points = ($user->points ?? 0) + $transaction->points_amount;
                    $user->save();

                    Log::info('Points added successfully', [
                        'user_id' => $user->id,
                        'points_added' => $transaction->points_amount,
                        'transaction_id' => $transaction->id
                    ]);

                    return redirect()->route('points.buy')->with('success', "تم إضافة {$transaction->points_amount} نقطة إلى حسابك بنجاح!");
                }
            } else {
                // تحديث المعاملة كفاشلة
                $transaction->status = 'FAILED';
                $transaction->payment_details = json_encode($payment);
                $transaction->save();

                Log::error('Moyasar Payment Failed', ['payment' => $payment]);
                return redirect()->route('points.buy')->with('error', 'فشلت عملية الدفع. يرجى المحاولة مرة أخرى.');
            }

        } catch (\Exception $e) {
            Log::error('Moyasar Callback Error: ' . $e->getMessage());
            return redirect()->route('points.buy')->with('error', 'حدث خطأ أثناء معالجة الدفع. يرجى الاتصال بالدعم.');
        }

        return redirect()->route('points.buy')->with('error', 'حدث خطأ غير متوقع. يرجى التحقق من حالة المعاملة في سجل المعاملات.');
    }
}
