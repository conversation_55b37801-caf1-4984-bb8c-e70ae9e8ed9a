<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $resume->full_name }} - السيرة الذاتية</title>
    <style>
        @page {
            margin: 0;
        }

        /* استخدام خط DejaVu Sans الافتراضي الذي يدعم اللغة العربية في DomPDF */
        * {
            font-family: 'DejaVu Sans', sans-serif;
        }

        body {
            margin: 0;
            padding: 0;
            color: {{ $resume->text_color ?? '#333' }};
            background-color: {{ $resume->background_color ?? '#fff' }};
            line-height: 1.6;
            direction: rtl;
            text-align: right;
        }

        .resume-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: {{ $resume->background_color ?? '#fff' }};
        }

        .resume-header {
            background-color: {{ $resume->primary_color ?? '#E67E22' }};
            color: white;
            padding: 30px;
            position: relative;
            text-align: right;
        }

        .resume-header-content {
            display: block;
            position: relative;
        }

        .resume-photo-container {
            float: left;
            width: 150px;
            margin-right: 30px;
        }

        .resume-photo {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            border: 5px solid rgba(255, 255, 255, 0.3);
        }

        .resume-name {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .resume-title {
            font-size: 20px;
            opacity: 0.9;
            margin-bottom: 15px;
        }

        .resume-contact {
            margin-top: 20px;
        }

        .resume-contact-item {
            margin-bottom: 5px;
        }

        .resume-body {
            padding: 30px;
        }

        .resume-section {
            margin-bottom: 25px;
            page-break-inside: avoid;
        }

        .resume-section-title {
            font-size: 22px;
            font-weight: bold;
            color: {{ $resume->primary_color ?? '#E67E22' }}; /* يمكن تغيير هذا اللون */
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        .resume-section-content {
            white-space: pre-line;
        }

        .resume-columns {
            overflow: hidden;
            margin: 0 -15px;
        }

        .resume-column {
            float: right;
            width: 30%;
            padding: 0 15px;
            box-sizing: border-box;
        }

        .no-photo-placeholder {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            text-align: center;
            line-height: 150px;
            font-size: 4rem;
            color: white;
        }

        .page-break {
            page-break-after: always;
        }

        .clearfix::after {
            content: "";
            clear: both;
            display: table;
        }
    </style>
</head>
<body>
    <div class="resume-container">
        <div class="resume-header">
            <div class="resume-header-content clearfix">
                @if($resume->photo)
                    <div class="resume-photo-container">
                        <img src="{{ public_path('storage/' . $resume->photo) }}" alt="{{ $resume->full_name }}" class="resume-photo">
                    </div>
                @endif

                <div>
                    <h1 class="resume-name">{{ $resume->full_name }}</h1>
                    @if($resume->job_title)
                        <div class="resume-title">{{ $resume->job_title }}</div>
                    @endif

                    <div class="resume-contact">
                        <div class="resume-contact-item">
                            البريد الإلكتروني: {{ $resume->email }}
                        </div>

                        @if($resume->phone)
                            <div class="resume-contact-item">
                                الهاتف: {{ $resume->phone }}
                            </div>
                        @endif

                        @if($resume->address)
                            <div class="resume-contact-item">
                                العنوان: {{ $resume->address }}
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <div class="resume-body">
            @if($resume->summary)
                <div class="resume-section">
                    <h3 class="resume-section-title">نبذة مختصرة</h3>
                    <div class="resume-section-content">
                        {{ $resume->summary }}
                    </div>
                </div>
            @endif

            @if($resume->skills)
                <div class="resume-section">
                    <h3 class="resume-section-title">المهارات</h3>
                    <div class="resume-section-content">
                        {{ $resume->skills }}
                    </div>
                </div>
            @endif

            @if($resume->experience)
                <div class="resume-section">
                    <h3 class="resume-section-title">الخبرات العملية</h3>
                    <div class="resume-section-content">
                        {{ $resume->experience }}
                    </div>
                </div>
            @endif

            @if($resume->education)
                <div class="resume-section">
                    <h3 class="resume-section-title">التعليم</h3>
                    <div class="resume-section-content">
                        {{ $resume->education }}
                    </div>
                </div>
            @endif

            <div class="resume-columns clearfix">
                @if($resume->languages)
                    <div class="resume-column">
                        <div class="resume-section">
                            <h3 class="resume-section-title">اللغات</h3>
                            <div class="resume-section-content">
                                {{ $resume->languages }}
                            </div>
                        </div>
                    </div>
                @endif

                @if($resume->certifications)
                    <div class="resume-column">
                        <div class="resume-section">
                            <h3 class="resume-section-title">الشهادات</h3>
                            <div class="resume-section-content">
                                {{ $resume->certifications }}
                            </div>
                        </div>
                    </div>
                @endif

                @if($resume->interests)
                    <div class="resume-column">
                        <div class="resume-section">
                            <h3 class="resume-section-title">الاهتمامات</h3>
                            <div class="resume-section-content">
                                {{ $resume->interests }}
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</body>
</html>
