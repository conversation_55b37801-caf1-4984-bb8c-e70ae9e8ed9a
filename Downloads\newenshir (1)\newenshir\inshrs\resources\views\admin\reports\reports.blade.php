@extends('layouts.admin')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- بطاقات الإحصائيات -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow p-4">
            <h3 class="text-lg font-semibold text-gray-700 mb-2">إجمالي البلاغات</h3>
            <p class="text-2xl font-bold text-blue-600" id="totalReports">0</p>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <h3 class="text-lg font-semibold text-gray-700 mb-2">بلاغات قيد الانتظار</h3>
            <p class="text-2xl font-bold text-yellow-600" id="pendingReports">0</p>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <h3 class="text-lg font-semibold text-gray-700 mb-2">بلاغات تمت معالجتها</h3>
            <p class="text-2xl font-bold text-green-600" id="processedReports">0</p>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <h3 class="text-lg font-semibold text-gray-700 mb-2">بلاغات مرفوضة</h3>
            <p class="text-2xl font-bold text-red-600" id="rejectedReports">0</p>
        </div>
    </div>

    <!-- أدوات التحكم -->
    <div class="bg-white rounded-lg shadow mb-6">
        <div class="p-4">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex items-center gap-4">
                    <div class="relative">
                        <input type="text" id="searchInput" class="form-input pl-10 pr-4 py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500" placeholder="البحث في البلاغات...">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                    <select id="statusFilter" class="form-select rounded-lg border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
                        <option value="">جميع الحالات</option>
                        <option value="pending">قيد الانتظار</option>
                        <option value="processed">تمت المعالجة</option>
                        <option value="rejected">مرفوض</option>
                    </select>
                    <button id="filterBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        تصفية
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول البلاغات -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="overflow-x-auto">
            <table id="reportsTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        @yield('table_headers')
                    </tr>
                </thead>
                <tbody id="reportsTableBody" class="bg-white divide-y divide-gray-200">
                    <!-- سيتم ملء الجدول ديناميكياً -->
                </tbody>
            </table>
        </div>
        <!-- رسالة عدم وجود نتائج -->
        <div id="noResultsMessage" class="hidden p-4 text-center text-gray-500">
            لا توجد بلاغات متطابقة مع معايير البحث
        </div>
    </div>

    <!-- ترقيم الصفحات -->
    <div class="mt-4 flex justify-between items-center">
        <div class="text-sm text-gray-700" id="paginationInfo"></div>
        <div class="space-x-2 rtl:space-x-reverse" id="paginationContainer">
            <!-- سيتم إضافة أزرار الترقيم هنا -->
        </div>
    </div>
</div>

<!-- نافذة تفاصيل البلاغ -->
<div id="reportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
    <div class="relative p-4 w-full max-w-2xl mx-auto mt-24">
        <div class="relative bg-white rounded-lg shadow">
            <div class="flex items-center justify-between p-4 border-b">
                <h3 class="text-xl font-semibold text-gray-900">تفاصيل البلاغ</h3>
                <button type="button" id="closeModalButton" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6 space-y-4">
                <p id="modalReportId" class="text-lg"></p>
                <p id="modalReportType" class="text-lg"></p>
                <p id="modalReportDate" class="text-lg"></p>
                <p id="modalReportStatus" class="text-lg"></p>
                <div class="mt-4">
                    <h4 class="font-semibold mb-2">وصف البلاغ:</h4>
                    <p id="modalReportDescription" class="text-gray-700"></p>
                </div>
            </div>
            <div class="flex items-center justify-end p-4 border-t">
                <button type="button" id="closeModalBtn" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                    إغلاق
                </button>
            </div>
        </div>
    </div>
</div>

@yield('custom_content')

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', () => {
        // البيانات والمتغيرات
        const reportsData = @json($reports);
        let filteredData = [...reportsData];
        let currentPage = 1;
        const itemsPerPage = 10;

        // تحديث البيانات الإحصائية
        function updateStats() {
            document.getElementById('totalReports').textContent = reportsData.length;
            document.getElementById('pendingReports').textContent = reportsData.filter(r => r.status === 'pending').length;
            document.getElementById('processedReports').textContent = reportsData.filter(r => r.status === 'processed').length;
            document.getElementById('rejectedReports').textContent = reportsData.filter(r => r.status === 'rejected').length;
        }

        // عرض البيانات
        function displayData(page, data) {
            const tbody = document.getElementById('reportsTableBody');
            tbody.innerHTML = '';
            const start = (page - 1) * itemsPerPage;
            const end = start + itemsPerPage;
            const paginatedData = data.slice(start, end);

            if (paginatedData.length === 0) {
                document.getElementById('reportsTable').classList.add('hidden');
                document.getElementById('noResultsMessage').classList.remove('hidden');
            } else {
                document.getElementById('reportsTable').classList.remove('hidden');
                document.getElementById('noResultsMessage').classList.add('hidden');
                
                paginatedData.forEach(report => {
                    const row = document.createElement('tr');
                    row.classList.add('hover:bg-gray-50');
                    @yield('row_content')
                    tbody.appendChild(row);
                });
            }
        }

        // تحديث معلومات الترقيم
        function updatePaginationInfo(totalItems) {
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            const start = ((currentPage - 1) * itemsPerPage) + 1;
            const end = Math.min(currentPage * itemsPerPage, totalItems);
            
            document.getElementById('paginationInfo').textContent = 
                `عرض ${start} إلى ${end} من ${totalItems} بلاغ`;
        }

        // تحديث أزرار الترقيم
        function updatePaginationButtons() {
            const container = document.getElementById('paginationContainer');
            const pageCount = Math.ceil(filteredData.length / itemsPerPage);
            
            container.innerHTML = '';
            
            // زر السابق
            const prevButton = document.createElement('button');
            prevButton.innerHTML = '<i class="fas fa-chevron-right"></i>';
            prevButton.classList.add('px-3', 'py-1', 'rounded-md', 'bg-gray-200', 
                'text-gray-700', 'hover:bg-gray-300', 'focus:outline-none', 
                'focus:ring-2', 'focus:ring-blue-500');
            prevButton.disabled = currentPage === 1;
            prevButton.addEventListener('click', () => {
                if (currentPage > 1) {
                    currentPage--;
                    displayData(currentPage, filteredData);
                    updatePaginationInfo(filteredData.length);
                    updatePaginationButtons();
                }
            });
            container.appendChild(prevButton);

            // أرقام الصفحات
            for (let i = 1; i <= pageCount; i++) {
                const pageButton = document.createElement('button');
                pageButton.textContent = i;
                pageButton.classList.add('px-3', 'py-1', 'rounded-md', 
                    'hover:bg-gray-300', 'focus:outline-none', 'focus:ring-2', 
                    'focus:ring-blue-500');
                
                if (i === currentPage) {
                    pageButton.classList.add('bg-blue-600', 'text-white');
                } else {
                    pageButton.classList.add('bg-gray-200', 'text-gray-700');
                }

                pageButton.addEventListener('click', () => {
                    currentPage = i;
                    displayData(currentPage, filteredData);
                    updatePaginationInfo(filteredData.length);
                    updatePaginationButtons();
                });
                container.appendChild(pageButton);
            }

            // زر التالي
            const nextButton = document.createElement('button');
            nextButton.innerHTML = '<i class="fas fa-chevron-left"></i>';
            nextButton.classList.add('px-3', 'py-1', 'rounded-md', 'bg-gray-200', 
                'text-gray-700', 'hover:bg-gray-300', 'focus:outline-none', 
                'focus:ring-2', 'focus:ring-blue-500');
            nextButton.disabled = currentPage === pageCount;
            nextButton.addEventListener('click', () => {
                if (currentPage < pageCount) {
                    currentPage++;
                    displayData(currentPage, filteredData);
                    updatePaginationInfo(filteredData.length);
                    updatePaginationButtons();
                }
            });
            container.appendChild(nextButton);
        }

        // تصفية البيانات
        function filterData(status) {
            filteredData = status 
                ? reportsData.filter(report => report.status === status)
                : [...reportsData];
            
            currentPage = 1;
            displayData(currentPage, filteredData);
            updatePaginationInfo(filteredData.length);
            updatePaginationButtons();
        }

        // البحث في البيانات
        function searchData(query) {
            filteredData = query
                ? reportsData.filter(report => 
                    Object.values(report).some(value => 
                        String(value).toLowerCase().includes(query.toLowerCase())
                    )
                )
                : [...reportsData];
            
            currentPage = 1;
            displayData(currentPage, filteredData);
            updatePaginationInfo(filteredData.length);
            updatePaginationButtons();
        }

        // معالجة الأحداث
        document.getElementById('filterBtn').addEventListener('click', () => {
            filterData(document.getElementById('statusFilter').value);
        });

        document.getElementById('searchInput').addEventListener('input', (e) => {
            searchData(e.target.value.trim());
        });

        // إغلاق النافذة المنبثقة
        ['closeModalButton', 'closeModalBtn'].forEach(id => {
            document.getElementById(id).addEventListener('click', () => {
                document.getElementById('reportModal').classList.add('hidden');
            });
        });

        // التهيئة الأولية
        updateStats();
        displayData(currentPage, filteredData);
        updatePaginationInfo(filteredData.length);
        updatePaginationButtons();
    });
</script>
@yield('custom_scripts')
@endsection
