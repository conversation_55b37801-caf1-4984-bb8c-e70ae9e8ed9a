<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Subcategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'category_id', 'slug', 'name', 'is_active'
    ];

    /**
     * العلاقة مع الفئة الرئيسية
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * العلاقة مع الإعلانات
     */
    public function ads()
    {
        return $this->hasMany(Ad::class);
    }
}
