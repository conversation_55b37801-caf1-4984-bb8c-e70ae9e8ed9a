<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر الإعدادات المحسن</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            text-align: right;
            padding: 2rem 0;
            transition: all 0.3s ease;
        }
        
        body.dark-theme {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: #ecf0f1;
        }
        
        .container {
            max-width: 1200px;
        }
        
        .test-card {
            background: white;
            border-radius: 1.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .dark-theme .test-card {
            background: #34495e;
            color: #ecf0f1;
        }
        
        .test-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 1.5rem;
            text-align: center;
        }
        
        .test-body {
            padding: 2rem;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 1rem;
            padding: 1.5rem;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .dark-theme .feature-card {
            background: #2c3e50;
            border-color: #34495e;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #007bff;
        }
        
        .feature-title {
            color: #007bff;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }
        
        .feature-title i {
            margin-left: 0.5rem;
        }
        
        .demo-button {
            margin: 0.5rem;
            border-radius: 0.75rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        .settings-demo {
            position: relative;
            display: inline-block;
            margin: 1rem;
        }
        
        /* نسخة مبسطة من أنماط زر الإعدادات للاختبار */
        .settings-button-demo {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
            position: relative;
        }
        
        .settings-button-demo:hover {
            background: linear-gradient(135deg, #5a6268 0%, #343a40 100%);
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        }
        
        .settings-icon-demo {
            font-size: 1.5rem;
            transition: transform 0.3s ease;
        }
        
        .settings-button-demo:hover .settings-icon-demo {
            transform: rotate(90deg);
        }
        
        .notification-badge-demo {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            animation: pulse 2s infinite;
            border: 2px solid white;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .status-indicator {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            margin: 0.5rem 0;
            font-weight: 500;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .dark-theme .status-success {
            background: #155724;
            color: #d4edda;
        }
        
        .dark-theme .status-info {
            background: #0c5460;
            color: #d1ecf1;
        }
        
        .changelog {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 1rem;
            padding: 1.5rem;
            margin: 2rem 0;
        }
        
        .dark-theme .changelog {
            background: linear-gradient(135deg, #1565c0 0%, #1976d2 100%);
        }
        
        .changelog h5 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
        
        .dark-theme .changelog h5 {
            color: #bbdefb;
        }
        
        .changelog ul {
            margin: 0;
            padding-right: 1.5rem;
        }
        
        .changelog li {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="test-card">
            <div class="test-header">
                <h1>
                    <i class="fas fa-cogs me-2"></i>
                    زر الإعدادات المحسن
                </h1>
                <p>تحسينات جديدة وميزات متقدمة</p>
            </div>
        </div>

        <!-- Status -->
        <div class="test-card">
            <div class="test-body">
                <div class="status-success status-indicator">
                    <i class="fas fa-check-circle me-2"></i>
                    تم إصلاح خطأ المسار وإضافة تحسينات جديدة
                </div>
                
                <div class="status-info status-indicator">
                    <i class="fas fa-info-circle me-2"></i>
                    جميع الميزات تعمل بشكل صحيح مع دعم المظهر الداكن
                </div>
            </div>
        </div>

        <!-- New Features -->
        <div class="feature-grid">
            <!-- إشعار التنبيه -->
            <div class="feature-card">
                <h5 class="feature-title">
                    <i class="fas fa-bell"></i>
                    إشعار التنبيه
                </h5>
                <p>إضافة إشعار أحمر صغير على زر الإعدادات لتنبيه المستخدم للإشعارات الجديدة.</p>
                
                <div class="settings-demo">
                    <button class="settings-button-demo" onclick="toggleBadge()">
                        <i class="settings-icon-demo fas fa-cog"></i>
                        <span class="notification-badge-demo" id="demoBadge">3</span>
                    </button>
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-primary demo-button" onclick="showBadge()">
                        إظهار الإشعار
                    </button>
                    <button class="btn btn-secondary demo-button" onclick="hideBadge()">
                        إخفاء الإشعار
                    </button>
                </div>
            </div>

            <!-- خيارات جديدة -->
            <div class="feature-card">
                <h5 class="feature-title">
                    <i class="fas fa-plus-circle"></i>
                    خيارات جديدة
                </h5>
                <p>إضافة خيارات جديدة في قائمة الإعدادات:</p>
                <ul>
                    <li><strong>اللغة:</strong> تغيير لغة الواجهة</li>
                    <li><strong>المساعدة:</strong> الحصول على المساعدة والدعم</li>
                    <li><strong>إشعارات محسنة:</strong> رسائل تفاعلية</li>
                </ul>
                
                <button class="btn btn-info demo-button" onclick="testLanguageChange()">
                    اختبار تغيير اللغة
                </button>
                <button class="btn btn-warning demo-button" onclick="testHelp()">
                    اختبار المساعدة
                </button>
            </div>

            <!-- المظهر الداكن -->
            <div class="feature-card">
                <h5 class="feature-title">
                    <i class="fas fa-moon"></i>
                    المظهر الداكن
                </h5>
                <p>دعم كامل للمظهر الداكن مع حفظ التفضيلات في المتصفح.</p>
                
                <button class="btn btn-dark demo-button" onclick="toggleDarkTheme()">
                    <i class="fas fa-adjust me-1"></i>
                    تبديل المظهر
                </button>
                
                <div class="mt-2">
                    <small class="text-muted">المظهر الحالي: <span id="currentTheme">فاتح</span></small>
                </div>
            </div>

            <!-- تحسينات الأداء -->
            <div class="feature-card">
                <h5 class="feature-title">
                    <i class="fas fa-rocket"></i>
                    تحسينات الأداء
                </h5>
                <p>تحسينات في الأداء والتفاعل:</p>
                <ul>
                    <li>تأثيرات انتقالية سلسة</li>
                    <li>تحميل سريع للقوائم</li>
                    <li>ذاكرة محسنة للتفضيلات</li>
                    <li>استجابة فورية للنقرات</li>
                </ul>
                
                <button class="btn btn-success demo-button" onclick="testPerformance()">
                    اختبار الأداء
                </button>
            </div>
        </div>

        <!-- Changelog -->
        <div class="test-card">
            <div class="test-body">
                <div class="changelog">
                    <h5>
                        <i class="fas fa-history me-2"></i>
                        سجل التحديثات
                    </h5>
                    <ul>
                        <li><strong>إصلاح خطأ المسار:</strong> تم حل مشكلة <code>[user.settings] not defined</code></li>
                        <li><strong>إشعار التنبيه:</strong> إضافة إشعار أحمر للتنبيهات الجديدة</li>
                        <li><strong>خيارات جديدة:</strong> إضافة خيارات اللغة والمساعدة</li>
                        <li><strong>المظهر الداكن:</strong> دعم كامل للمظهر الداكن</li>
                        <li><strong>تحسينات الأداء:</strong> تأثيرات أسرع وأكثر سلاسة</li>
                        <li><strong>حفظ التفضيلات:</strong> حفظ الإعدادات في المتصفح</li>
                        <li><strong>رسائل محسنة:</strong> إشعارات أكثر وضوحاً وتفاعلية</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Summary -->
        <div class="test-card">
            <div class="test-body">
                <div class="alert alert-success">
                    <h4 class="alert-heading">
                        <i class="fas fa-check-circle me-2"></i>
                        تم تطوير زر الإعدادات بنجاح!
                    </h4>
                    <p>تم إنشاء مكون إعدادات شامل ومتقدم يتضمن:</p>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>إصلاح جميع أخطاء المسارات</li>
                                <li>إشعارات تفاعلية ملونة</li>
                                <li>دعم المظهر الداكن</li>
                                <li>خيارات متقدمة جديدة</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="mb-0">
                                <li>حفظ التفضيلات محلياً</li>
                                <li>تأثيرات بصرية محسنة</li>
                                <li>استجابة سريعة</li>
                                <li>تصميم متجاوب</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // إدارة إشعار التنبيه
        function showBadge() {
            document.getElementById('demoBadge').style.display = 'flex';
            showMessage('تم إظهار إشعار التنبيه', 'success');
        }
        
        function hideBadge() {
            document.getElementById('demoBadge').style.display = 'none';
            showMessage('تم إخفاء إشعار التنبيه', 'info');
        }
        
        function toggleBadge() {
            const badge = document.getElementById('demoBadge');
            const isVisible = badge.style.display !== 'none';
            
            if (isVisible) {
                hideBadge();
            } else {
                showBadge();
            }
        }
        
        // تبديل المظهر الداكن
        function toggleDarkTheme() {
            document.body.classList.toggle('dark-theme');
            const isDark = document.body.classList.contains('dark-theme');
            
            localStorage.setItem('theme', isDark ? 'dark' : 'light');
            document.getElementById('currentTheme').textContent = isDark ? 'داكن' : 'فاتح';
            
            showMessage(`تم تغيير المظهر إلى ${isDark ? 'الداكن' : 'الفاتح'}`, 'info');
        }
        
        // اختبار تغيير اللغة
        function testLanguageChange() {
            const languages = ['العربية', 'English', 'Français'];
            const currentLang = Math.floor(Math.random() * languages.length);
            
            showMessage(`تم تغيير اللغة إلى ${languages[currentLang]}`, 'info');
        }
        
        // اختبار المساعدة
        function testHelp() {
            showMessage('تم فتح نافذة المساعدة والدعم الفني', 'warning');
        }
        
        // اختبار الأداء
        function testPerformance() {
            const startTime = performance.now();
            
            // محاكاة عملية
            setTimeout(() => {
                const endTime = performance.now();
                const duration = (endTime - startTime).toFixed(2);
                
                showMessage(`تم اختبار الأداء: ${duration} مللي ثانية`, 'success');
            }, 100);
        }
        
        // إظهار الرسائل
        function showMessage(message, type = 'info') {
            const colors = {
                'success': '#28a745',
                'info': '#007bff',
                'warning': '#ffc107',
                'error': '#dc3545'
            };
            
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                left: 20px;
                background: ${colors[type]};
                color: white;
                padding: 0.75rem 1rem;
                border-radius: 0.5rem;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                z-index: 1002;
                opacity: 0;
                transform: translateX(-100%);
                transition: all 0.3s ease;
                max-width: 300px;
            `;
            
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'info' ? 'info-circle' : type === 'warning' ? 'exclamation-triangle' : 'times-circle'} me-2"></i>
                ${message}
            `;
            
            document.body.appendChild(notification);
            
            // إظهار الإشعار
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            // إخفاء الإشعار
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(-100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 300);
            }, 3000);
        }
        
        // تطبيق المظهر المحفوظ
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                document.body.classList.add('dark-theme');
                document.getElementById('currentTheme').textContent = 'داكن';
            }
            
            // إظهار رسالة ترحيب
            setTimeout(() => {
                showMessage('مرحباً! جرب جميع الميزات الجديدة', 'success');
            }, 1000);
        });
    </script>
</body>
</html>
