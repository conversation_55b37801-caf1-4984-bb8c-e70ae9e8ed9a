<?php

namespace App\Services;

use App\Models\Ad;
use App\Models\AdView;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ViewTrackingService
{
    /**
     * تسجيل مشاهدة إعلان مع التحقق من عدم التكرار
     */
    public function trackAdView(Ad $ad, Request $request, $userId = null)
    {
        try {
            // إنشاء مفتاح cache فريد للجهاز والإعلان
            $cacheKey = $this->generateCacheKey($ad->id, $request, $userId);
            
            // التحقق من cache أولاً (للأداء)
            if (Cache::has($cacheKey)) {
                return false; // مشاهدة مكررة
            }
            
            // التحقق من قاعدة البيانات
            if (AdView::hasViewed($ad->id, $request, $userId)) {
                // حفظ في cache لمدة ساعة لتجنب الاستعلامات المتكررة
                Cache::put($cacheKey, true, 3600);
                return false; // مشاهدة مكررة
            }
            
            // تسجيل المشاهدة الجديدة
            $viewRecorded = AdView::recordView($ad->id, $request, $userId);
            
            if ($viewRecorded) {
                // زيادة عداد المشاهدات في جدول الإعلانات
                $ad->increment('views');
                
                // حفظ في cache لمدة ساعة
                Cache::put($cacheKey, true, 3600);
                
                // تسجيل في اللوج للمراقبة
                Log::info("New ad view recorded", [
                    'ad_id' => $ad->id,
                    'user_id' => $userId,
                    'ip' => $request->ip(),
                    'user_agent' => substr($request->header('User-Agent', ''), 0, 100)
                ]);
                
                return true; // مشاهدة جديدة
            }
            
            return false; // فشل في التسجيل
            
        } catch (\Exception $e) {
            Log::error("Error tracking ad view", [
                'ad_id' => $ad->id,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
    
    /**
     * إنشاء مفتاح cache فريد للجهاز والإعلان
     */
    private function generateCacheKey($adId, Request $request, $userId = null)
    {
        $components = [
            'ad_view',
            $adId,
            $request->ip(),
            hash('md5', $request->header('User-Agent', '')),
        ];
        
        if ($userId) {
            $components[] = 'user_' . $userId;
        } else {
            $components[] = 'session_' . $request->session()->getId();
        }
        
        return implode(':', $components);
    }
    
    /**
     * الحصول على إحصائيات المشاهدات لإعلان معين
     */
    public function getAdViewStats($adId)
    {
        return AdView::getAdViewStats($adId);
    }
    
    /**
     * الحصول على المشاهدات اليومية لإعلان معين
     */
    public function getDailyViews($adId, $days = 30)
    {
        return AdView::getDailyViews($adId, $days);
    }
    
    /**
     * تنظيف المشاهدات القديمة
     */
    public function cleanOldViews($days = 30)
    {
        try {
            $deletedCount = AdView::cleanOldViews($days);
            
            Log::info("Cleaned old ad views", [
                'deleted_count' => $deletedCount,
                'older_than_days' => $days
            ]);
            
            return $deletedCount;
            
        } catch (\Exception $e) {
            Log::error("Error cleaning old views", [
                'error' => $e->getMessage()
            ]);
            
            return 0;
        }
    }
    
    /**
     * إعادة حساب عدد المشاهدات لإعلان معين
     */
    public function recalculateAdViews($adId)
    {
        try {
            $actualViews = AdView::where('ad_id', $adId)->count();
            
            $ad = Ad::find($adId);
            if ($ad) {
                $ad->update(['views' => $actualViews]);
                
                Log::info("Recalculated ad views", [
                    'ad_id' => $adId,
                    'actual_views' => $actualViews,
                    'previous_views' => $ad->views
                ]);
                
                return $actualViews;
            }
            
            return 0;
            
        } catch (\Exception $e) {
            Log::error("Error recalculating ad views", [
                'ad_id' => $adId,
                'error' => $e->getMessage()
            ]);
            
            return 0;
        }
    }
    
    /**
     * إعادة حساب عدد المشاهدات لجميع الإعلانات
     */
    public function recalculateAllAdViews()
    {
        try {
            $updated = 0;
            
            Ad::chunk(100, function ($ads) use (&$updated) {
                foreach ($ads as $ad) {
                    $actualViews = AdView::where('ad_id', $ad->id)->count();
                    $ad->update(['views' => $actualViews]);
                    $updated++;
                }
            });
            
            Log::info("Recalculated all ad views", [
                'updated_ads' => $updated
            ]);
            
            return $updated;
            
        } catch (\Exception $e) {
            Log::error("Error recalculating all ad views", [
                'error' => $e->getMessage()
            ]);
            
            return 0;
        }
    }
    
    /**
     * الحصول على أكثر الإعلانات مشاهدة
     */
    public function getMostViewedAds($limit = 10, $days = null)
    {
        $query = Ad::with('user');
        
        if ($days) {
            // الإعلانات الأكثر مشاهدة خلال فترة معينة
            $adIds = AdView::where('viewed_at', '>=', now()->subDays($days))
                ->groupBy('ad_id')
                ->selectRaw('ad_id, COUNT(*) as view_count')
                ->orderBy('view_count', 'desc')
                ->limit($limit)
                ->pluck('ad_id');
                
            $query->whereIn('id', $adIds);
        } else {
            // الإعلانات الأكثر مشاهدة بشكل عام
            $query->orderBy('views', 'desc');
        }
        
        return $query->limit($limit)->get();
    }
    
    /**
     * الحصول على إحصائيات عامة للمشاهدات
     */
    public function getGeneralStats()
    {
        return [
            'total_views' => AdView::count(),
            'unique_viewers_today' => AdView::whereDate('viewed_at', today())
                ->distinct('ip_address')->count(),
            'total_views_today' => AdView::whereDate('viewed_at', today())->count(),
            'total_views_this_week' => AdView::whereBetween('viewed_at', 
                [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'total_views_this_month' => AdView::whereMonth('viewed_at', now()->month)->count(),
            'most_viewed_ad_today' => AdView::whereDate('viewed_at', today())
                ->groupBy('ad_id')
                ->selectRaw('ad_id, COUNT(*) as view_count')
                ->orderBy('view_count', 'desc')
                ->first(),
        ];
    }
}
