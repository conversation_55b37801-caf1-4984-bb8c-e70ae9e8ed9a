<!doctype html>
<html class="no-js" lang="en">
    <head>
        

        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />

        
        
        
    
        
        <script src="//a.fsdn.com/con/js/min/sf.sandiego-cmp-top.js?1746807694"></script>

        
    

        


            


    <script>
        bizx.cmp.ifConsent({ purposes: 'ads', vendors: 'adshield'}, function () {
            var s = document.createElement('script');
            s.async = true;s.id = "FIXtUCr";
            s.setAttribute('data-sdk', 'l/1.1.5');
            s.setAttribute('data-cfasync', 'false');
            s.src = "https://as.sourceforge.net/loader.min.js";
            s.charset = "UTF-8";
            s.data="ithc1r8q8e878b8v808e1p8efy81108k3e8iy28y8e808784y8i801m8i8i878y8e387848r8i28y8e878i8g8712z80808y8e8sf12y81108k8s8e1z89";
            s.setAttribute('onload', "!async function(){let e='html-load.com';const t=window,r=e=>new Promise((t=>setTimeout(t,e))),o=t.atob,a=t.btoa,n=r=>{const n=o('VGhpcyBwYWdlIGNvdWxkIG5vdCBiZSBsb2FkZWQgcHJvcGVybHkgZHVlIHRvIGluY29ycmVjdCAvIGJhZCBmaWx0ZXJpbmcgcnVsZShzKSBvZiBhZGJsb2NrZXJzIGluIHVzZS4gUGxlYXNlIGRpc2FibGUgYWxsIGFkYmxvY2tlcnMgdG8gY29udGludWUgdXNpbmcgdGhlIHdlYnNpdGUuIChjbGljayBPSyBpZiB5b3UnZCBsaWtlIHRvIGxlYXJuIG1vcmUp');if(confirm(n)){const o=new t.URL('https://report.error-report.com/modal'),n=o.searchParams;n.set('url',a(location.href)),n.set('error',a(r.toString())),n.set('domain',e),location.href=o.href}else location.reload()};try{const l=()=>new Promise((e=>{let r=Math.random().toString(),o=Math.random().toString();t.addEventListener('message',(e=>e.data===r&&t.postMessage(o,'*'))),t.addEventListener('message',(t=>t.data===o&&e())),t.postMessage(r,'*')}));async function s(){try{let e=!1,o=Math.random().toString();if(t.addEventListener('message',(t=>{t.data===o+'_as_res'&&(e=!0)})),t.postMessage(o+'_as_req','*'),await l(),await r(500),e)return!0}catch(e){console.error(e)}return!1}const c=[100,500,1e3];for(let i=0;i<=c.length&&!await s();i++){if(i===c.length-1)throw o('RmFpbGVkIHRvIGxvYWQgd2Vic2l0ZSBwcm9wZXJseSBzaW5jZSA')+e+o('IGlzIHRhaW50ZWQuIFBsZWFzZSBhbGxvdyA')+e;await r(c[i])}}catch(d){console.error(d);try{t.localStorage.setItem(t.location.host+'_fa_'+a('last_bfa_at'),Date.now().toString())}catch(m){}try{setInterval((()=>document.querySelectorAll('link,style').forEach((e=>e.remove()))),100),alert(d);const h=await(await fetch('https://error-report.com/report?type=loader_light&url='+a(location.href)+'&error='+a(d),{method:'POST'})).text();let g=!1;t.addEventListener('message',(e=>{'as_modal_loaded'===e.data&&(g=!0)}));let p=document.createElement('iframe');const v=new t.URL('https://report.error-report.com/modal'),u=v.searchParams;u.set('url',a(location.href)),u.set('eventId',h),u.set('error',a(d)),u.set('domain',e),p.src=v.href,p.setAttribute('style','width:100vw;height:100vh;z-index:2147483647;position:fixed;left:0;top:0;');const I=e=>{'close-error-report'===e.data&&(p.remove(),t.removeEventListener('message',I))};t.addEventListener('message',I),document.body.appendChild(p);const G=()=>{const e=p.getBoundingClientRect();return'none'!==t.getComputedStyle(p).display&&0!==e.width&&0!==e.height},f=setInterval((()=>{if(!document.contains(p))return clearInterval(f);G()||(n(d),clearInterval(f))}),1e3);setTimeout((()=>{g||n(errStr)}),3e3)}catch(w){n(w)}}}();");
            s.setAttribute('onerror', "!async function(){const t=window,e=t.atob,r=t.btoa;let o=JSON.parse(e('WyJodG1sLWxvYWQuY29tIiwiZmIuaHRtbC1sb2FkLmNvbSIsImNvbnRlbnQtbG9hZGVyLmNvbSIsImZiLmNvbnRlbnQtbG9hZGVyLmNvbSJd'));const a=o=>{const a=e('VGhpcyBwYWdlIGNvdWxkIG5vdCBiZSBsb2FkZWQgcHJvcGVybHkgZHVlIHRvIGluY29ycmVjdCAvIGJhZCBmaWx0ZXJpbmcgcnVsZShzKSBvZiBhZGJsb2NrZXJzIGluIHVzZS4gUGxlYXNlIGRpc2FibGUgYWxsIGFkYmxvY2tlcnMgdG8gY29udGludWUgdXNpbmcgdGhlIHdlYnNpdGUuIChjbGljayBPSyBpZiB5b3UnZCBsaWtlIHRvIGxlYXJuIG1vcmUp');if(confirm(a)){const e=new t.URL('https://report.error-report.com/modal'),a=e.searchParams;a.set('url',r(location.href)),a.set('error',r(o.toString())),a.set('domain',domain),location.href=e.href}else location.reload()};try{if(void 0===t.as_retry&&(t.as_retry=0),t.as_retry>=o.length)throw e('RmFpbGVkIHRvIGxvYWQgd2Vic2l0ZSBwcm9wZXJseSBzaW5jZSA')+o[0]+e('IGlzIGJsb2NrZWQuIFBsZWFzZSBhbGxvdyA')+o[0];const r=document.getElementById('FIXtUCr'),a=document.createElement('script');for(let t=0;t<r.attributes.length;t++)a.setAttribute(r.attributes[t].name,r.attributes[t].value);const n=new t.URL(r.getAttribute('src'));n.host=o[t.as_retry++],a.setAttribute('src',n.href),r.setAttribute('id',r.getAttribute('id')+'_'),r.parentNode.insertBefore(a,r),r.remove()}catch(e){console.error(e);try{t.localStorage.setItem(t.location.host+'_fa_'+r('last_bfa_at'),Date.now().toString())}catch(t){}try{setInterval((()=>document.querySelectorAll('link,style').forEach((t=>t.remove()))),100),alert(e);const o=await(await fetch('https://error-report.com/report?type=loader_light&url='+r(location.href)+'&error='+r(e),{method:'POST'})).text();let n=!1;t.addEventListener('message',(t=>{'as_modal_loaded'===t.data&&(n=!0)}));let s=document.createElement('iframe');const c=new t.URL('https://report.error-report.com/modal'),l=c.searchParams;l.set('url',r(location.href)),l.set('eventId',o),l.set('error',r(e)),l.set('domain',domain),s.src=c.href,s.setAttribute('style','width: 100vw; height: 100vh; z-index: 2147483647; position: fixed; left: 0; top: 0;');const i=e=>{'close-error-report'===e.data&&(s.remove(),t.removeEventListener('message',i))};t.addEventListener('message',i),document.body.appendChild(s);const d=()=>{const e=s.getBoundingClientRect();return'none'!==t.getComputedStyle(s).display&&0!==e.width&&0!==e.height},m=setInterval((()=>{if(!document.contains(s))return clearInterval(m);d()||(a(e),clearInterval(m))}),1e3);setTimeout((()=>{n||a(errStr,domain)}),3e3)}catch(t){a(t)}}}();");
            document.head.appendChild(s);
        });
    </script>


        <script>
            /*global unescape, window, SF*/
            // Setup our namespace
            if (!window.SF) { window.SF = {}; }
            if (!window.net) { window.net = {}; }
            if (!window.net.sf) { window.net.sf = {}; }
            SF.Ads = {};
            SF.cdn = '//a.fsdn.com/con';
            SF.alluracdn = '//a.fsdn.com/allura/cdn/allura/nf';
            SF.deploy_time = '1746807694';
            SF.sandiego = true;
            SF.sandiego_chrome = true;
            SF.variant = 'sf';
            SF.fpid = 'aaaae66f-100c-47e8-be02-daa4a8e3489d';
            SF.billboard_route = '/software/product/$slug/';
            
            SF.Breakpoints = {
              small: 0,
              medium: 640,
              leaderboard: 743,
              billboard: 985,
              large: 1053,
              xlarge: 1295,
              xxlarge: 1366
            };
            SF.initial_breakpoints_visible = {};
            for (var bp in SF.Breakpoints) {
                if (!SF.Breakpoints.hasOwnProperty(bp)) {
                    continue;
                }
                SF.initial_breakpoints_visible[bp] = !window.matchMedia || window.matchMedia('(min-width: ' + SF.Breakpoints[bp] + 'px)').matches;
            }
            
                
                SF.Ads.viewportWidth = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);
                SF.Ads.viewportHeight = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);
            
        </script>

        
        <script>
            window.ID5EspConfig = {partnerId: 1787};
        </script>
        

        <script>
            bizx.uids.gather();
        </script><script>
                
                bizx.cmp.ifConsent({ purposes: 'ads', vendors: ['amazon-ads']}, function () {
                    
                    /*jshint ignore:start*/
                    !function(a9,a,p,s,t,A,g){if(a[a9])return;function q(c,r){a[a9]._Q.push([c,r])}a[a9]={init:function(){q("i",arguments)},fetchBids:function(){q("f",arguments)},setDisplayBids:function(){},targetingKeys:function(){return[]},_Q:[]};A=p.createElement(s);A.async=!0;A.src=t;g=p.getElementsByTagName(s)[0];g.parentNode.insertBefore(A,g)}("apstag",window,document,"script","//c.amazon-adsystem.com/aax2/apstag.js");
                    /*jshint ignore:end*/
                    /*global apstag*/

                    apstag.init({
                         pubID: 'c97760a3-e14d-4cad-9969-833f3ed793e6',
                         adServer: 'googletag'
                    });
                });
                
            

            SF.Ads.prebidOptions = {
                showIndicators: false,
                analytics: false,
                timeout: 650,
                timeouts_by_bids: {650: 3, 2000: 1, 3000: 0},
                pbjsConfig: { 
                },
                uids: { 
                    ip: "2001:16a4:6:f9a7:30e3:3e11:3d2f:ff9d",
                },
            };

            if (!SF.Ads.prebidOptions.uids.hem) {
                SF.Ads.prebidOptions.uids.hem = bizx.uids.getHem();
            }

            SF.Ads.prebidUnits = [];
            if (SF.initial_breakpoints_visible.leaderboard) {
                var thisUnit =  {
                    bids: [{"bidder":"sovrn","params":{"tagid":"364668"}},
                            {"bidder":"rubicon","params":{"zoneId":"486002","sizes":[2],"position":"atf","accountId":"15680","siteId":"103240"}},
                            {"bidder":"appnexus","params":{"placementId":9265096}},
                            {"bidder":"pubmatic","params":{"adSlot":"4741594","publisherId":"162538"}},
                            ],
                    code: 'div-gpt-ad-*************-0',
                    tag: 'SF_Temp5_728x90_A',
                    mediaTypes: { banner: { sizes: [] } }
                }; 
                if (SF.Ads.viewportWidth >= 728 && SF.Ads.viewportHeight >= 200){
                    thisUnit.mediaTypes.banner.sizes = [[728,90]];
                } 
                if (SF.Ads.viewportWidth >= 970 && SF.Ads.viewportHeight >= 901){
                    thisUnit.mediaTypes.banner.sizes = [[728,90]];
                }

                SF.Ads.prebidUnits.push(thisUnit);
            }
            if (!SF.initial_breakpoints_visible.leaderboard) {
                var thisUnit =  {
                    bids: [{"bidder":"sovrn","params":{"tagid":"542316"}},
                            {"bidder":"rubicon","params":{"zoneId":"610848","sizes":[43],"position":"atf","accountId":"15680","siteId":"103240"}},
                            {"bidder":"appnexus","params":{"placementId":********}},
                            {"bidder":"pubmatic","params":{"adSlot":"4845070","publisherId":"162538"}},
                            ],
                    code: 'div-gpt-ad-*************-0',
                    tag: 'SF_Mobile_320x50_A',
                    mediaTypes: { banner: { sizes: [[320,50]] } }
                };

                SF.Ads.prebidUnits.push(thisUnit);
            }
            if (SF.initial_breakpoints_visible.large) {
                var thisUnit =  {
                    bids: [{"bidder":"sovrn","params":{"tagid":"364665"}},
                            {"bidder":"rubicon","params":{"zoneId":"486002","sizes":[15,10],"position":"atf","accountId":"15680","siteId":"103240"}},
                            {"bidder":"appnexus","params":{"placementId":9265099}},
                            {"bidder":"pubmatic","params":{"adSlot":"4741591","publisherId":"162538"}},
                            ],
                    code: 'div-gpt-ad-*************-0',
                    tag: 'SF_Temp5_300x250_A',
                    mediaTypes: { banner: { sizes: [[300,250],[300,600]] } }
                };

                SF.Ads.prebidUnits.push(thisUnit);
            }
            if (SF.initial_breakpoints_visible.large) {
                var thisUnit =  {
                    bids: [{"bidder":"sovrn","params":{"tagid":"364666"}},
                            {"bidder":"rubicon","params":{"zoneId":"486004","sizes":[15],"accountId":"15680","siteId":"103240"}},
                            {"bidder":"appnexus","params":{"placementId":9265101}},
                            {"bidder":"pubmatic","params":{"adSlot":"4741592","publisherId":"162538"}},
                            ],
                    code: 'div-gpt-ad-*************-0',
                    tag: 'SF_Temp5_300x250_B',
                    mediaTypes: { banner: { sizes: [[300,250]] } }
                };

                SF.Ads.prebidUnits.push(thisUnit);
            }
            if (SF.initial_breakpoints_visible.large) {
                var thisUnit =  {
                    bids: [{"bidder":"sovrn","params":{"tagid":"364667"}},
                            {"bidder":"rubicon","params":{"zoneId":"486004","sizes":[15],"accountId":"15680","siteId":"103240"}},
                            {"bidder":"appnexus","params":{"placementId":9265102}},
                            {"bidder":"pubmatic","params":{"adSlot":"4741593","publisherId":"162538"}},
                            ],
                    code: 'div-gpt-ad-*************-0',
                    tag: 'SF_Temp5_300x250_C',
                    mediaTypes: { banner: { sizes: [[300,250]] } }
                };

                SF.Ads.prebidUnits.push(thisUnit);
            }
            if (!SF.initial_breakpoints_visible.leaderboard) {
                var thisUnit =  {
                    bids: [{"bidder":"sovrn","params":{"tagid":"542317"}},
                            {"bidder":"rubicon","params":{"zoneId":"798260","sizes":[43],"accountId":"15680","siteId":"103240"}},
                            {"bidder":"appnexus","params":{"placementId":********}},
                            {"bidder":"pubmatic","params":{"adSlot":"4845074","publisherId":"162538"}},
                            ],
                    code: 'div-gpt-ad-*************-0',
                    tag: 'SF_Mobile_Multi_B',
                    mediaTypes: { banner: { sizes: [[300,50],[300,250],[320,50],[320,100]] } }
                };

                SF.Ads.prebidUnits.push(thisUnit);
            }
            if (!SF.initial_breakpoints_visible.leaderboard) {
                var thisUnit =  {
                    bids: [{"bidder":"sovrn","params":{"tagid":"542318"}},
                            {"bidder":"rubicon","params":{"zoneId":"798260","sizes":[43],"accountId":"15680","siteId":"103240"}},
                            {"bidder":"appnexus","params":{"placementId":********}},
                            {"bidder":"pubmatic","params":{"adSlot":"4845075","publisherId":"162538"}},
                            ],
                    code: 'div-gpt-ad-*************-0',
                    tag: 'SF_Mobile_Multi_C',
                    mediaTypes: { banner: { sizes: [[300,50],[300,250],[320,50],[320,100]] } }
                };

                SF.Ads.prebidUnits.push(thisUnit);
            }
            SF.Ads.prebidAdjustments = {"bidder_deflations":{"aardvark":1,"aol":0.98,"appnexus":1.07,"brealtime":0.98,"districtm":1.04,"districtmdmx":1.08,"emxdigital":1,"indexex#hange":0.98,"indexexchange":0.95,"komoona":0.92,"oftmedia":0.9,"onefiftytwo":0.9,"pubmatic":1,"pulsepoint":0.93,"rhythmone":0.85,"rubicon":1.23,"rubiconlite":1,"sharethrough":1.07,"smartadserver":1.15,"sovrn":1.25,"springserve":0.001},"inflation":1,"floor":0.02};
        </script>

        

        
            <script id="pbjs_script" data-dom="https://d3tglifpd8whs6.cloudfront.net"  src="//a.fsdn.com/con/js/sftheme/vendor/bizx-prebid.js?1746807694"></script>
        
        <script>
            function logAdsConsent(msg) {
            }
            bizx.cmp.ifConsent({ purposes: 'all', vendors: 'prebid'}, function () { // hasConsentFn
                if (window.bizxPrebid) {
                    window.bizxPrebid.Ads.initPrebid(window.bizxPrebid.adUnits);
                }
                
                    
                    var link = document.createElement("link");
                    link.rel = "preload";
                    link.as = "script";
                    link.href = 'https://securepubads.g.doubleclick.net/tag/js/gpt.js';
                    document.head.appendChild(link);
                
                logAdsConsent('pageview with ads consent');
            }, function() { // rejectedFn
                // no 'prebid' consent (e.g. region without prebid consent string support) but try GPT directly
                bizx.cmp.ifConsent({ purposes: 'all', vendors: 'google-ads'}, function () { // hasConsentFn
                    bizx.cmp.embedScript('https://securepubads.g.doubleclick.net/tag/js/gpt.js');
                    logAdsConsent('pageview without prebid consent but able to run GPT');
                }, function() { // rejectedFn
                    logAdsConsent('pageview without ads consent');
                }, null,
                function() { // unknownFn
                    logAdsConsent('pageview without prebid consent and UNKNOWN ads consent');
                });
            }, null,
            function() { // unknownFn
                logAdsConsent('pageview with UNKNOWN ads consent');
            });
            </script>
        
            
    
    
    
    
    
    
    
    
    <link rel="stylesheet" href="//a.fsdn.com/con/css/lato.css?1746807694">

        
        
        
        <link rel="stylesheet" href="//a.fsdn.com/con/css/sandiego.css?1746807694">
        
        <link rel="stylesheet" href="//a.fsdn.com/con/css/disallow.css?1746807694">

        

        
        <meta name="description" content="An easy to install Apache distribution containing MySQL, PHP, and Perl">
        <meta name="keywords" content="Database Engines/Servers, Site Management, HTTP Servers, Internet,  Open Source, Open Source Software, Development, Community, Source Code, Secure,  Downloads, Free Software">

<noscript>
    <meta http-equiv="refresh" content="5; url=https://downloads.sourceforge.net/project/xampp/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe?ts=gAAAAABoIDv5MR60z0q7o6PxJqM089wR6GdIRZlxCA125Bt0zJBXaLDR7JR5uscOcvzHLzIk59HnWLCJf3k7CCIZBP5r3S3RRg%3D%3D&amp;use_mirror=yer&amp;r=">
</noscript>

        <title>Download xampp-windows-x64-8.2.12-0-VS16-installer.exe (XAMPP)</title>
        <link rel="icon" sizes="180x180" href="//a.fsdn.com/con/img/sandiego/logo-180x180.png" type="image/png">
<link rel="icon" sizes="any" href="//a.fsdn.com/con/img/sandiego/svg/originals/sf-icon-orange-no_sf.svg" type="image/svg+xml">
<link rel="apple-touch-icon" sizes="180x180" href="//a.fsdn.com/con/img/sandiego/logo-180x180.png">
<link rel="mask-icon" href="//a.fsdn.com/con/img/sandiego/svg/originals/sf-icon-orange-no_sf.svg" color="#FF6600">
    
        <link rel="canonical" href="https://sourceforge.net/projects/xampp/">
    
        
        <script>
            /*global unescape, window, console, jQuery, $, net, SF, bizx  */
            if (!window.SF) {
                window.SF = {};
            }SF.EU_country_codes = ["MQ","RO","GI","SE","SI","FK","CH","IS","FR","VG","NC","BE","GF","IE","PF","JE","FI","ES","WF","GR","LI","CW","NL","YT","EE","BG","PL","LU","RE","MF","AW","SX","GP","AI","CY","DE","PT","TF","NO","HR","AT","GL","SH","ME","LV","PN","IO","KY","MT","DK","PM","GB","MS","GS","TC","AX","CZ","GG","BL","BM","LT","HU","IT","SK"];
            SF.unknown_country_codes = ["","A1","A2","O1"];
        </script>
        
    
        
        <script src="//a.fsdn.com/con/js/min/sf.sandiego-head.js?1746807694"></script>

        
    


        <style>.Zae32d36185988607c94f8fd86bc54ca71a3dbe41 { display: none !important; }</style>
    <link rel="alternate" type="application/rss+xml" title="XAMPP&#8230;Recent Activity" href="/p/xampp/activity/feed" />
    


        
<script>SF.adblock = true;</script>  
<script src="//a.fsdn.com/con/js/adpopup.js?1746807694"></script>



<script>
       /*global Foundation */
           /*global googletag, bizxPrebid */
            SF.Ads.gptExpected();
            SF.Ads.usePrebid = true;

       var gptadslots=[];
       var gptadHandlers={};
       var gptadRenderers=[];
       SF.Ads.slotsById = {};
       SF.Ads.idsByPath = {};

        googletag.cmd.push(function() {
            var leaderboard = googletag.sizeMapping()
                .addSize([970, 901], [[728,90]])
                .addSize([728, 200], [[728,90]])
                .build();
            var leaderboardInContent = googletag.sizeMapping()
                .addSize([1280, 200], [[728,90]])
                .addSize([728, 200], [[728,90]])
                .build();

                googletag.pubads().setTargeting('usingSafeFrame','1')
                .setSafeFrameConfig({
                        allowOverlayExpansion: true,
                        allowPushExpansion: true,
                        sandbox: true
                });

                var thisSlot;
                thisSlot = googletag.defineSlot('/41014381/Sourceforge/SF_Temp5_728x90_A',
                                                            [728, 90],'div-gpt-ad-*************-0')
                                                        .defineSizeMapping( leaderboard )
                                                        .addService(googletag.pubads())
                                                            .setTargeting('oss_tpc',["Database","Database Engines/Servers","HTTP Servers","Internet","Site Management"])
                                                            .setTargeting('shortname',"xampp")
                                                            .setTargeting('dc_ref',"https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download")
                                                            .setTargeting('page_type',"pg_download");
                gptadslots.push(thisSlot);
                SF.Ads.slotsById['div-gpt-ad-*************-0'] = thisSlot;
                SF.Ads.idsByPath['/41014381/Sourceforge/SF_Temp5_728x90_A'] = 'div-gpt-ad-*************-0';
                thisSlot = googletag.defineSlot('/41014381/Sourceforge/SF_Mobile_320x50_A',[320,50],'div-gpt-ad-*************-0')
                                                        .addService(googletag.pubads())
                                                            .setTargeting('oss_tpc',["Database","Database Engines/Servers","HTTP Servers","Internet","Site Management"])
                                                            .setTargeting('shortname',"xampp")
                                                            .setTargeting('dc_ref',"https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download")
                                                            .setTargeting('sz',"320x50")
                                                            .setTargeting('page_type',"pg_download");
                gptadslots.push(thisSlot);
                SF.Ads.slotsById['div-gpt-ad-*************-0'] = thisSlot;
                SF.Ads.idsByPath['/41014381/Sourceforge/SF_Mobile_320x50_A'] = 'div-gpt-ad-*************-0';
                thisSlot = googletag.defineSlot('/41014381/Sourceforge/SF_Temp5_GEL_A',"fluid",'div-gpt-ad-1715287086973-0')
                                                        .addService(googletag.pubads())
                                                            .setTargeting('oss_tpc',["Database","Database Engines/Servers","HTTP Servers","Internet","Site Management"])
                                                            .setTargeting('shortname',"xampp")
                                                            .setTargeting('dc_ref',"https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download")
                                                            .setTargeting('sz',"fluid")
                                                            .setTargeting('page_type',"pg_download");
                gptadslots.push(thisSlot);
                SF.Ads.slotsById['div-gpt-ad-1715287086973-0'] = thisSlot;
                SF.Ads.idsByPath['/41014381/Sourceforge/SF_Temp5_GEL_A'] = 'div-gpt-ad-1715287086973-0';
                thisSlot = googletag.defineSlot('/41014381/Sourceforge/SF_Temp5_GEL_B',"fluid",'div-gpt-ad-1715702977782-0')
                                                        .addService(googletag.pubads())
                                                            .setTargeting('oss_tpc',["Database","Database Engines/Servers","HTTP Servers","Internet","Site Management"])
                                                            .setTargeting('shortname',"xampp")
                                                            .setTargeting('dc_ref',"https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download")
                                                            .setTargeting('sz',"fluid")
                                                            .setTargeting('page_type',"pg_download");
                gptadslots.push(thisSlot);
                SF.Ads.slotsById['div-gpt-ad-1715702977782-0'] = thisSlot;
                SF.Ads.idsByPath['/41014381/Sourceforge/SF_Temp5_GEL_B'] = 'div-gpt-ad-1715702977782-0';
                thisSlot = googletag.defineSlot('/41014381/Sourceforge/SF_Temp5_GEL_C',"fluid",'div-gpt-ad-1715705113227-0')
                                                        .addService(googletag.pubads())
                                                            .setTargeting('oss_tpc',["Database","Database Engines/Servers","HTTP Servers","Internet","Site Management"])
                                                            .setTargeting('shortname',"xampp")
                                                            .setTargeting('dc_ref',"https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download")
                                                            .setTargeting('sz',"fluid")
                                                            .setTargeting('page_type',"pg_download");
                gptadslots.push(thisSlot);
                SF.Ads.slotsById['div-gpt-ad-1715705113227-0'] = thisSlot;
                SF.Ads.idsByPath['/41014381/Sourceforge/SF_Temp5_GEL_C'] = 'div-gpt-ad-1715705113227-0';
                thisSlot = googletag.defineSlot('/41014381/Sourceforge/SF_Temp5_GEL_D',"fluid",'div-gpt-ad-1715705310820-0')
                                                        .addService(googletag.pubads())
                                                            .setTargeting('oss_tpc',["Database","Database Engines/Servers","HTTP Servers","Internet","Site Management"])
                                                            .setTargeting('shortname',"xampp")
                                                            .setTargeting('dc_ref',"https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download")
                                                            .setTargeting('sz',"fluid")
                                                            .setTargeting('page_type',"pg_download");
                gptadslots.push(thisSlot);
                SF.Ads.slotsById['div-gpt-ad-1715705310820-0'] = thisSlot;
                SF.Ads.idsByPath['/41014381/Sourceforge/SF_Temp5_GEL_D'] = 'div-gpt-ad-1715705310820-0';
                thisSlot = googletag.defineSlot('/41014381/Sourceforge/SF_Temp5_GEL_E',"fluid",'div-gpt-ad-1715705368750-0')
                                                        .addService(googletag.pubads())
                                                            .setTargeting('oss_tpc',["Database","Database Engines/Servers","HTTP Servers","Internet","Site Management"])
                                                            .setTargeting('shortname',"xampp")
                                                            .setTargeting('dc_ref',"https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download")
                                                            .setTargeting('sz',"fluid")
                                                            .setTargeting('page_type',"pg_download");
                gptadslots.push(thisSlot);
                SF.Ads.slotsById['div-gpt-ad-1715705368750-0'] = thisSlot;
                SF.Ads.idsByPath['/41014381/Sourceforge/SF_Temp5_GEL_E'] = 'div-gpt-ad-1715705368750-0';
                thisSlot = googletag.defineSlot('/41014381/Sourceforge/SF_Temp5_300x250_A',[[300,250],[300,600]],'div-gpt-ad-*************-0')
                                                        .addService(googletag.pubads())
                                                            .setTargeting('oss_tpc',["Database","Database Engines/Servers","HTTP Servers","Internet","Site Management"])
                                                            .setTargeting('shortname',"xampp")
                                                            .setTargeting('dc_ref',"https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download")
                                                            .setTargeting('sz',"300x250,300x600")
                                                            .setTargeting('page_type',"pg_download");
                gptadslots.push(thisSlot);
                SF.Ads.slotsById['div-gpt-ad-*************-0'] = thisSlot;
                SF.Ads.idsByPath['/41014381/Sourceforge/SF_Temp5_300x250_A'] = 'div-gpt-ad-*************-0';
                thisSlot = googletag.defineSlot('/41014381/Sourceforge/SF_Temp5_300x250_B',[300,250],'div-gpt-ad-*************-0')
                                                        .addService(googletag.pubads())
                                                            .setTargeting('oss_tpc',["Database","Database Engines/Servers","HTTP Servers","Internet","Site Management"])
                                                            .setTargeting('shortname',"xampp")
                                                            .setTargeting('dc_ref',"https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download")
                                                            .setTargeting('sz',"300x250")
                                                            .setTargeting('page_type',"pg_download");
                gptadslots.push(thisSlot);
                SF.Ads.slotsById['div-gpt-ad-*************-0'] = thisSlot;
                SF.Ads.idsByPath['/41014381/Sourceforge/SF_Temp5_300x250_B'] = 'div-gpt-ad-*************-0';
                thisSlot = googletag.defineSlot('/41014381/Sourceforge/SF_Temp5_300x250_C',[300,250],'div-gpt-ad-*************-0')
                                                        .addService(googletag.pubads())
                                                            .setTargeting('oss_tpc',["Database","Database Engines/Servers","HTTP Servers","Internet","Site Management"])
                                                            .setTargeting('shortname',"xampp")
                                                            .setTargeting('dc_ref',"https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download")
                                                            .setTargeting('sz',"300x250")
                                                            .setTargeting('page_type',"pg_download");
                gptadslots.push(thisSlot);
                SF.Ads.slotsById['div-gpt-ad-*************-0'] = thisSlot;
                SF.Ads.idsByPath['/41014381/Sourceforge/SF_Temp5_300x250_C'] = 'div-gpt-ad-*************-0';
                thisSlot = googletag.defineSlot('/41014381/Sourceforge/SF_Mobile_Multi_B',[[300,50],[300,250],[320,50],[320,100]],'div-gpt-ad-*************-0')
                                                        .addService(googletag.pubads())
                                                            .setTargeting('oss_tpc',["Database","Database Engines/Servers","HTTP Servers","Internet","Site Management"])
                                                            .setTargeting('shortname',"xampp")
                                                            .setTargeting('dc_ref',"https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download")
                                                            .setTargeting('sz',"300x50,300x250,320x50,320x100")
                                                            .setTargeting('page_type',"pg_download");
                gptadslots.push(thisSlot);
                SF.Ads.slotsById['div-gpt-ad-*************-0'] = thisSlot;
                SF.Ads.idsByPath['/41014381/Sourceforge/SF_Mobile_Multi_B'] = 'div-gpt-ad-*************-0';
                thisSlot = googletag.defineSlot('/41014381/Sourceforge/SF_Mobile_Multi_C',[[300,50],[300,250],[320,50],[320,100]],'div-gpt-ad-*************-0')
                                                        .addService(googletag.pubads())
                                                            .setTargeting('oss_tpc',["Database","Database Engines/Servers","HTTP Servers","Internet","Site Management"])
                                                            .setTargeting('shortname',"xampp")
                                                            .setTargeting('dc_ref',"https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download")
                                                            .setTargeting('sz',"300x50,300x250,320x50,320x100")
                                                            .setTargeting('page_type',"pg_download");
                gptadslots.push(thisSlot);
                SF.Ads.slotsById['div-gpt-ad-*************-0'] = thisSlot;
                SF.Ads.idsByPath['/41014381/Sourceforge/SF_Mobile_Multi_C'] = 'div-gpt-ad-*************-0';   
                
                googletag.pubads().enableAsyncRendering();

                googletag.pubads().collapseEmptyDivs();
                    googletag.pubads().enableLazyLoad({fetchMarginPercent: 0,renderMarginPercent: 0,});googletag.pubads().addEventListener('slotOnload', function(event) {
                    SF.Ads.Helpers.getSizeAndSetClass.call(this, event);
                });
                googletag.pubads().addEventListener('slotRenderEnded', function(event) {
                    var unitName = event.slot.getAdUnitPath();
                    if ( unitName in gptadHandlers ) {
                    for (var i = 0; i < gptadHandlers[unitName].length; i++) {
                        try {
                            SF.Ads.RenderHandlers[gptadHandlers[unitName][i]].call(this, event);
                        } catch (e) {
                        }
                    }
                    }
                    if (event.slot.getResponseInformation()) {
                        SF.Ads.RenderHandlers.decorateSizeDelivered.call(this, event);
                    } else {SF.Ads.RenderHandlers.gotBlank.call(this, event);
                    }
                    if(SF.Ads.scrollFixable) {
                        SF.Ads.scrollFixable.scrollRelocate();
                    }
                });
                googletag.pubads().addEventListener('slotVisibilityChanged', SF.Ads.visibilityChangedTracker);
                SF.Ads.setupAdRefresh();
                googletag.pubads().addEventListener('impressionViewable', SF.Ads.RenderHandlers.viewabilityInstrumentation);
                
                googletag.pubads().addEventListener('slotRenderEnded', SF.Ads.listenerForBlockThis);
                bizxPrebid.Ads.pushToGoogle();

                console.log('GPT enableServices');
                googletag.enableServices();

                if ($.isEmptyObject(SF.Ads.slotsById)) {
                    $('body').addClass("no-ads");
                }
            });
   </script>  

        

        
    <script>
        function initPiwik(){
            var _paq = window._paq = window._paq || [];
            _paq.push(['trackPageView', document.title, {
                    dimension1: 'xampp',
                dimension2: 'pg_dwnld',
                dimension3: SF.devicePixelRatio,
                
            }]);
            _paq.push(['enableLinkTracking']);

            (function() {
                var u="//analytics.slashdotmedia.com/";
                _paq.push(['setTrackerUrl', u+'sf.php']);
                _paq.push(['setSiteId', 39]);
                 
        // only execute if 'measurement' has been granted
        bizx.cmp.ifConsent({ purposes: ['measurement'], vendors: 'sdm'}, function() {
            var interval = 6 * 60 * 60 * 1000; // 6 hrs, expressed in ms
            var vid_date = new Date(localStorage.getItem('vid_date'));
            if (new Date() - vid_date >= interval) {
                var data = {firstparty_id: "aaaae66f-100c-47e8-be02-daa4a8e3489d", do_not_sell: false, is_commercial_page: "False" };
                bizx.cmp.ifConsent({ purposes: ['ads'], vendors: 'sdm'}, function() {},
                    function(){
                        // no consent (opt-out)
                        data.do_not_sell = true;
                    },
                    function(){
                        //finally call api endpoint
                        // push promise to pwik and set it run if pwik is allowed to run based on it's own ifConsent check
                        _paq.push([ function() {
                            data.matomo_id = this.getVisitorId();
                            data.domain = "sourceforge.net";
                            $.ajax({
                                method: 'PUT',
                                url: '/p/sfapi/push_vid',
                                data:  JSON.stringify(data)
                            })
                            .done(function(response){
                                if(response.result) {
                                    localStorage.setItem('vid_date', new Date());
                                }
                            })
                            .fail(function(){
                                // Do nothing on failure
                            });
                        }]);
                    }
                    );
                }
            });
    
                var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
                g.type='text/javascript'; g.async=true; g.defer=true; g.src=u+'sf.js'; s.parentNode.insertBefore(g,s);
            })();
        }
        bizx.cmp.ifConsent({ purposes: ['storage', 'measurement'], vendors: 'sdm' }, initPiwik);
    </script>
<script>
    SF.ShowProgressBar = true;
</script>



        

        <script type="application/ld+json">
            {
                "@context": "http://schema.org",
                "@type": "WebSite",
                "name": "SourceForge",
                "url": "https://sourceforge.net/"
            }
        </script>
    </head>
    <body id="pg_dwnld"
          class="   anonymous has-ads sandiego v-sf">
        

            
        <div id="busy-spinner"></div>
        
        
<div id="messages">
</div>


        <div class="off-canvas position-right" id="offCanvas" data-off-canvas>
    <!-- Menu -->
    <ul class="header-nav-menulist">
        <li class="highlight search">
            
            
                
            
            
    <form method="get" action="/directory/" class="m-search-form">
    
        <input type="text" placeholder="Search for software or solutions" autocomplete="off" name="q" >
        
        <label >
            <input type="submit" class="bt" value="">
            


<svg  data-name="search" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1216 832q0-185-131.5-316.5t-316.5-131.5-316.5 131.5-131.5 316.5 131.5 316.5 316.5 131.5 316.5-131.5 131.5-316.5zm512 832q0 52-38 90t-90 38q-54 0-90-38l-343-342q-179 124-399 124-143 0-273.5-55.5t-225-150-150-225-55.5-273.5 55.5-273.5 150-225 225-150 273.5-55.5 273.5 55.5 225 150 150 225 55.5 273.5q0 220-124 399l343 343q37 37 37 90z"/></svg>
        </label>
    </form>
        </li>
        
        <li class="highlight"><a href="https://sourceforge.net/auth/">Join/Login</a></li>
        
        <li><a href="/software/">Business Software</a></li>
        <li><a href="/directory/">Open Source Software</a></li>
        <li><a  href="/software/vendors/" title="For Vendors">For Vendors</a></li>
        <li><a href="/blog/" title="Blog">Blog</a></li>
        <li><a href="/about">About</a></li>
        <li><a id="header-nav-more" data-toggle="header-nav-more header-nav-more-content" data-toggler=".toggled">More</a></li>
        <li>
            <ul id="header-nav-more-content" class="toggled" data-toggler=".toggled">
                
    

    
    
        <li><a href="/articles/">Articles</a></li>
    
    

    
 
                
                <li><a href="/create">Create</a></li>
                
                <li><a href="https://sourceforge.net/articles/category/sourceforge-podcast/">SourceForge Podcast</a></li>
                
                    <li><a href="https://sourceforge.net/p/forge/documentation/Docs%20Home/">Site Documentation</a></li>
                
                <li><a href="/user/newsletters">Subscribe to our Newsletter</a></li>
                <li><a href="/support">Support Request</a></li>
            </ul>
        </li>
    </ul>
</div>

        <div class="off-canvas-content" data-off-canvas-content>
            
                


<script>
    SF.linkout_icon = '<svg  data-name="sf-linkout-icon" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 300" style="enable-background:new 0 0 300 300;" xml:space="preserve"><polygon class="st0" points="243.2,243.2 56.8,243.2 56.8,56.8 123,56.8 123,9 9,9 9,291 291,291 291,179.4 243.2,179 "/><polygon class="st0" points="128.5,213 155,186.5 176,165.5 206.7,196.3 235.5,132.5 248.9,102.6 290.6,9.8 291,9 290.6,9.2 197.4,51.1 169.1,63.8 103.7,93.3 137,126.5 115.9,147.5 89.5,174 "/></svg>';
</script>


    <section class="sandiego l-header-nav-top show-for-large">
        <div class="row">
            <a href="/" title="Home" class="sf-logo">
                
    
    <img src="//a.fsdn.com/con/images/sandiego/sf-logo-full.svg"  alt="SourceForge logo" class="sf-logo-full"/>
            </a>
            <nav class="links">
                
                    <a href="/user/newsletters" title="Subscribe to our newsletter"><span class="newsletter-icon">


<svg  data-name="mmSF_11mail" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 42 42" enable-background="new 0 0 42 42" xml:space="preserve"><path fill="#FFFFFF" d="M0,6v30h42V6H0z M24.2,21.2c-0.8,0.8-2.3,2-3.2,2c-0.9,0-2.4-1.2-3.2-2L5.8,9.7h30.3L24.2,21.2z M13.7,21l-9.9,9.4V11.6L13.7,21z M15.7,23L15.7,23c0.5,0.5,2.9,2.9,5.3,2.9c2.4,0,4.8-2.4,5.2-2.8l0.1-0.1l9.8,9.4H5.8L15.7,23z M28.3,21l9.9-9.5v18.9L28.3,21z"/></svg></span></a>
                
                <a href="/software/vendors/" title="For Vendors">For Vendors</a>
                
                
                    <a href="/support" title="Help">Help</a>
                    <a href="/create/" title="Create">Create</a>
                

                
                <a href="/user/registration" title="Join" >Join</a>
                <a href="https://sourceforge.net/auth/" title="Login">Login</a>
                
            </nav>
        </div>
    </section>

<div class="l-header-nav sticky sandiego l-header-nav-collapse">

    <section class="sandiego l-header-nav-top hide-for-large">
        <div class="row">
            <a href="/" title="Home" class="sf-logo">
                
    
    <img src="//a.fsdn.com/con/images/sandiego/sf-logo-full.svg"  alt="SourceForge logo" class="sf-logo-full"/>
            </a>
            <div class="title-bar-right">
                <button type="button" aria-label="Toggle Main Menu" class="menu-icon" data-toggle="offCanvas"></button>
            </div>
        </div>
    </section>
    <section class="sandiego l-header-nav-bottom">
        <nav class="row">
            <a href="/" title="Home" class="sf-logo">
                
    
    <img src="//a.fsdn.com/con/images/sandiego/sf-logo-full.svg"  alt="SourceForge logo" class="sf-logo-full"/>
            </a>
            <div class="links">
                
    
        <div class="nav-dropdown">
            <a href="/software/">Business Software</a>
            
        </div>
        <div class="nav-dropdown">
            <a href="/directory/" title="Browse">Open Source Software</a>
            
        </div>
        <div class="nav-dropdown">
            <a href="https://sourceforge.net/articles/category/sourceforge-podcast/">SourceForge Podcast</a>
        </div>
        <div class="nav-dropdown">
            <a>Resources</a>
            <ul class="nav-dropdown-menu">
                  <li><a href="/articles/">Articles</a></li>
                  
                  <li><a href="/software/case-studies/">Case Studies</a></li>
                  
                  <li><a href="/blog/">Blog</a></li>
            </ul>
        </div>
    

                <div class="dev-menu-when-stuck">
                    Menu
                    <ul class="dev-menu-dropdown header-nav-menulist">
                        <li><a href="/support">Help</a></li>
                        <li><a href="/create">Create</a></li>
                        <li><a href="/user/registration/" title="Join" >Join</a></li>
                        <li><a href="https://sourceforge.net/auth/" title="Login">Login</a></li>
                    </ul>
                </div>
                <div class="search-toggle-when-stuck">
                    <a class="search-toggle">
                        


<svg  data-name="search" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1216 832q0-185-131.5-316.5t-316.5-131.5-316.5 131.5-131.5 316.5 131.5 316.5 316.5 131.5 316.5-131.5 131.5-316.5zm512 832q0 52-38 90t-90 38q-54 0-90-38l-343-342q-179 124-399 124-143 0-273.5-55.5t-225-150-150-225-55.5-273.5 55.5-273.5 150-225 225-150 273.5-55.5 273.5 55.5 225 150 150 225 55.5 273.5q0 220-124 399l343 343q37 37 37 90z"/></svg>
                    </a>
                </div>
            </div>

            <div class="search">
                
                
    
    
        <div class="main-nav-link">
            
            
            <a href="https://hubs.la/Q038lz530" rel="nofollow" target="_blank" id="main-nav-badge-link" data-label="socradarmay">
                <img src="//a.fsdn.com/con/assets/maxnav/sourceforge/report-32-f587c5f2.png"  srcset="//a.fsdn.com/con/assets/maxnav/sourceforge/report-64-f5896c5e.png 2x"  alt="" id="main-nav-image"/>
            </a>
        </div>
    


                
                    
                
                
                
                
    <form method="get" action="/directory/" class="m-search-form">
    
    <div class="typeahead__container">
      <div class="typeahead__field">
        <div class="typeahead__query">
        
        <input type="text" placeholder="Search for software or solutions" autocomplete="off" name="q" >
        
        </div>
        
        <label >
            <input type="submit" class="bt" value="">
            


<svg  data-name="search" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1216 832q0-185-131.5-316.5t-316.5-131.5-316.5 131.5-131.5 316.5 131.5 316.5 316.5 131.5 316.5-131.5 131.5-316.5zm512 832q0 52-38 90t-90 38q-54 0-90-38l-343-342q-179 124-399 124-143 0-273.5-55.5t-225-150-150-225-55.5-273.5 55.5-273.5 150-225 225-150 273.5-55.5 273.5 55.5 225 150 150 225 55.5 273.5q0 220-124 399l343 343q37 37 37 90z"/></svg>
        </label>
      </div>
    </div>
    
    </form>
                
            </div>
        </nav>
        
    </section>
    <div id="banner-sterling" class="sterling">
        
        
        


    


<div id="SF_Temp5_728x90_A_wrapped" data-id="div-gpt-ad-*************-0" class="draper   
visibility_rules
 v_970_billboard  v_728_leaderboard "> </div><script>
/*global googletag */
if (SF.initial_breakpoints_visible.leaderboard) {
(function(){
    
    var el = document.getElementById('SF_Temp5_728x90_A_wrapped');
    var newNode = document.createElement('div');
    newNode.id = 'div-gpt-ad-*************-0';
    el.appendChild(newNode);
}());

gptadRenderers['SF_Temp5_728x90_A'] = function(){  // jshint ignore:line
    
        if (!SF.adblock) {
            
                
            
            $('#div-gpt-ad-*************-0').parents('.draper').css("min-height", 90 + 13 + 12); // for height of .lbl-ad and padding
        }
    
    googletag.cmd.push(function() {
        googletag.display('div-gpt-ad-*************-0');
    });
};
gptadRenderers['SF_Temp5_728x90_A']();  // jshint ignore:line
}
</script>
        


    


<div id="SF_Mobile_320x50_A_wrapped" data-id="div-gpt-ad-*************-0" class="draper   
"> </div><script>
/*global googletag */
if (!SF.initial_breakpoints_visible.leaderboard) {
(function(){
    
    var el = document.getElementById('SF_Mobile_320x50_A_wrapped');
    var newNode = document.createElement('div');
    newNode.id = 'div-gpt-ad-*************-0';
    el.appendChild(newNode);
}());

gptadRenderers['SF_Mobile_320x50_A'] = function(){  // jshint ignore:line
    
        if (!SF.adblock) {
            
                
            
            $('#div-gpt-ad-*************-0').parents('.draper').css("min-height", 50 + 13 + 12); // for height of .lbl-ad and padding
        }
    
    googletag.cmd.push(function() {
        googletag.display('div-gpt-ad-*************-0');
    });
};
gptadRenderers['SF_Mobile_320x50_A']();  // jshint ignore:line
}
</script>
    </div>
</div>
            

            
                

                
<div class="full-width-masthead project-masthead" id="downloading" >
    
    
<div class="backdrop" style="box-sizing: content-box; padding-bottom: 131px"></div>

    <div class="content">
        
    <nav id="breadcrumbs" class="breadcrumbs rtl">
        <ul itemscope itemtype="http://schema.org/BreadcrumbList">
            
            <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
            <a itemprop="item" href="/"><span itemprop="name">Home</span></a>
            <meta itemprop="position" content="1" />
            </li>
            
            <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
                <a itemprop="item" href="/directory/"><span itemprop="name">Open Source Software</span></a>
                <meta itemprop="position" content="2" />
            </li>
            <li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
                <a itemprop="item" href="/directory/database/"><span itemprop="name">Database</span></a>
                <meta itemprop="position" content="3" />
            </li><li itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem">
                <a itemprop="item" href="/directory/database-engines-servers/"><span itemprop="name">Database Engines/Servers</span></a>
                <meta itemprop="position" content="4" />
            </li><li class="project" itemprop="itemListElement" itemscope itemtype="http://schema.org/ListItem"><span itemprop="name">XAMPP</span><meta itemprop="position" content="5" />
            </li>
            
        </ul>
    </nav>


        <div id="starting">
            
<div class="overview">
    


<div class="project-icon   " >
    
    
    <img itemprop="image" alt="XAMPP" title="XAMPP" 
src="//a.fsdn.com/allura/p/xampp/icon?1599843055?&amp;w=90"
    srcset="//a.fsdn.com/allura/p/xampp/icon?1599843055?&amp;w=135 1.5x
        ,
            //a.fsdn.com/allura/p/xampp/icon?1599843055?&amp;w=180 2x"/></div>


    
    <div class="title "> 

        
        <h1  itemprop="name"><a href="/projects/xampp/" itemprop="url">XAMPP</a>
        </h1>
        
        <h2 class="as-h3 summary">
            An easy to install Apache distribution containing MySQL, PHP, and Perl
        </h2>
         
          
        
            
            
            <div class="as-h3 brought-by">
                
                Brought to you by:
                
                    <a href="/u/beltranrueda/profile/">beltranrueda</a>,
                    
                
                    <a href="/u/bitnami/profile/">bitnami</a>,
                    
                
                    <a href="/u/koswalds/profile/">koswalds</a>,
                    
                
                    <a href="/u/kvogelgesang/profile/">kvogelgesang</a>
                    
                
            </div>
            
        

        

        
    </div>


    

</div>

        </div>
        <div class="buttons clearfix row">
            <div id="buttons-group">
                <div id="download-updates" class="column large-6">
                    
                    <div id="get-updates">
    <img id="psp_newsletter_subscribe-icon" src="//a.fsdn.com/con/images/sandiego/sf_email_icon.svg"  alt="Email in envelope"  />
    <div id="download-group">
        <h4>Get an email when there's a new version of XAMPP</h4>
        <div id="get-updates-form">
            <form>
                <input type="email" name="email" placeholder="Enter your email address" value="">
                <a id="get-updates-button" class="button blue" data-open="psp-newsletter-modal">Next</a>
            </form>
        </div>
    </div>
    </div>
    <div class="psp_newsletter_subscribe reveal" data-reveal id="psp-newsletter-modal" data-v-offset="0" data-ajax-url="/projects/xampp/get_updates?source=DLP">

    </div>
    <script>

    $('#get-updates-form input[type="email"]').on('focus', function(){
        if(SF.downloader) {
            SF.downloader.cancelRedirect();
        }
        SF.noRedirect =  true;
    });
    $('#get-updates-button').click(function(){
        if(SF.downloader) {
            SF.downloader.cancelRedirect();
        }
        SF.noRedirect =  true;
    });
    $("#get-updates-form form").on("keydown", function(event) {
        if(event.which === 13) {
            if (SF.downloader) {
                SF.downloader.cancelRedirect();
            }
            SF.noRedirect = true;
            $('#get-updates-button').trigger('click');
        }
    });

    </script>
                    
                </div>
                <div id="remaining-buttons" class="column large-6 xlarge-5">
                    <div>
                        <p id="downloader" class="">
        Your download will start shortly...
</p>



<div class="radial-progress">
    <div class="circle">
        <div class="mask full">
            <div class="fill"></div>
        </div>
        <div class="mask half">
            <div class="fill"></div>
            <div class="fill fix"></div>
        </div>
    </div>
    <div class="inset"></div>
</div>



<script id="redirect-template" type="text/x-handlebars-template">
Learn more: check out <a href="/projects/xampp/postdownload">screenshots, reviews, and more</a>. We'll take you there in a few moments.
</script>
                    </div>


                    <div class="large-12">
                         
                        


                        




                        <a id="share-project-button" class="button default" href="#">Share This</a>
                        

<div class="social-sharing-buttons invisible hide">
    
    
    
    <a rel=nofollow class="social-media-icon twitter" href="https://twitter.com/share?url=https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download&amp;text=Download%20XAMPP%20on%20SourceForge%20for%20free%21%20An%20easy%20to%20install%20Apache%20distribution%20containing%20MySQL%2C%20PHP%2C%20and%20Perl" title="Share XAMPP on SourceForge on Twitter">


<svg  data-name="twitter" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1684 408q-67 98-162 167 1 14 1 42 0 130-38 259.5t-115.5 248.5-184.5 210.5-258 146-323 54.5q-271 0-496-145 35 4 78 4 225 0 401-138-105-2-188-64.5t-114-159.5q33 5 61 5 43 0 85-11-112-23-185.5-111.5t-73.5-205.5v-4q68 38 146 41-66-44-105-115t-39-154q0-88 44-163 121 149 294.5 238.5t371.5 99.5q-8-38-8-74 0-134 94.5-228.5t228.5-94.5q140 0 236 102 109-21 205-78-37 115-142 178 93-10 186-50z"/></svg></a>
    <a rel=nofollow class="social-media-icon facebook" href="https://www.facebook.com/sharer/sharer.php?u=https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download&title=XAMPP%20on%20SourceForge" title="Share XAMPP on SourceForge on facebook">


<svg  data-name="facebook" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1343 12v264h-157q-86 0-116 36t-30 108v189h293l-39 296h-254v759h-306v-759h-255v-296h255v-218q0-***********.5t277-102.5q147 0 228 12z"/></svg></a>
    <a rel=nofollow class="social-media-icon linkedin" href="https://www.linkedin.com/shareArticle?mini=true&url=https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download&title=XAMPP%20on%20SourceForge&source=SourceForge.net" title="Share XAMPP on SourceForge on LinkedIn">


<svg  data-name="linkedin" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M477 625v991h-330v-991h330zm21-306q1 73-50.5 122t-135.5 49h-2q-82 0-132-49t-50-122q0-74 51.5-122.5t134.5-48.5 133 48.5 51 122.5zm1166 729v568h-329v-530q0-105-40.5-164.5t-126.5-59.5q-63 0-105.5 34.5t-63.5 85.5q-11 30-11 81v553h-329q2-399 2-647t-1-296l-1-48h329v144h-2q20-32 41-56t56.5-52 87-43.5 114.5-15.5q171 0 275 113.5t104 332.5z"/></svg></a>
</div>


                        
                            
    
    <div class="reveal" id="mirror-modal">
        <div class="modal-content row">
            <div class="column small-12 row modal-header">
            </div>

            <div id="mirror-drawer"></div>

            <button class="close-button" data-close aria-label="Close modal" type="button">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    </div>
    <a class="button default mirror " id="btn-problems-downloading" data-release-url="https://downloads.sourceforge.net/project/xampp/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe?ts=gAAAAABoIDv5MR60z0q7o6PxJqM089wR6GdIRZlxCA125Bt0zJBXaLDR7JR5uscOcvzHLzIk59HnWLCJf3k7CCIZBP5r3S3RRg%3D%3D&amp;use_mirror=yer&amp;r=" rel="nofollow" href="/settings/mirror_choices?projectname=xampp&amp;filename=XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe&amp;selected=yer">Problems Downloading?</a>
                        
                    </div>
                </div>
            </div>
        </div>

        <div class="file-info">
            <div class="file-name">
                    xampp-windows-x64-8.2.12-0-VS16-installer.exe
                
            </div>

            
            
            <div id="scanned_by">
                <span>Scanned for malware ✔</span>
            </div>
        </div>

        
    </div>
    <div id="mirror">
        <div class="mirror-title">Mirror Provided by</div><a id="provider_top" href="https://yer.az/" title="YeR" target="_blank"><img class="mirror-logo" alt="YeR"
                  src="https://a.fsdn.com/mirrorimages/yer-xlarge.png" width="230" height="126" 
                    srcset="https://a.fsdn.com/mirrorimages/yer-hidpi.png 2x" /></a>
          <div class="mirror-footer"><a href="https://yer.az/" title="YeR" rel=nofollow target="_blank">Learn more about YeR</a></div>
    </div>
</div>

                <div class="l-two-column-page">
                    <div class="l-content-column l-has-sidebar">
                        
<div class="project-body">
    <article class="main-content">

        
        

        
    

<script>
    if (!SF.wireOutboundZoneTrackingComplete) {  
        $(SF.wireOutboundZoneTracking);
        $('body').append('<iframe src="https://c.sf-syn.com/conversion_outbound_tracker/sf" id="frame-zone-outbound" style="display: none;"></iframe>');
        SF.wireOutboundZoneTrackingComplete = true;
    }
</script>


     
        
        <section id="nels" class="small-12 columns">
            
            
    
    
    
    
     <div class="as-header">
        <div class="as-h2">
            You Might Also Like</div>
    </div>
    
    
        
        

    
    
        
    

    <div class="nel standard trunc-eligible  "
        data-id="19646" data-cid="15647">

        


<span id="f598062b-386c-4c88-812e-380d049f6cee"></span>
<script>
    /* globals bizx */
    bizx.cmp.trackingPixel('publisher', ['storage', 'measurement'], '/directory/tp3/?b=81094&amp;c=15647&amp;z=78493&amp;cb=5b31a51261', "ADVANCE Your Career: Unlimited Access to 10,000+ Courses", 'f598062b-386c-4c88-812e-380d049f6cee');
</script>


        
        <div class="application-image thumbnail"  data-dest="https://sourceforge.net/software/link?oaparams=2__bannerid=81094__zoneid=78493__cb=5b31a51261__oadest=https%3A%2F%2Fimp.i384100.net%2Fc%2F160060%2F1320993%2F14726%3FsubId1%3Dcourseraplus" data-newtab="true" data-target="_blank" rel="nofollow">
            <img class="main-image" src="//a.fsdn.com/con/app/nel_img/19646" alt="ADVANCE Your Career: Unlimited Access to 10,000+ Courses Icon">
            
        </div>
        <div class="wrapper">
            <div class="heading">
                <div class="heading-main">
                    
                    <span data-dest="https://sourceforge.net/software/link?oaparams=2__bannerid=81094__zoneid=78493__cb=5b31a51261__oadest=https%3A%2F%2Fimp.i384100.net%2Fc%2F160060%2F1320993%2F14726%3FsubId1%3Dcourseraplus" data-newtab="true" data-target="_blank" rel="nofollow" title="Find out more about ADVANCE Your Career: Unlimited Access to 10,000+ Courses">ADVANCE Your Career: Unlimited Access to 10,000+ Courses</span>

                    
                    <p class="teaser">Elevate your career with Coursera Plus. Access 10,000+ courses and certifications to help you grow professionally.</p>
                </div>
            </div>

            
            

            <div class="tiles">
                <div class="tile">
                    
                    <div class="description ">
                        <div class="description-inner">
                            With Coursera Plus, you gain unlimited access to 10,000+ courses, professional certificates, and skill-building programs from top universities and companies. Whether you’re aiming for a promotion, switching careers, or acquiring new skills, Coursera Plus offers the tools you need for career success. From business and technology to leadership and data science, get certified and boost your credentials at your own pace. Join today and start building the career you’ve always dreamed of.
                        </div>
                    </div>
                </div>

                
            </div>

        </div>

        
        <div class="download standard">
            
            

            

            
        
            
        
        <div class="button green wide sfdl sfdl-lite" data-target="_blank" data-newtab="true" data-dest="https://sourceforge.net/software/link?oaparams=2__bannerid=81094__zoneid=78493__cb=5b31a51261__oadest=https%3A%2F%2Fimp.i384100.net%2Fc%2F160060%2F1320993%2F14726%3FsubId1%3Dcourseraplus"
                >Start for Free</div>
        </div>

        
    </div>


    
        
        

    
    
        
    

    <div class="nel standard trunc-eligible  "
        data-id="3805" data-cid="4547">

        


<span id="fa706f72-d659-4fca-a0aa-d4c0c632e775"></span>
<script>
    /* globals bizx */
    bizx.cmp.trackingPixel('publisher', ['storage', 'measurement'], '/directory/tp3/?b=25201&amp;c=4547&amp;z=22575&amp;cb=da61206ce4', "Tool Tracking Made Simple", 'fa706f72-d659-4fca-a0aa-d4c0c632e775');
</script>


        
        <div class="application-image thumbnail"  data-dest="https://sourceforge.net/software/link?oaparams=2__bannerid=25201__zoneid=22575__cb=da61206ce4__oadest=https%3A%2F%2Fwww.sharemytoolbox.com%2F%3Futm_source%3Dsourceforge%26utm_medium%3Dpaid%26utm_campaign%3Dsourceforgelisting%26utm_id%3Dfixed0223" data-newtab="true" data-target="_blank" rel="nofollow">
            <img class="main-image" src="//a.fsdn.com/con/app/nel_img/3805" alt="Tool Tracking Made Simple Icon">
            
        </div>
        <div class="wrapper">
            <div class="heading">
                <div class="heading-main">
                    
                    <span data-dest="https://sourceforge.net/software/link?oaparams=2__bannerid=25201__zoneid=22575__cb=da61206ce4__oadest=https%3A%2F%2Fwww.sharemytoolbox.com%2F%3Futm_source%3Dsourceforge%26utm_medium%3Dpaid%26utm_campaign%3Dsourceforgelisting%26utm_id%3Dfixed0223" data-newtab="true" data-target="_blank" rel="nofollow" title="Find out more about Tool Tracking Made Simple">Tool Tracking Made Simple</span>

                    
                    <p class="teaser">Use Phones to Track Tools - A simple app to turn your phone into a tool tracker.</p>
                </div>
            </div>

            
            

            <div class="tiles">
                <div class="tile">
                    
                    <div class="description ">
                        <div class="description-inner">
                            ShareMyToolbox is a tool tracking solution that enables companies to track individuals who are responsible for tools and small equipment. Mobile users are able to search the company tool inventory, request tools and accept tool assignments with Apple or Android devices such as phones or tablets. Built for contractors, the system was designed to be extremely easy to use.
                        </div>
                    </div>
                </div>

                
            </div>

        </div>

        
        <div class="download standard">
            
            

            

            
        
            
        
        <div class="button green wide sfdl sfdl-lite" data-target="_blank" data-newtab="true" data-dest="https://sourceforge.net/software/link?oaparams=2__bannerid=25201__zoneid=22575__cb=da61206ce4__oadest=https%3A%2F%2Fwww.sharemytoolbox.com%2F%3Futm_source%3Dsourceforge%26utm_medium%3Dpaid%26utm_campaign%3Dsourceforgelisting%26utm_id%3Dfixed0223"
                
                    data-zone="21887" data-slug="ShareMyToolbox"
                >Learn More</div>
        </div>

        
    </div>


    
        
            


    


<div id="SF_Temp5_GEL_C_wrapped" data-id="div-gpt-ad-1715705113227-0" class="draper   
"> </div><script>
/*global googletag */
if (true) {
(function(){
    
    var el = document.getElementById('SF_Temp5_GEL_C_wrapped');
    var newNode = document.createElement('div');
    newNode.id = 'div-gpt-ad-1715705113227-0';
    el.appendChild(newNode);
}());

gptadRenderers['SF_Temp5_GEL_C'] = function(){  // jshint ignore:line
    
        if (!SF.adblock) {
            
                
            
            $('#div-gpt-ad-1715705113227-0').parents('.draper').css("min-height", 185 + 13 + 12); // for height of .lbl-ad and padding
        }
    
    googletag.cmd.push(function() {
        googletag.display('div-gpt-ad-1715705113227-0');
    });
};
gptadRenderers['SF_Temp5_GEL_C']();  // jshint ignore:line
}
</script>
        
        

    
    
        
    

    <div class="nel standard trunc-eligible  nel-hidden"
        data-id="19651" data-cid="15653">

        


<span id="1e2514f1-9497-4030-96fe-296b10a67582"></span>
<script>
    /* globals bizx */
    bizx.cmp.trackingPixel('publisher', ['storage', 'measurement'], '/directory/tp3/?b=85061&amp;c=15653&amp;z=82460&amp;cb=eeeb7b3b5f', "Learn Generative AI and LLMs and Become an Expert TODAY", '1e2514f1-9497-4030-96fe-296b10a67582');
</script>


        
        <div class="application-image thumbnail"  data-dest="https://sourceforge.net/software/link?oaparams=2__bannerid=85061__zoneid=82460__cb=eeeb7b3b5f__oadest=https%3A%2F%2Fimp.i384100.net%2Fc%2F160060%2F2807178%2F14726" data-newtab="true" data-target="_blank" rel="nofollow">
            <img class="main-image" src="//a.fsdn.com/con/app/nel_img/19651" alt="Learn Generative AI and LLMs and Become an Expert TODAY Icon">
            
        </div>
        <div class="wrapper">
            <div class="heading">
                <div class="heading-main">
                    
                    <span data-dest="https://sourceforge.net/software/link?oaparams=2__bannerid=85061__zoneid=82460__cb=eeeb7b3b5f__oadest=https%3A%2F%2Fimp.i384100.net%2Fc%2F160060%2F2807178%2F14726" data-newtab="true" data-target="_blank" rel="nofollow" title="Find out more about Learn Generative AI and LLMs and Become an Expert TODAY">Learn Generative AI and LLMs and Become an Expert TODAY</span>

                    
                    <p class="teaser">Advance your career and learn how to build and deploy generative AI models with this expert-led course. Start mastering AI today!</p>
                </div>
            </div>

            
            

            <div class="tiles">
                <div class="tile">
                    
                    <div class="description ">
                        <div class="description-inner">
                            In today’s world, you MUST know how to use AI. Dive into the world of generative AI with the “Generative AI with LLMs” course on Coursera. Learn from top experts how to build, train, and deploy Large Language Models (LLMs) for practical applications. Whether you’re new to AI or looking to expand your expertise, this course offers in-depth knowledge and hands-on experience. Unlock the potential of cutting-edge technology to enhance your career.
                        </div>
                    </div>
                </div>

                
            </div>

        </div>

        
        <div class="download standard">
            
            

            

            
        
            
        
        <div class="button green wide sfdl sfdl-lite" data-target="_blank" data-newtab="true" data-dest="https://sourceforge.net/software/link?oaparams=2__bannerid=85061__zoneid=82460__cb=eeeb7b3b5f__oadest=https%3A%2F%2Fimp.i384100.net%2Fc%2F160060%2F2807178%2F14726"
                >Enroll Free Today</div>
        </div>

        
    </div>


    
        
        

    
    
        
    

    <div class="nel standard trunc-eligible  "
        data-id="18543" data-cid="14469">

        


<span id="1dcac195-b5c3-4ab4-a930-3759de9558c7"></span>
<script>
    /* globals bizx */
    bizx.cmp.trackingPixel('publisher', ['storage', 'measurement'], '/directory/tp3/?b=75019&amp;c=14469&amp;z=72411&amp;cb=b0c2bd6c20', "The Power of a Comprehensive B2B Data Platform: People Data Labs", '1dcac195-b5c3-4ab4-a930-3759de9558c7');
</script>


        
        <div class="application-image thumbnail"  data-dest="https://sourceforge.net/software/link?oaparams=2__bannerid=75019__zoneid=72411__cb=b0c2bd6c20__oadest=https%3A%2F%2Fwww.youtube.com%2Fwatch%3Fv%3DldFM8_xkSo8" data-newtab="true" data-target="_blank" rel="nofollow">
            <img class="main-image" src="//a.fsdn.com/con/app/nel_img/18543" alt="The Power of a Comprehensive B2B Data Platform: People Data Labs Icon">
            
        </div>
        <div class="wrapper">
            <div class="heading">
                <div class="heading-main">
                    
                    <span data-dest="https://sourceforge.net/software/link?oaparams=2__bannerid=75019__zoneid=72411__cb=b0c2bd6c20__oadest=https%3A%2F%2Fwww.youtube.com%2Fwatch%3Fv%3DldFM8_xkSo8" data-newtab="true" data-target="_blank" rel="nofollow" title="Find out more about The Power of a Comprehensive B2B Data Platform: People Data Labs">The Power of a Comprehensive B2B Data Platform: People Data Labs</span>

                    
                    <p class="teaser">Epicor | SourceForge Podcast, ep. #8</p>
                </div>
            </div>

            
            

            <div class="tiles">
                <div class="tile">
                    
                    <div class="description ">
                        <div class="description-inner">
                            Ben Eisenberg, VP of Innovation at People Data Labs, discusses the company's focus on building high-quality data and being developer-friendly. People Data Labs builds people and company data for developers, engineers, and data scientists. People Data Labs handles the heavy-lifting of data collection, so you can build innovative and compliant data solutions at scale. Ben highlights the importance of data privacy and compliance, as well as the need for different methods of consuming data. Eisenberg also shares upcoming projects, such as improving data updates and releasing a job posting data set. He emphasizes the role of innovation in product development and the challenges of evaluating data quality. Finally, he discusses the company's approach to security and the importance of internal and external collaboration.
                        </div>
                    </div>
                </div>

                
            </div>

        </div>

        
        <div class="download standard">
            
            

            

            
        
            
        
        <div class="button green wide sfdl sfdl-lite" data-target="_blank" data-newtab="true" data-dest="https://sourceforge.net/software/link?oaparams=2__bannerid=75019__zoneid=72411__cb=b0c2bd6c20__oadest=https%3A%2F%2Fwww.youtube.com%2Fwatch%3Fv%3DldFM8_xkSo8"
                >Watch Now</div>
        </div>

        
    </div>


    
        
            


    


<div id="SF_Temp5_GEL_E_wrapped" data-id="div-gpt-ad-1715705368750-0" class="draper   
"> </div><script>
/*global googletag */
if (true) {
(function(){
    
    var el = document.getElementById('SF_Temp5_GEL_E_wrapped');
    var newNode = document.createElement('div');
    newNode.id = 'div-gpt-ad-1715705368750-0';
    el.appendChild(newNode);
}());

gptadRenderers['SF_Temp5_GEL_E'] = function(){  // jshint ignore:line
    
        if (!SF.adblock) {
            
                
            
            $('#div-gpt-ad-1715705368750-0').parents('.draper').css("min-height", 185 + 13 + 12); // for height of .lbl-ad and padding
        }
    
    googletag.cmd.push(function() {
        googletag.display('div-gpt-ad-1715705368750-0');
    });
};
gptadRenderers['SF_Temp5_GEL_E']();  // jshint ignore:line
}
</script>
        
        

    
    
        
    

    <div class="nel standard trunc-eligible  nel-hidden"
        data-id="18525" data-cid="14395">

        


<span id="3c5d8cf3-b7d3-4431-9630-62073d6cb5ee"></span>
<script>
    /* globals bizx */
    bizx.cmp.trackingPixel('publisher', ['storage', 'measurement'], '/directory/tp3/?b=74787&amp;c=14395&amp;z=72179&amp;cb=d1d5141a3b', "Smart \u0026amp; Connected ERP for the \"Make, Move, \u0026amp; Sell\" Economy", '3c5d8cf3-b7d3-4431-9630-62073d6cb5ee');
</script>


        
        <div class="application-image thumbnail"  data-dest="https://sourceforge.net/software/link?oaparams=2__bannerid=74787__zoneid=72179__cb=d1d5141a3b__oadest=https%3A%2F%2Fwww.youtube.com%2Fwatch%3Fv%3DbdpEa0sWytY" data-newtab="true" data-target="_blank" rel="nofollow">
            <img class="main-image" src="//a.fsdn.com/con/app/nel_img/18525" alt="Smart &amp; Connected ERP for the "Make, Move, &amp; Sell" Economy Icon">
            
        </div>
        <div class="wrapper">
            <div class="heading">
                <div class="heading-main">
                    
                    <span data-dest="https://sourceforge.net/software/link?oaparams=2__bannerid=74787__zoneid=72179__cb=d1d5141a3b__oadest=https%3A%2F%2Fwww.youtube.com%2Fwatch%3Fv%3DbdpEa0sWytY" data-newtab="true" data-target="_blank" rel="nofollow" title="Find out more about Smart &amp; Connected ERP for the "Make, Move, &amp; Sell" Economy">Smart &amp; Connected ERP for the "Make, Move, &amp; Sell" Economy</span>

                    
                    <p class="teaser">Epicor | SourceForge Podcast, ep. #7</p>
                </div>
            </div>

            
            

            <div class="tiles">
                <div class="tile">
                    
                    <div class="description ">
                        <div class="description-inner">
                            In this conversation, Kerrie Jordan, the Group Vice President of Product Management at Epicor, discusses the software solutions provided by Epicor for businesses in the make, move, and sell economy.
                        </div>
                    </div>
                </div>

                
            </div>

        </div>

        
        <div class="download standard">
            
            

            

            
        
            
        
        <div class="button green wide sfdl sfdl-lite" data-target="_blank" data-newtab="true" data-dest="https://sourceforge.net/software/link?oaparams=2__bannerid=74787__zoneid=72179__cb=d1d5141a3b__oadest=https%3A%2F%2Fwww.youtube.com%2Fwatch%3Fv%3DbdpEa0sWytY"
                >Watch Now</div>
        </div>

        
    </div>


    
        </section>
        
    


        
        <div class="small-12 columns hr-bottom">
            <aside class="content-supplement">
                <div class="as-header">
                    <h3>XAMPP Overview</h3>
                </div>

                <div class="project-container">
                    
                    <p id="summary">An easy to install Apache distribution containing MySQL, PHP, and Perl</p>
                    
                    <p id="description">XAMPP is a very easy to install Apache Distribution for Linux, Solaris, Windows, and Mac OS X. The package includes the Apache web server, MySQL, PHP, Perl, a FTP server and phpMyAdmin.</p>
                </div>
            </aside>
        </div>
        


        


        
        <div class="small-12 columns hr-bottom">
            
<section id="project-additional-trove" class="content-supplement">
    <div class="as-header">
        <h2>Additional Details for XAMPP</h2>
    </div>
    <div class="project-container">
        

        <section class="project-info">
    <div class="as-header">
        <h3>Operating Systems</h3>
    </div>
    <section class="content"><a href="/directory/solaris/">Solaris</a>, <a href="/directory/linux/">Linux</a>, <a href="/directory/mac/">Mac</a>, <a href="/directory/windows/">Windows</a></section>
</section>


        <section class="project-info">
    <div class="as-header">
        <h3>Languages</h3>
    </div>
    <section class="content">English, German</section>
</section>


        <section class="project-info">
    <div class="as-header">
        <h3>Intended Audience</h3>
    </div>
    <section class="content">Information Technology, Education, System Administrators, Developers</section>
</section>


        <section class="project-info">
    <div class="as-header">
        <h3>User Interface</h3>
    </div>
    <section class="content">Win32 (MS Windows), Web-based, Non-interactive (Daemon)</section>
</section>


        <section class="project-info">
    <div class="as-header">
        <h3>Programming Language</h3>
    </div>
    <section class="content"><a href="/directory/perl/">Perl</a>, <a href="/directory/php/">PHP</a></section>
</section>


        <section class="project-info">
            <div class="as-header">
                <h4>Registered</h4>
            </div>
            <section class="content">
                2002-09-04
            </section>
        </section>

        
        <section class="project-info">
            <div class="as-header">
                <h3>Last Updated</h3>
            </div>
            <section class="content">
                <time itemprop="dateModified" class="dateUpdated" datetime="2023-11-25">2023-11-25</time>
            </section>
        </section>
        
        <section class="project-info">
            <div class="as-header">
                <h3>Categories</h3>
            </div><a href="/directory/database-engines-servers/" itemprop="applicationCategory">Database Engines/Servers</a>, <a href="/directory/site-management/" itemprop="applicationCategory">Site Management</a>, <a href="/directory/http-servers/" itemprop="applicationCategory">HTTP Servers</a>, <a href="/directory/internet/" itemprop="applicationCategory">Internet</a>, <a href="/directory/port-scanners/" itemprop="applicationCategory">Port Scanners</a></section>
        
        <section class="project-info">
            <div class="as-header">
                <h3>Maintainers</h3>
            </div><a href="https://sourceforge.net/u/beltranrueda/"><span>beltranrueda</span></a>, <a href="https://sourceforge.net/u/bitnami/"><span>bitnami</span></a>, <a href="https://sourceforge.net/u/koswalds/"><span>koswalds</span></a>, <a href="https://sourceforge.net/u/kvogelgesang/"><span>kvogelgesang</span></a>
        </section>
    </div>
</section>

        </div>
        
        
<aside class="m-wide-widget m-wide-projects-widget ">
    <div class="as-header">
        
        Recommended Projects
    </div>
    <div class="body">
        <ul>
            
            




<li>
    
        


<div class="project-icon   " >
    
    
    <img alt="Wamp - WampServer" title="Wamp - WampServer" 
src="//a.fsdn.com/allura/p/wampserver/icon?1716278993?&amp;w=48"
    srcset="//a.fsdn.com/allura/p/wampserver/icon?1716278993?&amp;w=72 1.5x
        ,
            //a.fsdn.com/allura/p/wampserver/icon?1716278993?&amp;w=96 2x" loading="lazy"/></div>

    
    <div class="project-info ">
        <div class="result-heading-texts">
            <a href="/projects/wampserver/" title="Find out more about Wamp - WampServer">
                
                <strong>Wamp - WampServer</strong>
            </a>
            
                <a href="/projects/wampserver/" title="Find out more about Wamp - WampServer" class="summary-inline">A Windows Web development environment for Apache, MySQL, PHP databases</a>
            
        </div>
        
        <div class="stats">
            
    
    
    <div class="rating">
        






<div class="m-stars" aria-hidden="true" >


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive half">
        
<svg  data-name="sf_star_yellow_half" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 300" style="enable-background:new 0 0 300 300;" xml:space="preserve"><path class="st1" fill="#CCCCCB" d="M148.8,223.4c-31.1,22.9-62.2,45.8-93.5,68.9c0.1-0.4,0.2-0.7,0.3-0.9c12.7-35.9,25.3-71.7,38-107.6c0.2-0.6,0.2-1-0.4-1.4c-30.8-21.4-61.6-42.8-92.4-64.2c-0.7-0.5-0.9-1-0.7-1.8c0.4,0,0.8,0,1.1,0c26.2,0.3,52.4,0.7,78.6,1c11.3,0.2,22.7,0.3,34,0.5c0.8,0,1.1-0.2,1.4-1c11.1-36.2,22.3-72.4,33.4-108.5c0.1-0.2,0.1-0.4,0.3-0.8c11.3,37.1,22.6,74,34,110.9c39-0.8,78-1.5,117.2-2.3c-0.3,0.3-0.5,0.4-0.7,0.6c-15.7,11-31.5,22.1-47.2,33.1c-15.5,10.8-30.9,21.7-46.4,32.5c-0.5,0.4-0.6,0.7-0.4,1.3c12.1,35.8,24.3,71.6,36.4,107.4c0.1,0.2,0.1,0.4,0.2,0.8C210.8,269.1,179.8,246.3,148.8,223.4z"/><path class="st0" fill="#ffcc00" d="M148.8,223.4c-31.1,22.9-62.2,45.8-93.5,68.9c0.1-0.4,0.2-0.7,0.3-0.9c12.7-35.9,25.3-71.7,38-107.6c0.2-0.6,0.2-1-0.4-1.4c-30.8-21.4-61.6-42.8-92.4-64.2c-0.7-0.5-0.9-1-0.7-1.8c0.4,0,0.8,0,1.1,0c26.2,0.3,52.4,0.7,78.6,1c11.3,0.2,22.7,0.3,34,0.5c0.8,0,1.1-0.2,1.4-1c11.1-36.2,22.3-72.4,33.4-108.5c0.1-0.2,0.1-0.4,0.3-0.8"/></svg>
    </div>


</div>


        <a href="/projects/wampserver/reviews/" class="count" title="Wamp - WampServer Reviews">
            140 Reviews
        </a>
    </div>
    

    <strong class="">
        <b class="label">Downloads:</b>
        
            25,745 This Week
        
    </strong>

    <strong class="">
        <b class="label">Last Update:</b>
        
            <time class="dateUpdated" datetime="2025-05-08">3 days ago</time>
        
    </strong>

        </div>
        
    </div>
    <div>
        <a href="/projects/wampserver/" title="Find out more about Wamp - WampServer" class="button green hollow see-project">See Project</a>
    </div>
</li>

            
            




<li>
    
        


<div class="project-icon   " >
    
    
    <img alt="AIOHTTP" title="AIOHTTP" 
src="//a.fsdn.com/allura/mirror/aiohttp/icon?e3a3aaf253de2e64fe1c2c8f3ee10a1b7b614c5cb2bb4b66997158052f2bb6cb?&amp;w=48"
    srcset="//a.fsdn.com/allura/mirror/aiohttp/icon?e3a3aaf253de2e64fe1c2c8f3ee10a1b7b614c5cb2bb4b66997158052f2bb6cb?&amp;w=72 1.5x
        ,
            //a.fsdn.com/allura/mirror/aiohttp/icon?e3a3aaf253de2e64fe1c2c8f3ee10a1b7b614c5cb2bb4b66997158052f2bb6cb?&amp;w=96 2x" loading="lazy"/></div>

    
    <div class="project-info ">
        <div class="result-heading-texts">
            <a href="/projects/aiohttp.mirror/" title="Find out more about AIOHTTP">
                
                <strong>AIOHTTP</strong>
            </a>
            
                <a href="/projects/aiohttp.mirror/" title="Find out more about AIOHTTP" class="summary-inline">Asynchronous HTTP client/server framework for asyncio and Python</a>
            
        </div>
        
        <div class="stats">
            
    
    
    <div class="rating">
        






<div class="m-stars" aria-hidden="true" >


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


</div>


        <a href="/projects/aiohttp.mirror/reviews/" class="count" title="AIOHTTP Reviews">
            1 Review
        </a>
    </div>
    

    <strong class="">
        <b class="label">Downloads:</b>
        
            13 This Week
        
    </strong>

    <strong class="">
        <b class="label">Last Update:</b>
        
            <time class="dateUpdated" datetime="2025-04-21">2025-04-21</time>
        
    </strong>

        </div>
        
    </div>
    <div>
        <a href="/projects/aiohttp.mirror/" title="Find out more about AIOHTTP" class="button green hollow see-project">See Project</a>
    </div>
</li>

            
            




<li>
    
        


<div class="project-icon   " >
    
    
    <img alt="Apache OpenOffice" title="Apache OpenOffice" 
src="//a.fsdn.com/allura/mirror/openofficeorg/icon?1703330040?&amp;w=48"
    srcset="//a.fsdn.com/allura/mirror/openofficeorg/icon?1703330040?&amp;w=72 1.5x
        " loading="lazy"/></div>

    
    <div class="project-info ">
        <div class="result-heading-texts">
            <a href="/projects/openofficeorg.mirror/" title="Find out more about Apache OpenOffice">
                
                <strong>Apache OpenOffice</strong>
            </a>
            
                <a href="/projects/openofficeorg.mirror/" title="Find out more about Apache OpenOffice" class="summary-inline">The free and Open Source productivity suite</a>
            
        </div>
        
        <div class="stats">
            
    
    
    <div class="rating">
        






<div class="m-stars" aria-hidden="true" >


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive half">
        
<svg  data-name="sf_star_yellow_half" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 300" style="enable-background:new 0 0 300 300;" xml:space="preserve"><path class="st1" fill="#CCCCCB" d="M148.8,223.4c-31.1,22.9-62.2,45.8-93.5,68.9c0.1-0.4,0.2-0.7,0.3-0.9c12.7-35.9,25.3-71.7,38-107.6c0.2-0.6,0.2-1-0.4-1.4c-30.8-21.4-61.6-42.8-92.4-64.2c-0.7-0.5-0.9-1-0.7-1.8c0.4,0,0.8,0,1.1,0c26.2,0.3,52.4,0.7,78.6,1c11.3,0.2,22.7,0.3,34,0.5c0.8,0,1.1-0.2,1.4-1c11.1-36.2,22.3-72.4,33.4-108.5c0.1-0.2,0.1-0.4,0.3-0.8c11.3,37.1,22.6,74,34,110.9c39-0.8,78-1.5,117.2-2.3c-0.3,0.3-0.5,0.4-0.7,0.6c-15.7,11-31.5,22.1-47.2,33.1c-15.5,10.8-30.9,21.7-46.4,32.5c-0.5,0.4-0.6,0.7-0.4,1.3c12.1,35.8,24.3,71.6,36.4,107.4c0.1,0.2,0.1,0.4,0.2,0.8C210.8,269.1,179.8,246.3,148.8,223.4z"/><path class="st0" fill="#ffcc00" d="M148.8,223.4c-31.1,22.9-62.2,45.8-93.5,68.9c0.1-0.4,0.2-0.7,0.3-0.9c12.7-35.9,25.3-71.7,38-107.6c0.2-0.6,0.2-1-0.4-1.4c-30.8-21.4-61.6-42.8-92.4-64.2c-0.7-0.5-0.9-1-0.7-1.8c0.4,0,0.8,0,1.1,0c26.2,0.3,52.4,0.7,78.6,1c11.3,0.2,22.7,0.3,34,0.5c0.8,0,1.1-0.2,1.4-1c11.1-36.2,22.3-72.4,33.4-108.5c0.1-0.2,0.1-0.4,0.3-0.8"/></svg>
    </div>


</div>


        <a href="/projects/openofficeorg.mirror/reviews/" class="count" title="Apache OpenOffice Reviews">
            350 Reviews
        </a>
    </div>
    

    <strong class="">
        <b class="label">Downloads:</b>
        
            207,179 This Week
        
    </strong>

    <strong class="">
        <b class="label">Last Update:</b>
        
            <time class="dateUpdated" datetime="2023-12-23">2023-12-23</time>
        
    </strong>

        </div>
        
    </div>
    <div>
        <a href="/projects/openofficeorg.mirror/" title="Find out more about Apache OpenOffice" class="button green hollow see-project">See Project</a>
    </div>
</li>

            
            




<li>
    
        


<div class="project-icon   " >
    
    
    <img alt="KeePass" title="KeePass" 
src="//a.fsdn.com/allura/p/keepass/icon?1740068736?&amp;w=48"
    srcset="//a.fsdn.com/allura/p/keepass/icon?1740068736?&amp;w=72 1.5x
        ,
            //a.fsdn.com/allura/p/keepass/icon?1740068736?&amp;w=96 2x" loading="lazy"/></div>

    
    <div class="project-info ">
        <div class="result-heading-texts">
            <a href="/projects/keepass/" title="Find out more about KeePass">
                
                <strong>KeePass</strong>
            </a>
            
                <a href="/projects/keepass/" title="Find out more about KeePass" class="summary-inline">A lightweight and easy-to-use password manager</a>
            
        </div>
        
        <div class="stats">
            
    
    
    <div class="rating">
        






<div class="m-stars" aria-hidden="true" >


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


</div>


        <a href="/projects/keepass/reviews/" class="count" title="KeePass Reviews">
            602 Reviews
        </a>
    </div>
    

    <strong class="">
        <b class="label">Downloads:</b>
        
            238,327 This Week
        
    </strong>

    <strong class="">
        <b class="label">Last Update:</b>
        
            <time class="dateUpdated" datetime="2025-03-14">2025-03-14</time>
        
    </strong>

        </div>
        
    </div>
    <div>
        <a href="/projects/keepass/" title="Find out more about KeePass" class="button green hollow see-project">See Project</a>
    </div>
</li>

            
            




<li>
    
        


<div class="project-icon   " >
    
    
    <img alt="DeSmuME: Nintendo DS emulator" title="DeSmuME: Nintendo DS emulator" 
src="//a.fsdn.com/allura/p/desmume/icon?1513717481?&amp;w=48"
    srcset="//a.fsdn.com/allura/p/desmume/icon?1513717481?&amp;w=72 1.5x
        ,
            //a.fsdn.com/allura/p/desmume/icon?1513717481?&amp;w=96 2x" loading="lazy"/></div>

    
    <div class="project-info ">
        <div class="result-heading-texts">
            <a href="/projects/desmume/" title="Find out more about DeSmuME: Nintendo DS emulator">
                
                <strong>DeSmuME: Nintendo DS emulator</strong>
            </a>
            
                <a href="/projects/desmume/" title="Find out more about DeSmuME: Nintendo DS emulator" class="summary-inline">DeSmuME is a Nintendo DS emulator</a>
            
        </div>
        
        <div class="stats">
            
    
    
    <div class="rating">
        






<div class="m-stars" aria-hidden="true" >


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive half">
        
<svg  data-name="sf_star_yellow_half" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 300" style="enable-background:new 0 0 300 300;" xml:space="preserve"><path class="st1" fill="#CCCCCB" d="M148.8,223.4c-31.1,22.9-62.2,45.8-93.5,68.9c0.1-0.4,0.2-0.7,0.3-0.9c12.7-35.9,25.3-71.7,38-107.6c0.2-0.6,0.2-1-0.4-1.4c-30.8-21.4-61.6-42.8-92.4-64.2c-0.7-0.5-0.9-1-0.7-1.8c0.4,0,0.8,0,1.1,0c26.2,0.3,52.4,0.7,78.6,1c11.3,0.2,22.7,0.3,34,0.5c0.8,0,1.1-0.2,1.4-1c11.1-36.2,22.3-72.4,33.4-108.5c0.1-0.2,0.1-0.4,0.3-0.8c11.3,37.1,22.6,74,34,110.9c39-0.8,78-1.5,117.2-2.3c-0.3,0.3-0.5,0.4-0.7,0.6c-15.7,11-31.5,22.1-47.2,33.1c-15.5,10.8-30.9,21.7-46.4,32.5c-0.5,0.4-0.6,0.7-0.4,1.3c12.1,35.8,24.3,71.6,36.4,107.4c0.1,0.2,0.1,0.4,0.2,0.8C210.8,269.1,179.8,246.3,148.8,223.4z"/><path class="st0" fill="#ffcc00" d="M148.8,223.4c-31.1,22.9-62.2,45.8-93.5,68.9c0.1-0.4,0.2-0.7,0.3-0.9c12.7-35.9,25.3-71.7,38-107.6c0.2-0.6,0.2-1-0.4-1.4c-30.8-21.4-61.6-42.8-92.4-64.2c-0.7-0.5-0.9-1-0.7-1.8c0.4,0,0.8,0,1.1,0c26.2,0.3,52.4,0.7,78.6,1c11.3,0.2,22.7,0.3,34,0.5c0.8,0,1.1-0.2,1.4-1c11.1-36.2,22.3-72.4,33.4-108.5c0.1-0.2,0.1-0.4,0.3-0.8"/></svg>
    </div>


</div>


        <a href="/projects/desmume/reviews/" class="count" title="DeSmuME: Nintendo DS emulator Reviews">
            123 Reviews
        </a>
    </div>
    

    <strong class="">
        <b class="label">Downloads:</b>
        
            1,930 This Week
        
    </strong>

    <strong class="">
        <b class="label">Last Update:</b>
        
            <time class="dateUpdated" datetime="2021-01-13">2021-01-13</time>
        
    </strong>

        </div>
        
    </div>
    <div>
        <a href="/projects/desmume/" title="Find out more about DeSmuME: Nintendo DS emulator" class="button green hollow see-project">See Project</a>
    </div>
</li>

            
        </ul>
    </div>
</aside>

        <hr class="bottom">


        
        <div class="small-12 columns content-supplement">
            

        </div>
        

        
            
<aside class="m-wide-widget m-wide-projects-widget ">
    <div class="as-header">
        
        Top 3 Projects in Database Engines/Servers
    </div>
    <div class="body">
        <ul>
            
            




<li>
    
        


<div class="project-icon   " >
    
    
    <img alt="XAMPP" title="XAMPP" 
src="//a.fsdn.com/allura/p/xampp/icon?1599843055?&amp;w=48"
    srcset="//a.fsdn.com/allura/p/xampp/icon?1599843055?&amp;w=72 1.5x
        ,
            //a.fsdn.com/allura/p/xampp/icon?1599843055?&amp;w=96 2x" loading="lazy"/></div>

    
    <div class="project-info ">
        <div class="result-heading-texts">
            <a href="/projects/xampp/" title="Find out more about XAMPP">
                
                <strong>XAMPP</strong>
            </a>
            
                <a href="/projects/xampp/" title="Find out more about XAMPP" class="summary-inline">An easy to install Apache distribution containing MySQL, PHP, and Perl</a>
            
        </div>
        
        <div class="stats">
            
    
    
    <div class="rating">
        






<div class="m-stars" aria-hidden="true" >


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive half">
        
<svg  data-name="sf_star_yellow_half" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 300" style="enable-background:new 0 0 300 300;" xml:space="preserve"><path class="st1" fill="#CCCCCB" d="M148.8,223.4c-31.1,22.9-62.2,45.8-93.5,68.9c0.1-0.4,0.2-0.7,0.3-0.9c12.7-35.9,25.3-71.7,38-107.6c0.2-0.6,0.2-1-0.4-1.4c-30.8-21.4-61.6-42.8-92.4-64.2c-0.7-0.5-0.9-1-0.7-1.8c0.4,0,0.8,0,1.1,0c26.2,0.3,52.4,0.7,78.6,1c11.3,0.2,22.7,0.3,34,0.5c0.8,0,1.1-0.2,1.4-1c11.1-36.2,22.3-72.4,33.4-108.5c0.1-0.2,0.1-0.4,0.3-0.8c11.3,37.1,22.6,74,34,110.9c39-0.8,78-1.5,117.2-2.3c-0.3,0.3-0.5,0.4-0.7,0.6c-15.7,11-31.5,22.1-47.2,33.1c-15.5,10.8-30.9,21.7-46.4,32.5c-0.5,0.4-0.6,0.7-0.4,1.3c12.1,35.8,24.3,71.6,36.4,107.4c0.1,0.2,0.1,0.4,0.2,0.8C210.8,269.1,179.8,246.3,148.8,223.4z"/><path class="st0" fill="#ffcc00" d="M148.8,223.4c-31.1,22.9-62.2,45.8-93.5,68.9c0.1-0.4,0.2-0.7,0.3-0.9c12.7-35.9,25.3-71.7,38-107.6c0.2-0.6,0.2-1-0.4-1.4c-30.8-21.4-61.6-42.8-92.4-64.2c-0.7-0.5-0.9-1-0.7-1.8c0.4,0,0.8,0,1.1,0c26.2,0.3,52.4,0.7,78.6,1c11.3,0.2,22.7,0.3,34,0.5c0.8,0,1.1-0.2,1.4-1c11.1-36.2,22.3-72.4,33.4-108.5c0.1-0.2,0.1-0.4,0.3-0.8"/></svg>
    </div>


</div>


        <a href="/projects/xampp/reviews/" class="count" title="XAMPP Reviews">
            232 Reviews
        </a>
    </div>
    

    <strong class="">
        <b class="label">Downloads:</b>
        
            438,558 This Week
        
    </strong>

    <strong class="">
        <b class="label">Last Update:</b>
        
            <time class="dateUpdated" datetime="2023-11-25">2023-11-25</time>
        
    </strong>

        </div>
        
    </div>
    <div>
        <a href="/projects/xampp/" title="Find out more about XAMPP" class="button green hollow see-project">See Project</a>
    </div>
</li>

            
            




<li>
    
        


<div class="project-icon   " >
    
    
    <img alt="Firebird" title="Firebird" 
src="//a.fsdn.com/allura/p/firebird/icon?1740215948?&amp;w=48"
    srcset="//a.fsdn.com/allura/p/firebird/icon?1740215948?&amp;w=72 1.5x
        ,
            //a.fsdn.com/allura/p/firebird/icon?1740215948?&amp;w=96 2x" loading="lazy"/></div>

    
    <div class="project-info ">
        <div class="result-heading-texts">
            <a href="/projects/firebird/" title="Find out more about Firebird">
                
                <strong>Firebird</strong>
            </a>
            
                <a href="/projects/firebird/" title="Find out more about Firebird" class="summary-inline">A powerful, cross platform, SQL database system</a>
            
        </div>
        
        <div class="stats">
            
    
    
    <div class="rating">
        






<div class="m-stars" aria-hidden="true" >


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


    
    
    
    
    
    
    
    <div class="star responsive yellow">
        
<svg  data-name="sf_star_yellow" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 285.8" style="enable-background:new 0 0 300 285.8;" xml:space="preserve"><polygon class="st0" fill="#ffcc00" points="150,0 185.4,109.2 300,109.2 207.3,176.6 242.7,285.8 150,218.3 57.3,285.8 92.7,176.6 0,109.2 114.6,109.2 "/></svg>
    </div>


</div>


        <a href="/projects/firebird/reviews/" class="count" title="Firebird Reviews">
            90 Reviews
        </a>
    </div>
    

    <strong class="">
        <b class="label">Downloads:</b>
        
            3,255 This Week
        
    </strong>

    <strong class="">
        <b class="label">Last Update:</b>
        
            <time class="dateUpdated" datetime="2025-02-22">2025-02-22</time>
        
    </strong>

        </div>
        
    </div>
    <div>
        <a href="/projects/firebird/" title="Find out more about Firebird" class="button green hollow see-project">See Project</a>
    </div>
</li>

            
            




<li>
    
        


<div class="project-icon  default-project-icon " >
    
    
    


<svg  data-name="default-icon" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 300 300" style="enable-background:new 0 0 300 300;" xml:space="preserve"><rect class="st0" width="300" height="300"/><g><path class="st1" d="M162.5,154.8c0-28.9-10.2-42-15.6-46.9c-1.1-1-2.7-0.1-2.6,1.3c1.1,16.3-19.4,20.3-19.4,45.9h0c0,0.1,0,0.1,0,0.2c0,15.6,11.8,28.3,26.3,28.3c14.5,0,26.3-12.7,26.3-28.3c0-0.1,0-0.1,0-0.2h0c0-7.2-2.7-14.1-5.5-19.3c-0.5-1-2.1-0.6-1.9,0.3C174.9,158.1,162.5,171.8,162.5,154.8z"/><g><path class="st1" d="M131.2,215.6c-0.7,0-1.3-0.3-1.8-0.7l-67.2-67.1c-1-1-1-2.6,0-3.6l70.9-70.9c0.5-0.5,1.1-0.7,1.8-0.7h20.4c1.2,0,2,0.8,2.3,1.6c0.3,0.7,0.3,1.9-0.5,2.7l-66.7,66.7c-1.3,1.3-1.3,3.5,0,4.9l52.7,52.7c1,1,1,2.6,0,3.6L133,214.9C132.5,215.4,131.9,215.6,131.2,215.6z"/></g><g><path class="st1" d="M144.7,227.4c-1.2,0-2-0.8-2.3-1.5c-0.3-0.7-0.3-1.9,0.5-2.7l66.7-66.7c0.7-0.6,1-1.5,1-2.4s-0.4-1.8-1-2.4l-52.7-52.7c-1-1-1-2.6,0-3.6l10.2-10.2c0.5-0.5,1.1-0.7,1.8-0.7c0.7,0,1.3,0.3,1.8,0.7l67,67.1c0.5,0.5,0.7,1.1,0.7,1.8s-0.3,1.3-0.7,1.8l-70.9,70.9c-0.5,0.5-1.1,0.7-1.8,0.7H144.7z"/></g></g></svg>
    </div>

    
    <div class="project-info ">
        <div class="result-heading-texts">
            <a href="/projects/libdbi/" title="Find out more about Database Independent Abstraction Layer">
                
                <strong>Database Independent Abstraction Layer</strong>
            </a>
            
        </div>
        
        <div class="stats">
            
    
    

    <strong class="">
        <b class="label">Downloads:</b>
        
            3,095 This Week
        
    </strong>

    <strong class="">
        <b class="label">Last Update:</b>
        
            <time class="dateUpdated" datetime="2017-10-27">2017-10-27</time>
        
    </strong>

        </div>
        
    </div>
    <div>
        <a href="/projects/libdbi/" title="Find out more about Database Independent Abstraction Layer" class="button green hollow see-project">See Project</a>
    </div>
</li>

            
        </ul>
    </div>
</aside>

        

        
        <div class="dlp_newsletter_subscribe">
            
<div class="m-kmu row">
    <div class="small-12 column">
        <div class="row is-collapse-child">
            <div class="small-12 column">
                <h3 class="as-h1">Keep Me Updated!</h3>
                <div class="subscribe-tagline">Get notified by email when XAMPP releases a new version.</div>
                

<form action="/user/newsletters/subscribe" method="post"
      class="newsletter-subscribe-form compliance-form optin-wide kmunl-subscribe-form"
      data-shortname="xampp" data-handler="KMUNSWidget">
    <input type="hidden" name="country_code_source" value="user">
    <input type="hidden" name="source" value="DLP">

    <div class="row">
        <div class="column small-12 large-6">
            <input type="email" class="subscriber-email" name="email"  placeholder="Enter your email address" value="" required autocapitalize="none">
            
        </div>
        
        <label class="column ">
            <span class="show-for-sr">Country</span><span class="input">
<select id="country" name="country" required class=" use-placeholder-color"  autocomplete="country">
    
    <option value=""></option>
    
    <option value="AF">Afghanistan</option>
    <option value="AX">Aland Islands</option>
    <option value="AL">Albania</option>
    <option value="DZ">Algeria</option>
    <option value="AS">American Samoa</option>
    <option value="AD">Andorra</option>
    <option value="AO">Angola</option>
    <option value="AI">Anguilla</option>
    <option value="AQ">Antarctica</option>
    <option value="AG">Antigua and Barbuda</option>
    <option value="AR">Argentina</option>
    <option value="AM">Armenia</option>
    <option value="AW">Aruba</option>
    <option value="AU">Australia</option>
    <option value="AT">Austria</option>
    <option value="AZ">Azerbaijan</option>
    <option value="BS">Bahamas</option>
    <option value="BH">Bahrain</option>
    <option value="BD">Bangladesh</option>
    <option value="BB">Barbados</option>
    <option value="BY">Belarus</option>
    <option value="BE">Belgium</option>
    <option value="BZ">Belize</option>
    <option value="BJ">Benin</option>
    <option value="BM">Bermuda</option>
    <option value="BT">Bhutan</option>
    <option value="BO">Bolivia</option>
    <option value="BA">Bosnia and Herzegovina</option>
    <option value="BW">Botswana</option>
    <option value="BV">Bouvet Island</option>
    <option value="BR">Brazil</option>
    <option value="IO">British Indian Ocean Territory</option>
    <option value="BN">Brunei Darussalam</option>
    <option value="BG">Bulgaria</option>
    <option value="BF">Burkina Faso</option>
    <option value="BI">Burundi</option>
    <option value="KH">Cambodia</option>
    <option value="CM">Cameroon</option>
    <option value="CA">Canada</option>
    <option value="CV">Cape Verde</option>
    <option value="KY">Cayman Islands</option>
    <option value="CF">Central African Republic</option>
    <option value="TD">Chad</option>
    <option value="CL">Chile</option>
    <option value="CN">China</option>
    <option value="CX">Christmas Island</option>
    <option value="CC">Cocos (Keeling) Islands</option>
    <option value="CO">Colombia</option>
    <option value="KM">Comoros</option>
    <option value="CG">Congo</option>
    <option value="CD">Congo, The Democratic Republic of the</option>
    <option value="CK">Cook Islands</option>
    <option value="CR">Costa Rica</option>
    <option value="CI">Cote D&#39;Ivoire</option>
    <option value="HR">Croatia</option>
    <option value="CU">Cuba</option>
    <option value="CY">Cyprus</option>
    <option value="CZ">Czech Republic</option>
    <option value="DK">Denmark</option>
    <option value="DJ">Djibouti</option>
    <option value="DM">Dominica</option>
    <option value="DO">Dominican Republic</option>
    <option value="EC">Ecuador</option>
    <option value="EG">Egypt</option>
    <option value="SV">El Salvador</option>
    <option value="GQ">Equatorial Guinea</option>
    <option value="ER">Eritrea</option>
    <option value="EE">Estonia</option>
    <option value="ET">Ethiopia</option>
    <option value="FK">Falkland Islands (Malvinas)</option>
    <option value="FO">Faroe Islands</option>
    <option value="FJ">Fiji</option>
    <option value="FI">Finland</option>
    <option value="FR">France</option>
    <option value="GF">French Guiana</option>
    <option value="PF">French Polynesia</option>
    <option value="TF">French Southern Territories</option>
    <option value="GA">Gabon</option>
    <option value="GM">Gambia</option>
    <option value="GE">Georgia</option>
    <option value="DE">Germany</option>
    <option value="GH">Ghana</option>
    <option value="GI">Gibraltar</option>
    <option value="GR">Greece</option>
    <option value="GL">Greenland</option>
    <option value="GD">Grenada</option>
    <option value="GP">Guadeloupe</option>
    <option value="GU">Guam</option>
    <option value="GT">Guatemala</option>
    <option value="GG">Guernsey</option>
    <option value="GN">Guinea</option>
    <option value="GW">Guinea-Bissau</option>
    <option value="GY">Guyana</option>
    <option value="HT">Haiti</option>
    <option value="HM">Heard Island and McDonald Islands</option>
    <option value="VA">Holy See (Vatican City State)</option>
    <option value="HN">Honduras</option>
    <option value="HK">Hong Kong</option>
    <option value="HU">Hungary</option>
    <option value="IS">Iceland</option>
    <option value="IN">India</option>
    <option value="ID">Indonesia</option>
    <option value="IR">Iran, Islamic Republic of</option>
    <option value="IQ">Iraq</option>
    <option value="IE">Ireland</option>
    <option value="IM">Isle of Man</option>
    <option value="IL">Israel</option>
    <option value="IT">Italy</option>
    <option value="JM">Jamaica</option>
    <option value="JP">Japan</option>
    <option value="JE">Jersey</option>
    <option value="JO">Jordan</option>
    <option value="KZ">Kazakhstan</option>
    <option value="KE">Kenya</option>
    <option value="KI">Kiribati</option>
    <option value="KP">Korea, Democratic People&#39;s Republic of</option>
    <option value="KR">Korea, Republic of</option>
    <option value="XK">Kosovo</option>
    <option value="KW">Kuwait</option>
    <option value="KG">Kyrgyzstan</option>
    <option value="LA">Lao People&#39;s Democratic Republic</option>
    <option value="LV">Latvia</option>
    <option value="LB">Lebanon</option>
    <option value="LS">Lesotho</option>
    <option value="LR">Liberia</option>
    <option value="LY">Libyan Arab Jamahiriya</option>
    <option value="LI">Liechtenstein</option>
    <option value="LT">Lithuania</option>
    <option value="LU">Luxembourg</option>
    <option value="MO">Macau</option>
    <option value="MK">Macedonia</option>
    <option value="MG">Madagascar</option>
    <option value="MW">Malawi</option>
    <option value="MY">Malaysia</option>
    <option value="MV">Maldives</option>
    <option value="ML">Mali</option>
    <option value="MT">Malta</option>
    <option value="MH">Marshall Islands</option>
    <option value="MQ">Martinique</option>
    <option value="MR">Mauritania</option>
    <option value="MU">Mauritius</option>
    <option value="YT">Mayotte</option>
    <option value="MX">Mexico</option>
    <option value="FM">Micronesia, Federated States of</option>
    <option value="MD">Moldova, Republic of</option>
    <option value="MC">Monaco</option>
    <option value="MN">Mongolia</option>
    <option value="ME">Montenegro</option>
    <option value="MS">Montserrat</option>
    <option value="MA">Morocco</option>
    <option value="MZ">Mozambique</option>
    <option value="MM">Myanmar</option>
    <option value="NA">Namibia</option>
    <option value="NR">Nauru</option>
    <option value="NP">Nepal</option>
    <option value="NL">Netherlands</option>
    <option value="AN">Netherlands Antilles</option>
    <option value="NC">New Caledonia</option>
    <option value="NZ">New Zealand</option>
    <option value="NI">Nicaragua</option>
    <option value="NE">Niger</option>
    <option value="NG">Nigeria</option>
    <option value="NU">Niue</option>
    <option value="NF">Norfolk Island</option>
    <option value="MP">Northern Mariana Islands</option>
    <option value="NO">Norway</option>
    <option value="OM">Oman</option>
    <option value="PK">Pakistan</option>
    <option value="PW">Palau</option>
    <option value="PS">Palestinian Territory</option>
    <option value="PA">Panama</option>
    <option value="PG">Papua New Guinea</option>
    <option value="PY">Paraguay</option>
    <option value="PE">Peru</option>
    <option value="PH">Philippines</option>
    <option value="PN">Pitcairn Islands</option>
    <option value="PL">Poland</option>
    <option value="PT">Portugal</option>
    <option value="PR">Puerto Rico</option>
    <option value="QA">Qatar</option>
    <option value="RE">Reunion</option>
    <option value="RO">Romania</option>
    <option value="RU">Russian Federation</option>
    <option value="RW">Rwanda</option>
    <option value="BL">Saint Barthelemy</option>
    <option value="SH">Saint Helena</option>
    <option value="KN">Saint Kitts and Nevis</option>
    <option value="LC">Saint Lucia</option>
    <option value="MF">Saint Martin</option>
    <option value="PM">Saint Pierre and Miquelon</option>
    <option value="VC">Saint Vincent and the Grenadines</option>
    <option value="WS">Samoa</option>
    <option value="SM">San Marino</option>
    <option value="ST">Sao Tome and Principe</option>
    <option value="SA" selected>Saudi Arabia</option>
    <option value="SN">Senegal</option>
    <option value="RS">Serbia</option>
    <option value="SC">Seychelles</option>
    <option value="SL">Sierra Leone</option>
    <option value="SG">Singapore</option>
    <option value="SK">Slovakia</option>
    <option value="SI">Slovenia</option>
    <option value="SB">Solomon Islands</option>
    <option value="SO">Somalia</option>
    <option value="ZA">South Africa</option>
    <option value="GS">South Georgia and the South Sandwich Islands</option>
    <option value="ES">Spain</option>
    <option value="LK">Sri Lanka</option>
    <option value="SD">Sudan</option>
    <option value="SR">Suriname</option>
    <option value="SJ">Svalbard and Jan Mayen</option>
    <option value="SZ">Swaziland</option>
    <option value="SE">Sweden</option>
    <option value="CH">Switzerland</option>
    <option value="SY">Syrian Arab Republic</option>
    <option value="TW">Taiwan</option>
    <option value="TJ">Tajikistan</option>
    <option value="TZ">Tanzania, United Republic of</option>
    <option value="TH">Thailand</option>
    <option value="TL">Timor-Leste</option>
    <option value="TG">Togo</option>
    <option value="TK">Tokelau</option>
    <option value="TO">Tonga</option>
    <option value="TT">Trinidad and Tobago</option>
    <option value="TN">Tunisia</option>
    <option value="TR">Turkey</option>
    <option value="TM">Turkmenistan</option>
    <option value="TC">Turks and Caicos Islands</option>
    <option value="TV">Tuvalu</option>
    <option value="UG">Uganda</option>
    <option value="UA">Ukraine</option>
    <option value="AE">United Arab Emirates</option>
    <option value="GB">United Kingdom</option>
    <option value="US">United States</option>
    <option value="UM">United States Minor Outlying Islands</option>
    <option value="UY">Uruguay</option>
    <option value="UZ">Uzbekistan</option>
    <option value="VU">Vanuatu</option>
    <option value="VE">Venezuela</option>
    <option value="VN">Vietnam</option>
    <option value="VG">Virgin Islands, British</option>
    <option value="VI">Virgin Islands, U.S.</option>
    <option value="WF">Wallis and Futuna</option>
    <option value="EH">Western Sahara</option>
    <option value="YE">Yemen</option>
    <option value="ZM">Zambia</option>
    <option value="ZW">Zimbabwe</option>
</select>
</span>
        </label>
        <label class="input-set-state column small-6 large-3">
            <span class="show-for-sr">State</span>
            <span class="input">
<select id="state" name="state" required class=" use-placeholder-color"  autocomplete="address-level1">
    
    <option value=""></option>
    
    <option value="AL">Alabama</option>
    <option value="AK">Alaska</option>
    <option value="AZ">Arizona</option>
    <option value="AR">Arkansas</option>
    <option value="CA">California</option>
    <option value="CO">Colorado</option>
    <option value="CT">Connecticut</option>
    <option value="DE">Delaware</option>
    <option value="DC">District of Columbia</option>
    <option value="FL">Florida</option>
    <option value="GA">Georgia</option>
    <option value="HI">Hawaii</option>
    <option value="ID">Idaho</option>
    <option value="IL">Illinois</option>
    <option value="IN">Indiana</option>
    <option value="IA">Iowa</option>
    <option value="KS">Kansas</option>
    <option value="KY">Kentucky</option>
    <option value="LA">Louisiana</option>
    <option value="ME">Maine</option>
    <option value="MD">Maryland</option>
    <option value="MA">Massachusetts</option>
    <option value="MI">Michigan</option>
    <option value="MN">Minnesota</option>
    <option value="MS">Mississippi</option>
    <option value="MO">Missouri</option>
    <option value="MT">Montana</option>
    <option value="NE">Nebraska</option>
    <option value="NV">Nevada</option>
    <option value="NH">New Hampshire</option>
    <option value="NJ">New Jersey</option>
    <option value="NM">New Mexico</option>
    <option value="NY">New York</option>
    <option value="NC">North Carolina</option>
    <option value="ND">North Dakota</option>
    <option value="OH">Ohio</option>
    <option value="OK">Oklahoma</option>
    <option value="OR">Oregon</option>
    <option value="PA">Pennsylvania</option>
    <option value="PR">Puerto Rico</option>
    <option value="RI">Rhode Island</option>
    <option value="SC">South Carolina</option>
    <option value="SD">South Dakota</option>
    <option value="TN">Tennessee</option>
    <option value="TX">Texas</option>
    <option value="UT">Utah</option>
    <option value="VT">Vermont</option>
    <option value="VA">Virginia</option>
    <option value="WA">Washington</option>
    <option value="WV">West Virginia</option>
    <option value="WI">Wisconsin</option>
    <option value="WY">Wyoming</option>
</select>
</span>
        </label>
        
    </div>
    <div class="row">
        
            <div class="column small-12 large-4">
                
<label>
    <span class="show-for-sr">Full Name</span>
    <input name="name" type="text" value="" placeholder="Full name" required autocomplete="name" >
</label>
<span class="fielderror"></span>
            </div>

            <div class="column small-12 large-4 field-group-phone">
                
    
    <span>
    <input type="text" name="phone_ext" value="" placeholder="Ext" class="phone-ext" autocomplete="work tel-extension">
    </span>
    <label class="phone-label">
        <span class="show-for-sr">Phone Number</span>
        <input type="text" name="phone" value="" placeholder="Phone # " class="phone"  autocomplete="work tel-national">
    </label>
    <span class="fielderror"></span>
    <span class="fielderror"></span>
            </div>

            
    <div class="column small-12 large-4">
    <label>
        <span class="show-for-sr">Job Title</span>
        <input type="text" name="job" value="" placeholder="Job Title " class="job"  autocomplete="organization-title">
        <span class="fielderror"></span>
    </label>
    </div>
    <div class="column small-12 large-4">
    <label>
        <span class="show-for-sr">Industry</span>
        <input type="text" name="industry" value="" placeholder="Industry " class="industry" >
        <span class="fielderror"></span>
    </label>
        </div>
        <div class="column small-12 large-4">
    <label>
        <span class="show-for-sr">Company</span>
        <input type="text" name="company" value="" placeholder="Company " class="company"  autocomplete="organization">
        <span class="fielderror"></span>
    </label>
    </div>
    <div class="column small-12 large-4">
    <label>
        <span class="show-for-sr">Company Size</span>
        
        
<select id="employees-8060391" name="employees"  class="employees use-placeholder-color" >
    
    <option value="">Company Size:  </option>
    
    <option value="1 - 25">1 - 25</option>
    <option value="26 - 99">26 - 99</option>
    <option value="100 - 499">100 - 499</option>
    <option value="500 - 999">500 - 999</option>
    <option value="1,000 - 4,999">1,000 - 4,999</option>
    <option value="5,000 - 9,999">5,000 - 9,999</option>
    <option value="10,000 - 19,999">10,000 - 19,999</option>
    <option value="20,000 or More">20,000 or More</option>
</select>

        <span class="fielderror"></span>
    </label>
    </div>
        

        <div class="column small-12">
            
                
 

<label class="input-set input-set-kmu kmunl_consent allow-precheck ">
    <span class="checkbox"> <input type="checkbox" name="kmu" value="xampp"   data-consent-action data-consent-id=67c8e2504e339052243db488 ></span>
    <span class="checkbox-label">Get an email when this software releases a new version (no other emails will be sent).</span>
    

</label>


                
                
                
 

<label class="input-set input-set-newsletters-optin-sitewide allow-precheck loose-consent-requirement kmunl_consent">
    <span class="checkbox"> <input type="checkbox" name="newsletters" value="sitewide"   data-consent-action data-consent-id=59aed8e456585fa9603b60e9 ></span>
    <span class="checkbox-label">Get the SourceForge newsletter.</span>
    

</label>


                
                
 

<label class="input-set allow-precheck input-set-newsletters-optin-all kmunl_consent">
    <span class="checkbox"> <input type="checkbox" name="newsletters" value="sitewide research"   data-consent-action data-consent-id=59aed8e456585fa9603b60ea ></span>
    <span class="checkbox-label">Get newsletters and notices that include site news, special offers and exclusive discounts about IT products &amp; services.</span>
    

</label>


                
            

            <input type="hidden" name="newsletters" class="input-assumed-newsletters" value="">
            <input type="hidden" name="kmu" class="input-assumed-kmu" value="">
        </div>

        
 

<label class="input-set input-set-agree-general allow-precheck column small-12">
    <span class="checkbox"> <input type="checkbox" name="consent" value="consent"   data-consent-action data-consent-id=64a70e76edead243b8c89d55 ></span>
    <span class="checkbox-label">I understand by clicking on "Subscribe" below I am agreeing to the SourceForge  <a href="https://slashdotmedia.com/terms-of-use">Terms of Use</a> and the <a href="https://slashdotmedia.com/privacy-statement/">Privacy Policy</a> which describe how we use and share your data. I agree to receive these communications from SourceForge.net. I understand that I can withdraw my consent at anytime.</span>
    

</label>


        
 

<label class="input-set input-set-agree-general-gdpr minimum-explicit-required column small-12">
    <span class="checkbox"> <input type="checkbox" name="consent" value="consent"   data-consent-action data-consent-id=6287d2bc1f62e544cb66ca31 ></span>
    <span class="checkbox-label">I understand by clicking below I am agreeing to the SourceForge <a href="https://slashdotmedia.com/terms-of-use">Terms and Conditions</a>.  I agree to receive these communications from SourceForge.net.  I understand that I can withdraw my consent at anytime. Please refer to our <a href="https://slashdotmedia.com/terms-of-use">Terms of Use</a> and <a href="https://slashdotmedia.com/privacy-statement/">Privacy Policy</a> or <a href="/support">Contact Us</a> for more details.</span>
    

</label>



        <div class="subscriber-submit-wrapper column small-12">
            

    <div class="js-required fielderror">JavaScript is required for this form.</div>
    <div class="g-recaptcha"
          data-sitekey="6LeVgCEUAAAAACtawUTrPTBy0mTrGtjpPn_Xh-ZW"
          data-badge="bottomleft"
          data-size="invisible"
          >
    </div>
    <script>
        SF.recaptchaInUse = true;
    </script>


            <button type="submit" class="subscriber-submit  button">
                <span>
                    Subscribe
                </span>
            </button>
        </div>
    </div>
    <input type="hidden" name="_visit_cookie" value="aaaae66f-100c-47e8-be02-daa4a8e3489d"/>
</form>


            </div>
        </div>
    </div>
</div>

        </div>
        
    </article>
</div>
                    </div>
                    <div class="l-gutter">
                    </div>
                    <div class="l-side-column">
                        
<div class="scroll-fixable">
    <section class="sterling">
    


    
    


<div id="SF_Temp5_300x250_A_wrapped" data-id="div-gpt-ad-*************-0" class="draper multisize  
visibility_rules
 v_300_large "> </div><script>
/*global googletag */
if (SF.initial_breakpoints_visible.large) {
(function(){
    
    var el = document.getElementById('SF_Temp5_300x250_A_wrapped');
    var newNode = document.createElement('div');
    newNode.id = 'div-gpt-ad-*************-0';
    el.appendChild(newNode);
}());

gptadRenderers['SF_Temp5_300x250_A'] = function(){  // jshint ignore:line
    
        if (!SF.adblock) {
            
                
            
            $('#div-gpt-ad-*************-0').parents('.draper').css("min-height", 250 + 13 + 12); // for height of .lbl-ad and padding
        }
    
    googletag.cmd.push(function() {
        googletag.display('div-gpt-ad-*************-0');
    });
};
gptadRenderers['SF_Temp5_300x250_A']();  // jshint ignore:line
}
</script>

    


    


<div id="SF_Temp5_300x250_B_wrapped" data-id="div-gpt-ad-*************-0" class="draper medrec  
visibility_rules
 v_300_large "> </div><script>
/*global googletag */
if (SF.initial_breakpoints_visible.large) {
(function(){
    
    var el = document.getElementById('SF_Temp5_300x250_B_wrapped');
    var newNode = document.createElement('div');
    newNode.id = 'div-gpt-ad-*************-0';
    el.appendChild(newNode);
}());

gptadRenderers['SF_Temp5_300x250_B'] = function(){  // jshint ignore:line
    
        if (!SF.adblock) {
            
                
            
            $('#div-gpt-ad-*************-0').parents('.draper').css("min-height", 250 + 13 + 12); // for height of .lbl-ad and padding
        }
    
    googletag.cmd.push(function() {
        googletag.display('div-gpt-ad-*************-0');
    });
};
gptadRenderers['SF_Temp5_300x250_B']();  // jshint ignore:line
}
</script>
    


    


<div id="SF_Temp5_300x250_C_wrapped" data-id="div-gpt-ad-*************-0" class="draper medrec  
visibility_rules
 v_300_large "> </div><script>
/*global googletag */
if (SF.initial_breakpoints_visible.large) {
(function(){
    
    var el = document.getElementById('SF_Temp5_300x250_C_wrapped');
    var newNode = document.createElement('div');
    newNode.id = 'div-gpt-ad-*************-0';
    el.appendChild(newNode);
}());

gptadRenderers['SF_Temp5_300x250_C'] = function(){  // jshint ignore:line
    
    googletag.cmd.push(function() {
        googletag.display('div-gpt-ad-*************-0');
    });
};
gptadRenderers['SF_Temp5_300x250_C']();  // jshint ignore:line
}
</script>
    </section>

    
    
        <aside class="m-sidebar-widget">
            <div class="as-header">Related Business Categories</div>
            <div class="body">
                <ul>
                    
                        
                            <li class="item">
                                <a href="/software/server-management/">Server Management</a>
                            </li>
                    
                        
                            <li class="item">
                                <a href="/software/hosting-control-panels/">Hosting Control Panels</a>
                            </li>
                    
                        
                            <li class="item">
                                <a href="/software/web-servers/">Web Servers</a>
                            </li>
                    
                        
                            <li class="item">
                                <a href="/software/it-management/">IT Management</a>
                            </li>
                    
                </ul>
            </div>
        </aside>
    

    
    <aside class="m-sidebar-widget content-supplement" id="sidebar-features">
    <div class="as-header">
        XAMPP Features
    </div>
    <ul ><li class="item odd">Apache</li><li class="item even">MySQL</li><li class="item odd">PHP</li><li class="item even">Perl</li><li class="item odd last">and many more...</li>
    </ul>
    </aside>
    
</div>

    
<script>
if (!SF.adblock && SF.initial_breakpoints_visible.large) {
    
        bizx.cmp.ifConsent({ purposes: 'all', vendors: 'google-ads'}, function() {
            SF.Ads.scrollFixable = new SF.ScrollFixable($('.scroll-fixable').eq(0), {"adjustHorizontal":false,"avoidElement":".l-header-nav, .l-header-nav-bottom","watchAvoidStickiness":true});
            if (SF.Ads.acceptable_ads_active){
                SF.Ads.scrollFixable.disable();
            }
        });
    
}
</script>

                    </div>
                </div>
            

            
    <footer class="sandiego">
    <div class="as-row">
        <div class="footer-wrapper">
            <nav aria-label="Site Links" role="navigation">
                <section>
                    <div class="as-h2">SourceForge</div>
                    <ul>
                    
                        <li><a href="/create/" title="Create a Project">Create a Project</a></li>
                    
                        <li><a href="/directory/" title="Open Source Software Directory">Open Source Software</a></li>
                        <li><a href="/software/" title="Business Software Directory">Business Software</a></li>
                        
                            
                            <li><a href="/top" title="Top Open Source Projects">Top Downloaded Projects</a></li>
                        

                    </ul>
                </section>
            </nav>
            <nav aria-label="Company Links" role="navigation">
                <section>
                    <div class="as-h2">Company</div>
                    <ul>
                        <li><a href="/about">About</a></li>
                        <li><a href="/about/leadership" title="Open Source Software Directory">Team</a></li>
                        <li class="h-card">
                            <address>
                                <span class="p-name p-org">SourceForge Headquarters</span><br>
                                <span class="p-street-address">225 Broadway Suite 1600</span><br>
                                <span class="p-locality">San Diego, CA <span class="p-postal-code">92101</span></span><br>
                                <span class="p-tel">+****************</span><br>
                            </address>
                        </li>
                        <li id="social">
                            
<span></span>
<a href="https://twitter.com/sourceforge" class="twitter" rel="nofollow" target="_blank" title="SourceForge on X">


<svg  class="vertical-icon-fix" data-name="twitter" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1684 408q-67 98-162 167 1 14 1 42 0 130-38 259.5t-115.5 248.5-184.5 210.5-258 146-323 54.5q-271 0-496-145 35 4 78 4 225 0 401-138-105-2-188-64.5t-114-159.5q33 5 61 5 43 0 85-11-112-23-185.5-111.5t-73.5-205.5v-4q68 38 146 41-66-44-105-115t-39-154q0-88 44-163 121 149 294.5 238.5t371.5 99.5q-8-38-8-74 0-134 94.5-228.5t228.5-94.5q140 0 236 102 109-21 205-78-37 115-142 178 93-10 186-50z"/></svg></a>
<a href="https://fosstodon.org/@sourceforge" rel="me nofollow" target="_blank" title="SourceForge on Mastodon">


<svg  class="vertical-icon-fix" data-name="mastodon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M433 179.11c0-97.2-63.71-125.7-63.71-125.7-62.52-28.7-228.56-28.4-290.48 0 0 0-63.72 28.5-63.72 125.7 0 115.7-6.6 259.4 105.63 289.1 40.51 10.7 75.32 13 103.33 11.4 50.81-2.8 79.32-18.1 79.32-18.1l-1.7-36.9s-36.31 11.4-77.12 10.1c-40.41-1.4-83-4.4-89.63-54a102.54 102.54 0 0 1-.9-13.9c85.63 20.9 158.65 9.1 178.75 6.7 56.12-6.7 105-41.3 111.23-72.9 9.8-49.8 9-121.5 9-121.5zm-75.12 125.2h-46.63v-114.2c0-49.7-64-51.6-64 6.9v62.5h-46.33V197c0-58.5-64-56.6-64-6.9v114.2H90.19c0-122.1-5.2-147.9 18.41-175 25.9-28.9 79.82-30.8 103.83 6.1l11.6 19.5 11.6-19.5c24.11-37.1 78.12-34.8 103.83-6.1 23.71 27.3 18.4 53 18.4 175z"/></svg></a>
<a href="https://www.facebook.com/sourceforgenet/" class="facebook" rel="nofollow" target="_blank" title="SourceForge on Facebook">


<svg  data-name="facebook" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M1343 12v264h-157q-86 0-116 36t-30 108v189h293l-39 296h-254v759h-306v-759h-255v-296h255v-218q0-***********.5t277-102.5q147 0 228 12z"/></svg></a>
<a href="https://www.linkedin.com/company/sourceforge.net" class="linkedin" rel="nofollow" target="_blank" title="SourceForge on LinkedIn">


<svg  data-name="linkedin" viewBox="0 0 1792 1792" xmlns="http://www.w3.org/2000/svg"><path d="M477 625v991h-330v-991h330zm21-306q1 73-50.5 122t-135.5 49h-2q-82 0-132-49t-50-122q0-74 51.5-122.5t134.5-48.5 133 48.5 51 122.5zm1166 729v568h-329v-530q0-105-40.5-164.5t-126.5-59.5q-63 0-105.5 34.5t-63.5 85.5q-11 30-11 81v553h-329q2-399 2-647t-1-296l-1-48h329v144h-2q20-32 41-56t56.5-52 87-43.5 114.5-15.5q171 0 275 113.5t104 332.5z"/></svg></a>

<a href="/user/newsletters" rel=nofollow class="newsletter" title="Subscribe to our newsletter">


<svg  class="vertical-icon-fix" data-name="mmSF_11mail" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 42 42" enable-background="new 0 0 42 42" xml:space="preserve"><path fill="#FFFFFF" d="M0,6v30h42V6H0z M24.2,21.2c-0.8,0.8-2.3,2-3.2,2c-0.9,0-2.4-1.2-3.2-2L5.8,9.7h30.3L24.2,21.2z M13.7,21l-9.9,9.4V11.6L13.7,21z M15.7,23L15.7,23c0.5,0.5,2.9,2.9,5.3,2.9c2.4,0,4.8-2.4,5.2-2.8l0.1-0.1l9.8,9.4H5.8L15.7,23z M28.3,21l9.9-9.5v18.9L28.3,21z"/></svg></a>

<span></span>
                        </li>
                    </ul>
                </section>
            </nav>
             <nav aria-label="Resources Links" role="navigation">
                <section>
                    <div class="as-h2">Resources</div>
                    <ul>
                        
                            <li><a href="/support" title="Support Section">Support</a></li>
                            <li><a href="/p/forge/documentation/Docs%20Home/" title="Site Documentation">Site Documentation</a></li>
                        
                        <li><a href="https://fosstodon.org/@sourceforgestatus" title="Site Status" rel="me nofollow">Site Status</a></li>
                        <li><a href="/reviews" title="Reviews" rel="me nofollow">SourceForge Reviews</a></li>

                    </ul>
                </section>
            </nav>
            <section class="footer-logo">
                <a href="/" title="Home" class="sf-logo">
                    
    
    <img src="//a.fsdn.com/con/images/sandiego/sf-logo-full.svg"  alt="SourceForge logo" class="sf-logo-full"/>
                </a>
            </section>
        </div>
    </div>
    <section class="l-nav-bottom">
        <nav class="row">
            
                
            
        
    <div class="columns small-12 large-6 copyright-notice">
        &copy; 2025 Slashdot Media. All Rights Reserved.
    </div>
    <div class="columns large-6 links links-right">
        

    
    

    <a href="https://slashdotmedia.com/terms-of-use" target="_blank" title="Terms" rel="nofollow">Terms</a>
    <a href="https://slashdotmedia.com/privacy-statement/" target="_blank" title="Privacy" rel="nofollow">Privacy</a>

    
    
    

    
        <a href="https://slashdotmedia.com/opt-out-choices/" target="_blank" title="Opt Out" rel="nofollow">Opt Out</a>
    

    
        <a href="https://slashdotmedia.com/contact/" target="_blank" title="Advertise" rel="nofollow">Advertise</a>
    
    </div>
        </nav>
    </section>

    </footer>
    



            
    
        </div>
            
    
    <div id="newsletter-floating" class="sandiego newsletter-floating-new">
        <a class="close-button btn-closer" data-close aria-label="Close modal">
            <span aria-hidden="true">&times;</span>
        </a>
        <div class="as-h2">Want the latest updates on software, tech news, and AI?</div>
        <div class="row">
            <div class="column small-2 medium-2 large-2" style="padding-left: 0px; padding-right: 0px;"><img src="//a.fsdn.com/con/images/sandiego/sf_email_icon.svg"  alt="" /></div>
            <div class="as-h3 column small-10 medium-10 large-10">Get latest updates about software, tech news, and AI from SourceForge directly in your inbox once a month.</div>
        </div>
        <form action="/user/newsletters/" method="post">
            <div class="row">
              <input class="newsletter-input" type="email" placeholder="Enter your email address" autocomplete="off" name="newsletter_email"
                     value="">
              <input type="hidden" name="source" value="floating">
              <button type="submit" class="button blue newsletter-button">Submit</button>
            </div>
        </form>
    </div>
    

            

    
        
    
        <script src="//a.fsdn.com/con/js/min/sf.sandiego-base.js?1746807694"></script>

        
    

    <script>
        /* global Dropzone */
        Dropzone.options.blockthisForm = false;
    </script>
        
    
        <script src="//a.fsdn.com/con/js/min/sf.sandiego-foundation-base.js?1746807694"></script>

        
    


    <script>
        SF.pspRedirect = true;
        SF.pdlpRedirect = true;

        SF.downloadRedirectDelay = 10000;
        SF.cantDownload = false;

        $.extend(SF, {"shortname":"xampp","noCountDown":false,"downloadDelay":5000});
            SF.Ads.enableLeadgenResize = true;
    </script>


        

        <script>
            /* global Foundation */
            $(document).foundation();
            Foundation.Triggers.forceListenersNow();
        </script>


        
        <script>
            bizx.cmp.ifConsent({purposes:'all', vendors:'google-ads'}, function () {
                $('body').removeClass('no-ads-consent'); 
            },
            function () { 
                $('body').addClass('no-ads-consent');
            },
            null,
            function () { 
                $('body').addClass('no-ads-consent');
            });
        </script>

        <noscript><p><img src="https://analytics.slashdotmedia.com/index.php?idsite=39" style="border:0;" alt="" /></p></noscript>

        

        
        

        <script>

        function geturl(url, params) {
            params = Object
                .keys(params)
                .map(function(key) {
                    return key + "=" + encodeURIComponent(params[key]);
                })
                .join("&");
            return url + "?" + params;
        }

        
        function loadimg(params, dimension_prefix) {
            params = Object.assign({"idsite":1,"rec":1,"rand":89669,"dimension2":"pg_dwnld","url":"https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download","action_name":"Download xampp-windows-x64-8.2.12-0-VS16-installer.exe (XAMPP)"}, params);
            for (var key in params) {
                if (params.hasOwnProperty(key) && key.indexOf('dimension') === 0 && params[key] !== undefined) {
                    params[key] = (dimension_prefix||'') + params[key];
                }
            }
            params.rand = Math.floor(Math.random() * 100000);
            bizx.cmp.ifConsent('publisher', ['measurement'], function() {
                var url = geturl("//sourceforge.net/software/visit", params);
                if (!('sendBeacon' in navigator) || !navigator.sendBeacon(url)) {
                    var img = document.createElement('img');
                    img.src = url;
                    img.style = "border:0;position:absolute;top:0;";
                    img.alt = "";
                    document.body.appendChild(img);
                }
            });
        }

        var $typeaheads = $('.typeahead__container input[name=q]');
        $typeaheads.on('typeahead-item-clicked', function(ev, $typeahead, q, item){
            var groupIndex = ($typeahead.data('groups') || []).indexOf(item.group);
            if (groupIndex === 2 || groupIndex === 3) {
                loadimg({
                    'e_c': 'Search | Typeahead | ' + item.group,
                    'e_a': "Typeahead Click | q=" + q,
                    'e_n': item.href, 
                 }, 'typeahead_on_');
            }
        });
        </script>

        <script type="text/javascript">
            bizx.cmp.ifConsent({purposes: 'all', vendors: '6sense'}, async() => {
                
                (function(){var s = document.getElementsByTagName("script")[0];
                var b = document.createElement("script");
                b.type = "text/javascript";b.async = true;b.defer=true;b.id='6senseWebTag';
                b.src = "https://j.6sc.co/j/58729049-be80-466a-9abf-b3911430bbd8.js";
                s.parentNode.insertBefore(b, s);})();
            });
            </script>
    

    <script>
    SF.verbose = SF.verbose || {};
    </script>

    
    
        <script src="//a.fsdn.com/con/js/min/sf.sandiego-dwnld.js?1746807694"></script>

        
    


        

<!-- CCM Tag -->
<script>
    (function () {
        bizx.cmp.ifConsent({ purposes: 'all', vendors: 'bombora'}, function () {
            /*global _ml:true, window */
            _ml = window._ml || {};
            
            _ml.eid = '771';
            _ml.fp = 'aaaae66f-100c-47e8-be02-daa4a8e3489d';  
            var s = document.getElementsByTagName('script')[0], cd = new Date(), mltag = document.createElement('script');
            mltag.type = 'text/javascript';
            mltag.async = true;
            mltag.defer = true;
            mltag.src = '//ml314.com/tag.aspx?' + cd.getDate() + cd.getMonth() + cd.getFullYear();
            s.parentNode.insertBefore(mltag, s);
        });
    })();
</script>
<!-- End CCM Tag -->


        
        
<!-- Hubspot tracking -->


        


        
            
    
    <div id="overlay-blockthis-wrapper" style="display: none;">
        <div id="overlay-blockthis">
            <div class="as-h2 title">Thanks for helping keep SourceForge clean.</div>
            <a href="#" id="btn-blockthis-close">X</a>
            <form class="dropzone small-12" action="/api/instrumentation/gpt" id="blockthisForm" method="POST">
                <div class="row small-12">
                    <div class="column description small-12">
                        <input type="hidden" name="_visit_cookie" value="aaaae66f-100c-47e8-be02-daa4a8e3489d"/>
                            <input type='hidden' name='timestamp' value='1746942969'/>
                            <input type='hidden' name='spinner' value='X9M3i_oqHn67HaPty08TGin_zJvo'/>
                            <p class='Zae32d36185988607c94f8fd86bc54ca71a3dbe41'><label for='Xw7PCpcKNwpDDr8O:wq_CnhDCpBh1JMKdUMOUOT_DmHA'>You seem to have CSS turned off.
             Please don't fill out this field.</label><input id='Xw7PCpcKNwpDDr8O:wq_CnhDCpBh1JMKdUMOUOT_DmHA' name='Xw7LCpcKNwpDDr8O:wq95C8KLw7zChcKKUsKYw4zCsw3CrBs' type=
             'text'/></p>
                            <p class='Zae32d36185988607c94f8fd86bc54ca71a3dbe41'><label for='Xw7PCpcKNwpDDr8O:wq_CnxDCpBh1JMKdUMOUOT_DmHA'>You seem to have CSS turned off.
             Please don't fill out this field.</label><input id='Xw7PCpcKNwpDDr8O:wq_CnxDCpBh1JMKdUMOUOT_DmHA' name='Xw7LCpcKNwpDDr8O:wq55C8KLw7zChcKKUsKYw4zCsw3CrBs' type=
             'text'/></p>
                        Briefly describe the problem (required):
                        <input name="Xw7_CqcKHwo3DqcO1w7bDnsKzAcKUHAQIJcKNwojCqsKwwqQ" type="text" required>
                    </div>
                </div>
                <div class="column small-12">
                    <div class="upload-text">Upload screenshot of ad (required):</div>
                    <div id='upload-it'>
                        <a href="#" id="upload-select-file">Select a file</a>, or drag & drop file here.
                    </div>
                    <div id="upload-it-placeholder"></div> 

                    <div class="dropzone-previews" style="display: none"></div>
                    <div class="dz-message" style="display: none"></div> 
                    
                    <div id="dropzone-preview-template" style="display: none">
                        <div class="dz-preview dz-file-preview">
                            <img data-dz-thumbnail src="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=" alt=""/>
                            <div class="dz-success-mark"><span>✔</span></div>
                            <div class="dz-error-mark"><span>✘</span></div>
                            <div class="dz-error-message"><span data-dz-errormessage></span></div>
                        </div>
                    </div>
                </div>
                <div class="column small-12">
                    <u>Screenshot instructions:</u>
                    
                    <a data-external target=_blank href="http://windows.microsoft.com/en-us/windows/take-screen-capture-print-screen#take-screen-capture-print-screen=windows-8">Windows</a>
                    
                </div>
                <div class="row small-12">
                    <div class="column large-5 small-6">
                        <p>
                            <u>Click URL instructions:</u><br>
                            Right-click on the ad, choose "Copy Link", then paste here &rarr;<br>
                            (This may not be possible with some types of ads)
                        </p>
                        <a class="more-info" href="https://sourceforge.net/p/forge/documentation/Report%20a%20problem%20with%20Ad%20content/" target="_blank">More information about our ad policies</a>
                    </div>
                    <div class="column large-7 small-6">
                        <p>Ad destination/click URL:
                        <input name="Xw73CrsKOwpfDqcOsw4DDm8K1BCzCvjDDgzHDk8Opwq1gNg" type="url" required>
                        </p>
                        <textarea id="gpt-info" name="Xw7DCqcKDworDq1BTTcOAwp_CosOkwo3Cggp0w7USSUo"></textarea>
                        <input class="button" type="submit" id="btn-blockthis-submit" value="Submit Report">
                    </div>
                </div>
            </form>
        </div>
    </div>

        

        <script>
            bizx.cmp.ifConsent('', ['all'], function () {
                bizx.cmp.embedScript('//ads.pro-market.net/ads/scripts/site-143572.js');
            });
        </script><script>
            bizx.cmp.ifConsent('', ['all'], function () {
                try{(function(){ var cb = new Date().getTime(); var s = document.createElement("script"); s.defer = true; s.src = "//tag.crsspxl.com/s1.js?d=2396&cb="+cb; var s0 = document.getElementsByTagName('script')[0]; s0.parentNode.insertBefore(s, s0); })();}catch(e){}
            });
        </script>
    
    
<script type="text/javascript">
    bizx.cmp.ifConsent({ purposes: ['storage', 'measurement'], vendors: 'google-analytics'}, function () {
        /* jshint ignore:start */
        (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
                (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
            m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
        })(window,document,'script','//www.google-analytics.com/analytics.js','ga');
        /* jshint ignore:end */

        window.dataLayer = window.dataLayer || [];
        function gtag(){ window.dataLayer.push(arguments); }
        window.gtag = window.gtag || gtag;
        bizx.cmp.embedScript("https://www.googletagmanager.com/gtag/js");
        gtag('js', new Date());
        gtag('set', {
            'page_location': 'https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download',
        });
    });
</script>
    <script>
        /*global ga, gtag */
        SF.devicePixelRatio = Math.round(window.getDevicePixelRatio()*10)/10;

        

        
            bizx.cmp.ifConsent({ purposes: ['storage', 'measurement'], vendors: 'google-analytics'}, function () {
                gtag('config', 'G-1H226E4E4L', {
                    
                    send_page_view: false,
                    'SF_Project_Shortname': 'xampp', 
                    'SF_Page_Type': 'pg_dwnld', 
                    user_properties: {
                        'SF_Logged_in': 'Logged Out', 
                    },
                    'SF_Ads_Disabled': 'No',   
                    'SF_Prebid_Load_Method': 'sync', 
                    'devicePixelRatio': SF.devicePixelRatio, 
                });
                gtag('event', 'page_view', { send_to: 'G-1H226E4E4L' });
            });
        
            
            
        
        
    </script>
    

        
        
        
             <script>
    $(function() {
        bizx.cmp.ifConsent({ purposes: 'all' , vendors: 'narrative'}, function() {
            var current_time = (new Date()).getTime();
            let imageUrl = "https://io.narrative.io/?companyId=2440&id=first_party%3Aaaaae66f-100c-47e8-be02-daa4a8e3489d&id=site_name%3Asourceforge.net&id=url%3A%2Fprojects%2Fxampp%2Ffiles%2FXAMPP%2520Windows%2F8.2.12%2Fxampp-windows-x64-8.2.12-0-VS16-installer.exe%2Fdownload&id=pagetitle%3ADownload+xampp-windows-x64-8.2.12-0-VS16-installer.exe+%28XAMPP%29&id=vertical%3AOpen+Source+Software&id=product%3AXAMPP&id=topics%3ADatabase+Engines%2FServers%2CSite+Management%2CHTTP+Servers%2CInternet&id=programming_language%3APerl%2CPHP&id=action%3ADownloaded+XAMPP&id=action%3ADownloaded+Database+Engines%2FServers+Software";
            imageUrl = imageUrl.replace(encodeURIComponent("$PAGE_TITLE"), document.title);  
            
            imageUrl = URL.parse(imageUrl);
            let hem = bizx.uids.getHem();
            if (hem) {
                imageUrl.searchParams.append('id', 'hem:' + hem);
            }
            imageUrl.searchParams.append('rand', current_time);

            var image = new Image();
            image.src = imageUrl.toString();
            image.style.display = "none";
            image.style.height = 0;
            image.style.width = 0;
            document.body.appendChild(image);
        });
    })
</script>
        

        
            
<script>
    $(function() {
        bizx.cmp.ifConsent({ purposes: 'all', vendors: 'xandr'}, function() {
            /*jshint ignore:start*/
            !function(e,i){if(!e.pixie){var n=e.pixie=function(e,i,a){n.actionQueue.push({action:e,actionValue:i,params:a})};n.actionQueue=[];var a=i.createElement("script");a.async=!0,a.src="//acdn.adnxs.com/dmp/up/pixie.js";var t=i.getElementsByTagName("head")[0];t.insertBefore(a,t.firstChild)}}(window,document);
            pixie('init', '48d4c5e5-03de-40f8-81ab-b370a8860afa');
            pixie('event', 'PageView');
            /*jshint ignore:end*/

        });
    });
</script>

        

        









<script>
        function gam(id){
            bizx.cmp.ifConsent({ purposes: 'all', vendors: 'google-ads'}, function () {
                bizx.cmp.embedScript(`https://pagead2.googlesyndication.com/pagead/js/pcd.js?${id}`,
                    true,  // async
                    'head', // location
                    null, // callback
                    false, // defer
                    {id: `google-pcd-tag-${id}`, 'data-audience-pixel': 'dc_iu=/41014381/DFPAudiencePixel;dc_seg=' + id});
            });
        }

        
            gam("8901705213");gam("8901712041");</script>

        
        








        
        
        
            
            
            
        
        
    </body>
</html>