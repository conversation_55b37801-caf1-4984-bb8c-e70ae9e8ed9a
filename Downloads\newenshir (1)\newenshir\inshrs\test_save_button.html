<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="test-token">
    <title>اختبار زر الحفظ</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .test-title {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: bold;
        }
        
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 اختبار زر الحفظ - تشخيص المشاكل</h1>
        
        <!-- اختبار CSRF Token -->
        <div class="test-section">
            <div class="test-title">1️⃣ اختبار CSRF Token</div>
            <button class="test-button" onclick="testCSRF()">اختبار CSRF</button>
            <div id="csrf-result" class="test-result"></div>
        </div>
        
        <!-- اختبار الروابط -->
        <div class="test-section">
            <div class="test-title">2️⃣ اختبار الروابط</div>
            <button class="test-button" onclick="testRoutes()">اختبار الروابط</button>
            <div id="routes-result" class="test-result"></div>
        </div>
        
        <!-- اختبار زر الحفظ -->
        <div class="test-section">
            <div class="test-title">3️⃣ اختبار زر الحفظ</div>
            
            <!-- زر الحفظ التجريبي -->
            <div class="save-button-container" data-item-type="ad" data-item-id="1">
                <button type="button" class="save-button" title="حفظ العنصر">
                    <i class="save-icon fas fa-bookmark"></i>
                    <span class="save-text">حفظ</span>
                </button>
            </div>
            
            <div id="save-result" class="test-result"></div>
        </div>
        
        <!-- سجل الأحداث -->
        <div class="test-section">
            <div class="test-title">4️⃣ سجل الأحداث</div>
            <button class="test-button" onclick="clearLog()">مسح السجل</button>
            <div id="log" class="log-area"></div>
        </div>
    </div>

    <!-- تضمين CSS زر الحفظ -->
    <style>
        .save-button-container {
            display: inline-block;
            position: relative;
            margin: 10px;
        }
        
        .save-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.5rem 0.75rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.875rem;
            box-shadow: 0 2px 6px rgba(108, 117, 125, 0.3);
        }
        
        .save-button.saved {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            box-shadow: 0 2px 6px rgba(40, 167, 69, 0.3);
        }
        
        .save-button.loading {
            pointer-events: none;
            opacity: 0.8;
        }
        
        .save-icon {
            font-size: 1rem;
            margin-left: 0.375rem;
        }
        
        .save-text {
            font-size: 0.875rem;
            font-weight: 600;
        }
    </style>

    <script>
        // دالة تسجيل الأحداث
        function log(message, type = 'info') {
            const logArea = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;
            
            console.log(`[Save Button Test] ${logEntry.trim()}`);
        }
        
        // مسح السجل
        function clearLog() {
            document.getElementById('log').textContent = '';
        }
        
        // اختبار CSRF Token
        function testCSRF() {
            const resultDiv = document.getElementById('csrf-result');
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            
            if (csrfToken && csrfToken.getAttribute('content')) {
                resultDiv.className = 'test-result success';
                resultDiv.textContent = '✅ CSRF Token موجود: ' + csrfToken.getAttribute('content');
                log('CSRF Token موجود ومتاح', 'success');
            } else {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = '❌ CSRF Token غير موجود';
                log('CSRF Token غير موجود', 'error');
            }
        }
        
        // اختبار الروابط
        async function testRoutes() {
            const resultDiv = document.getElementById('routes-result');
            const routes = [
                { url: '/saved/check', method: 'POST', name: 'فحص الحالة' },
                { url: '/saved/toggle', method: 'POST', name: 'تبديل الحفظ' },
                { url: '/saved', method: 'GET', name: 'صفحة المحفوظات' }
            ];
            
            resultDiv.className = 'test-result info';
            resultDiv.textContent = '🔄 جاري اختبار الروابط...';
            log('بدء اختبار الروابط', 'info');
            
            let results = [];
            
            for (const route of routes) {
                try {
                    log(`اختبار ${route.name}: ${route.method} ${route.url}`, 'info');
                    
                    const response = await fetch(route.url, {
                        method: route.method,
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 'test-token'
                        },
                        body: route.method === 'POST' ? JSON.stringify({
                            item_type: 'ad',
                            item_id: 1
                        }) : undefined
                    });
                    
                    if (response.ok) {
                        results.push(`✅ ${route.name}: متاح`);
                        log(`${route.name}: متاح (${response.status})`, 'success');
                    } else {
                        results.push(`⚠️ ${route.name}: ${response.status} ${response.statusText}`);
                        log(`${route.name}: خطأ ${response.status} - ${response.statusText}`, 'warning');
                    }
                } catch (error) {
                    results.push(`❌ ${route.name}: خطأ في الاتصال`);
                    log(`${route.name}: خطأ في الاتصال - ${error.message}`, 'error');
                }
            }
            
            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = results.join('<br>');
        }
        
        // تفعيل زر الحفظ
        document.addEventListener('DOMContentLoaded', function() {
            log('تم تحميل الصفحة', 'info');
            
            // إضافة مستمع الأحداث لزر الحفظ
            document.addEventListener('click', function(e) {
                if (e.target.closest('.save-button')) {
                    e.preventDefault();
                    log('تم الضغط على زر الحفظ', 'info');
                    handleSaveClick(e.target.closest('.save-button-container'));
                }
            });
            
            // اختبار تلقائي للـ CSRF
            testCSRF();
        });
        
        // دالة معالجة الضغط على زر الحفظ
        async function handleSaveClick(container) {
            const itemType = container.dataset.itemType;
            const itemId = container.dataset.itemId;
            const button = container.querySelector('.save-button');
            const icon = button.querySelector('.save-icon');
            const text = button.querySelector('.save-text');
            const resultDiv = document.getElementById('save-result');
            
            log(`بدء عملية الحفظ: ${itemType} ID ${itemId}`, 'info');
            
            // إضافة حالة التحميل
            button.classList.add('loading');
            icon.classList.add('fa-spinner', 'fa-spin');
            icon.classList.remove('fa-bookmark');
            
            resultDiv.className = 'test-result info';
            resultDiv.textContent = '🔄 جاري الحفظ...';
            
            try {
                const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
                
                if (!csrfToken) {
                    throw new Error('CSRF Token غير موجود');
                }
                
                log('إرسال طلب الحفظ إلى /saved/toggle', 'info');
                
                const response = await fetch('/saved/toggle', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: JSON.stringify({
                        item_type: itemType,
                        item_id: parseInt(itemId)
                    })
                });
                
                log(`استجابة الخادم: ${response.status} ${response.statusText}`, 'info');
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
                
                const data = await response.json();
                log(`بيانات الاستجابة: ${JSON.stringify(data)}`, 'info');
                
                // إزالة حالة التحميل
                button.classList.remove('loading');
                icon.classList.remove('fa-spinner', 'fa-spin');
                icon.classList.add('fa-bookmark');
                
                if (data.success) {
                    if (data.saved) {
                        // تم الحفظ
                        button.classList.add('saved');
                        text.textContent = 'محفوظ';
                        icon.classList.remove('far');
                        icon.classList.add('fas');
                        
                        resultDiv.className = 'test-result success';
                        resultDiv.textContent = '✅ تم حفظ العنصر بنجاح';
                        log('تم حفظ العنصر بنجاح', 'success');
                    } else {
                        // تم إلغاء الحفظ
                        button.classList.remove('saved');
                        text.textContent = 'حفظ';
                        icon.classList.remove('fas');
                        icon.classList.add('far');
                        
                        resultDiv.className = 'test-result success';
                        resultDiv.textContent = '✅ تم إلغاء حفظ العنصر';
                        log('تم إلغاء حفظ العنصر', 'success');
                    }
                } else {
                    throw new Error(data.message || 'فشل في العملية');
                }
                
            } catch (error) {
                log(`خطأ في عملية الحفظ: ${error.message}`, 'error');
                
                // إزالة حالة التحميل
                button.classList.remove('loading');
                icon.classList.remove('fa-spinner', 'fa-spin');
                icon.classList.add('fa-bookmark');
                
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `❌ خطأ: ${error.message}`;
                
                // تفاصيل إضافية للتشخيص
                if (error.message.includes('404')) {
                    resultDiv.innerHTML += '<br>💡 المشكلة: الرابط غير موجود - تحقق من الروابط في routes/web.php';
                } else if (error.message.includes('419')) {
                    resultDiv.innerHTML += '<br>💡 المشكلة: CSRF Token غير صحيح';
                } else if (error.message.includes('500')) {
                    resultDiv.innerHTML += '<br>💡 المشكلة: خطأ في الخادم - تحقق من logs';
                } else if (error.message.includes('NetworkError')) {
                    resultDiv.innerHTML += '<br>💡 المشكلة: مشكلة في الاتصال بالخادم';
                }
            }
        }
    </script>
</body>
</html>
