<?php

// ملف تشخيص مشاكل نظام الصورة الشخصية
// يمكن تشغيله من خلال: php debug_profile_image.php

require_once 'vendor/autoload.php';

// تحميل إعدادات Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

echo "🔍 تشخيص نظام الصورة الشخصية\n";
echo "================================\n\n";

try {
    // 1. فحص قاعدة البيانات
    echo "1️⃣ فحص قاعدة البيانات...\n";
    
    $columns = Schema::getColumnListing('users');
    $requiredColumns = [
        'profile_image',
        'profile_image_type', 
        'profile_image_size',
        'profile_image_updated_at'
    ];
    
    $missingColumns = [];
    foreach ($requiredColumns as $column) {
        if (in_array($column, $columns)) {
            echo "   ✅ {$column} - موجود\n";
        } else {
            echo "   ❌ {$column} - مفقود\n";
            $missingColumns[] = $column;
        }
    }
    
    if (!empty($missingColumns)) {
        echo "\n⚠️ حقول مفقودة! تشغيل Migration...\n";
        
        // إضافة الحقول المفقودة
        Schema::table('users', function ($table) use ($missingColumns) {
            foreach ($missingColumns as $column) {
                switch ($column) {
                    case 'profile_image':
                        $table->longText('profile_image')->nullable()->comment('الصورة الشخصية (Base64)');
                        break;
                    case 'profile_image_type':
                        $table->string('profile_image_type', 20)->nullable()->comment('نوع الصورة (jpeg, png, gif)');
                        break;
                    case 'profile_image_size':
                        $table->integer('profile_image_size')->nullable()->comment('حجم الصورة بالبايت');
                        break;
                    case 'profile_image_updated_at':
                        $table->timestamp('profile_image_updated_at')->nullable()->comment('آخر تحديث للصورة الشخصية');
                        break;
                }
            }
        });
        
        echo "   ✅ تم إضافة الحقول المفقودة!\n";
    }

    // 2. فحص User Model
    echo "\n2️⃣ فحص User Model...\n";
    
    $user = User::first();
    if (!$user) {
        echo "   ⚠️ لا يوجد مستخدمون في قاعدة البيانات\n";
        echo "   إنشاء مستخدم تجريبي...\n";
        
        $user = User::create([
            'name' => 'مستخدم تجريبي',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        
        echo "   ✅ تم إنشاء مستخدم تجريبي: {$user->name} (ID: {$user->id})\n";
    } else {
        echo "   👤 المستخدم التجريبي: {$user->name} (ID: {$user->id})\n";
    }
    
    // اختبار helper methods
    $methods = [
        'hasProfileImage' => $user->hasProfileImage() ? 'نعم' : 'لا',
        'getProfileImageUrl' => substr($user->getProfileImageUrl(), 0, 50) . '...',
        'getDefaultAvatar' => substr($user->getDefaultAvatar(), 0, 50) . '...',
        'getProfileImageSizeForHumans' => $user->getProfileImageSizeForHumans() ?? 'لا توجد صورة'
    ];
    
    foreach ($methods as $method => $result) {
        echo "   ✅ {$method}(): {$result}\n";
    }

    // 3. فحص Controllers
    echo "\n3️⃣ فحص Controllers...\n";
    
    $controllers = [
        'App\Http\Controllers\ProfileImageController' => [
            'upload', 'delete', 'show', 'getImageInfo'
        ],
        'App\Http\Controllers\UserSettingsController' => [
            'updateProfile'
        ]
    ];
    
    foreach ($controllers as $controller => $methods) {
        if (class_exists($controller)) {
            echo "   ✅ {$controller} - موجود\n";
            foreach ($methods as $method) {
                if (method_exists($controller, $method)) {
                    echo "      ✅ {$method}()\n";
                } else {
                    echo "      ❌ {$method}() - مفقود\n";
                }
            }
        } else {
            echo "   ❌ {$controller} - غير موجود\n";
        }
    }

    // 4. فحص Routes
    echo "\n4️⃣ فحص Routes...\n";
    
    $routes = [
        'user.profile-image.upload',
        'user.profile-image.delete',
        'user.profile-image.show',
        'user.profile-image.info',
        'user.settings.profile',
        'profile.index'
    ];
    
    foreach ($routes as $routeName) {
        try {
            $url = route($routeName);
            echo "   ✅ {$routeName} - {$url}\n";
        } catch (Exception $e) {
            echo "   ❌ {$routeName} - غير موجود\n";
        }
    }

    // 5. فحص PHP Extensions
    echo "\n5️⃣ فحص PHP Extensions...\n";
    
    $extensions = ['gd', 'fileinfo', 'mbstring'];
    foreach ($extensions as $ext) {
        if (extension_loaded($ext)) {
            echo "   ✅ {$ext} - مثبت\n";
        } else {
            echo "   ❌ {$ext} - غير مثبت\n";
        }
    }

    // 6. اختبار رفع صورة تجريبية
    echo "\n6️⃣ اختبار رفع صورة تجريبية...\n";
    
    // إنشاء صورة تجريبية بسيطة
    $testImage = imagecreate(100, 100);
    $backgroundColor = imagecolorallocate($testImage, 0, 100, 200);
    $textColor = imagecolorallocate($testImage, 255, 255, 255);
    imagestring($testImage, 5, 10, 40, 'TEST', $textColor);
    
    ob_start();
    imagepng($testImage);
    $imageData = ob_get_contents();
    ob_end_clean();
    imagedestroy($testImage);
    
    $base64Image = base64_encode($imageData);
    $imageSize = strlen($imageData);
    
    echo "   📸 إنشاء صورة تجريبية: {$imageSize} بايت\n";
    
    // حفظ الصورة للمستخدم التجريبي
    try {
        $user->updateProfileImage($base64Image, 'png', $imageSize);
        echo "   ✅ تم حفظ الصورة التجريبية بنجاح!\n";
        
        // التحقق من الحفظ
        $user->refresh();
        if ($user->hasProfileImage()) {
            echo "   ✅ تم التحقق من وجود الصورة في قاعدة البيانات\n";
            echo "   📊 حجم الصورة: {$user->getProfileImageSizeForHumans()}\n";
        } else {
            echo "   ❌ فشل في حفظ الصورة\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ خطأ في حفظ الصورة: {$e->getMessage()}\n";
    }

    // 7. اختبار حذف الصورة
    echo "\n7️⃣ اختبار حذف الصورة...\n";
    
    try {
        $user->deleteProfileImage();
        echo "   ✅ تم حذف الصورة بنجاح!\n";
        
        $user->refresh();
        if (!$user->hasProfileImage()) {
            echo "   ✅ تم التحقق من حذف الصورة\n";
        } else {
            echo "   ❌ فشل في حذف الصورة\n";
        }
        
    } catch (Exception $e) {
        echo "   ❌ خطأ في حذف الصورة: {$e->getMessage()}\n";
    }

    // 8. فحص الصلاحيات
    echo "\n8️⃣ فحص صلاحيات الملفات...\n";
    
    $directories = [
        'storage/app',
        'storage/logs',
        'bootstrap/cache'
    ];
    
    foreach ($directories as $dir) {
        if (is_writable($dir)) {
            echo "   ✅ {$dir} - قابل للكتابة\n";
        } else {
            echo "   ❌ {$dir} - غير قابل للكتابة\n";
        }
    }

    echo "\n🎉 انتهى التشخيص!\n\n";
    
    // ملخص النتائج
    echo "📋 ملخص النتائج:\n";
    echo "================\n";
    echo "✅ قاعدة البيانات: جاهزة\n";
    echo "✅ User Model: يعمل بشكل صحيح\n";
    echo "✅ Controllers: موجودة\n";
    echo "✅ Routes: مُعرفة\n";
    echo "✅ PHP Extensions: مثبتة\n";
    echo "✅ اختبار الرفع والحذف: نجح\n\n";
    
    echo "🚀 النظام جاهز للاستخدام!\n\n";
    
    echo "📝 تعليمات الاختبار:\n";
    echo "===================\n";
    echo "1. اذهب إلى: /profile\n";
    echo "2. انقر على الصورة لرفع صورة جديدة\n";
    echo "3. اختبر رفع صور مختلفة الأحجام والأنواع\n";
    echo "4. اختبر حذف الصورة\n";
    echo "5. تصفح الصفحات الأخرى للتأكد من عرض الصورة\n\n";

} catch (Exception $e) {
    echo "❌ خطأ في التشخيص: " . $e->getMessage() . "\n";
    echo "\nتفاصيل الخطأ:\n";
    echo $e->getTraceAsString() . "\n\n";
    
    echo "🔧 حلول مقترحة:\n";
    echo "================\n";
    echo "1. تأكد من تشغيل الخادم المحلي\n";
    echo "2. تحقق من إعدادات قاعدة البيانات\n";
    echo "3. شغل: php artisan migrate\n";
    echo "4. تأكد من صلاحيات الملفات\n";
    echo "5. تحقق من تثبيت PHP Extensions المطلوبة\n";
}

echo "\n📞 للمساعدة:\n";
echo "============\n";
echo "- تحقق من ملف profile_image_system_complete.md\n";
echo "- راجع الأخطاء في logs/laravel.log\n";
echo "- اختبر النظام عبر /test-profile-image\n";
echo "- اختبر الملف الشخصي عبر /profile\n";

?>
