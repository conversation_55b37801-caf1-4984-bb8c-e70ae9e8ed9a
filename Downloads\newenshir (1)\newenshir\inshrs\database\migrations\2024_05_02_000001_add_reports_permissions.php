<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Permission;

class AddReportsPermissions extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // إضافة صلاحيات البلاغات
        $permissions = [
            [
                'name' => 'reports.view',
                'display_name' => 'عرض البلاغات',
                'group' => 'reports',
                'description' => 'السماح بعرض البلاغات المقدمة من المستخدمين'
            ],
            [
                'name' => 'reports.manage',
                'display_name' => 'إدارة البلاغات',
                'group' => 'reports',
                'description' => 'السماح بإدارة البلاغات وتغيير حالتها'
            ],
            [
                'name' => 'reports.delete',
                'display_name' => 'حذف البلاغات',
                'group' => 'reports',
                'description' => 'السماح بحذف البلاغات من النظام'
            ],
            [
                'name' => 'reports.export',
                'display_name' => 'تصدير البلاغات',
                'group' => 'reports',
                'description' => 'السماح بتصدير بيانات البلاغات'
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name']],
                $permission
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // حذف صلاحيات البلاغات
        Permission::whereIn('name', [
            'reports.view',
            'reports.manage',
            'reports.delete',
            'reports.export',
        ])->delete();
    }
}
