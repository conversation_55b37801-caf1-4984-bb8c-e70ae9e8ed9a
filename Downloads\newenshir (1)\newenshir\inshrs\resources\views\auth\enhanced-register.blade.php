<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>إنشاء حساب جديد - {{ config('app.name') }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            text-align: right;
        }
        
        .register-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }
        
        .register-card {
            background: white;
            border-radius: 1.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            animation: slideUp 0.6s ease;
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .register-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .register-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .register-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }
        
        .register-body {
            padding: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }
        
        .form-label i {
            margin-left: 0.5rem;
            color: #667eea;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .form-control.is-valid {
            border-color: #28a745;
        }
        
        .form-control.is-invalid {
            border-color: #dc3545;
        }
        
        .btn-register {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 0.75rem;
            padding: 0.75rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-register:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-register .spinner {
            display: none;
        }
        
        .btn-register.loading .spinner {
            display: inline-block;
        }
        
        .btn-register.loading .btn-text {
            display: none;
        }
        
        .register-footer {
            text-align: center;
            padding: 1rem 2rem 2rem;
            border-top: 1px solid #f8f9fa;
        }
        
        .register-footer a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .register-footer a:hover {
            text-decoration: underline;
        }
        
        .progress-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
            padding: 0 1rem;
        }
        
        .step {
            flex: 1;
            text-align: center;
            position: relative;
        }
        
        .step::before {
            content: '';
            position: absolute;
            top: 15px;
            right: 50%;
            width: 100%;
            height: 2px;
            background: #e9ecef;
            z-index: 1;
        }
        
        .step:last-child::before {
            display: none;
        }
        
        .step-circle {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 0.5rem;
            font-size: 0.875rem;
            font-weight: 600;
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
        }
        
        .step.active .step-circle {
            background: #667eea;
            color: white;
        }
        
        .step.completed .step-circle {
            background: #28a745;
            color: white;
        }
        
        .step.completed::before {
            background: #28a745;
        }
        
        .step-label {
            font-size: 0.75rem;
            color: #6c757d;
            font-weight: 500;
        }
        
        .step.active .step-label {
            color: #667eea;
            font-weight: 600;
        }
        
        .step.completed .step-label {
            color: #28a745;
            font-weight: 600;
        }
        
        .terms-checkbox {
            background: #f8f9fa;
            border-radius: 0.75rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .terms-checkbox .form-check {
            margin: 0;
        }
        
        .terms-checkbox .form-check-input {
            margin-left: 0.5rem;
        }
        
        .terms-checkbox .form-check-label {
            font-size: 0.9rem;
            color: #495057;
        }
        
        .terms-checkbox .form-check-label a {
            color: #667eea;
            text-decoration: none;
        }
        
        .terms-checkbox .form-check-label a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .register-container {
                padding: 1rem;
            }
            
            .register-card {
                margin: 0;
            }
            
            .register-header,
            .register-body {
                padding: 1.5rem;
            }
            
            .progress-steps {
                padding: 0;
            }
            
            .step-label {
                font-size: 0.7rem;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-card">
            <!-- Header -->
            <div class="register-header">
                <h1>
                    <i class="fas fa-user-plus me-2"></i>
                    إنشاء حساب جديد
                </h1>
                <p>انضم إلى منصة انشر واستمتع بخدماتنا المميزة</p>
            </div>
            
            <!-- Progress Steps -->
            <div class="progress-steps">
                <div class="step active" id="step-1">
                    <div class="step-circle">1</div>
                    <div class="step-label">المعلومات الأساسية</div>
                </div>
                <div class="step" id="step-2">
                    <div class="step-circle">2</div>
                    <div class="step-label">كلمة المرور</div>
                </div>
                <div class="step" id="step-3">
                    <div class="step-circle">3</div>
                    <div class="step-label">التحقق</div>
                </div>
            </div>
            
            <!-- Body -->
            <div class="register-body">
                <form id="registerForm" method="POST" action="{{ route('register') }}">
                    @csrf
                    
                    <!-- Step 1: Basic Information -->
                    <div class="step-content" id="step-content-1">
                        <div class="form-group">
                            <label for="name" class="form-label">
                                <i class="fas fa-user"></i>
                                الاسم الكامل
                            </label>
                            <input type="text" 
                                   class="form-control" 
                                   id="name" 
                                   name="name" 
                                   value="{{ old('name') }}" 
                                   required 
                                   autocomplete="name"
                                   placeholder="أدخل اسمك الكامل">
                        </div>
                        
                        <div class="form-group">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope"></i>
                                البريد الإلكتروني
                            </label>
                            <input type="email" 
                                   class="form-control" 
                                   id="email" 
                                   name="email" 
                                   value="{{ old('email') }}" 
                                   required 
                                   autocomplete="email"
                                   placeholder="<EMAIL>">
                            
                            <!-- Email Validator Component -->
                            @include('components.email-validator', [
                                'inputId' => 'email',
                                'checkAvailability' => true,
                                'realTimeValidation' => true,
                                'showSuggestions' => true
                            ])
                        </div>
                        
                        <button type="button" class="btn btn-register" onclick="nextStep(2)">
                            <span class="btn-text">التالي</span>
                            <i class="fas fa-arrow-left ms-2"></i>
                        </button>
                    </div>
                    
                    <!-- Step 2: Password -->
                    <div class="step-content" id="step-content-2" style="display: none;">
                        <div class="form-group">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock"></i>
                                كلمة المرور
                            </label>
                            <input type="password" 
                                   class="form-control" 
                                   id="password" 
                                   name="password" 
                                   required 
                                   autocomplete="new-password"
                                   placeholder="أدخل كلمة مرور قوية">
                            
                            <!-- Password Strength Component -->
                            @include('components.password-strength', [
                                'inputId' => 'password',
                                'showRequirements' => true,
                                'showStrengthBar' => true,
                                'minLength' => 8,
                                'requireUppercase' => true,
                                'requireLowercase' => true,
                                'requireNumbers' => true,
                                'requireSpecialChars' => true
                            ])
                        </div>
                        
                        <div class="form-group">
                            <label for="password_confirmation" class="form-label">
                                <i class="fas fa-lock"></i>
                                تأكيد كلمة المرور
                            </label>
                            <input type="password" 
                                   class="form-control" 
                                   id="password_confirmation" 
                                   name="password_confirmation" 
                                   required 
                                   autocomplete="new-password"
                                   placeholder="أعد إدخال كلمة المرور">
                            <div id="password-match-status" class="mt-2" style="display: none;"></div>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-secondary" onclick="prevStep(1)">
                                <i class="fas fa-arrow-right me-2"></i>
                                السابق
                            </button>
                            <button type="button" class="btn btn-register flex-fill" onclick="nextStep(3)">
                                <span class="btn-text">التالي</span>
                                <i class="fas fa-arrow-left ms-2"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Step 3: Verification -->
                    <div class="step-content" id="step-content-3" style="display: none;">
                        <div class="terms-checkbox">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                                <label class="form-check-label" for="terms">
                                    أوافق على 
                                    <a href="{{ route('terms-and-conditions') }}" target="_blank">الشروط والأحكام</a>
                                    و
                                    <a href="{{ route('privacy') }}" target="_blank">سياسة الخصوصية</a>
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="newsletter" name="newsletter">
                            <label class="form-check-label" for="newsletter">
                                أرغب في تلقي النشرة الإخبارية والعروض الخاصة
                            </label>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-secondary" onclick="prevStep(2)">
                                <i class="fas fa-arrow-right me-2"></i>
                                السابق
                            </button>
                            <button type="submit" class="btn btn-register flex-fill" id="submitBtn">
                                <span class="spinner">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    جاري الإنشاء...
                                </span>
                                <span class="btn-text">
                                    <i class="fas fa-user-check me-2"></i>
                                    إنشاء الحساب
                                </span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- Footer -->
            <div class="register-footer">
                <p class="mb-0">
                    لديك حساب بالفعل؟ 
                    <a href="{{ route('login') }}">تسجيل الدخول</a>
                </p>
            </div>
        </div>
    </div>

    <!-- Notification System -->
    @include('components.notification-system', [
        'type' => 'info',
        'title' => 'مرحباً بك!',
        'message' => 'يرجى ملء جميع الحقول المطلوبة لإنشاء حسابك',
        'position' => 'top-right',
        'autoHide' => true,
        'duration' => 3000,
        'id' => 'welcome-notification'
    ])

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentStep = 1;
        const totalSteps = 3;
        
        // إظهار الإشعار الترحيبي
        document.addEventListener('DOMContentLoaded', function() {
            showNotification('welcome-notification');
        });
        
        function nextStep(step) {
            if (validateCurrentStep()) {
                showStep(step);
            }
        }
        
        function prevStep(step) {
            showStep(step);
        }
        
        function showStep(step) {
            // إخفاء جميع الخطوات
            for (let i = 1; i <= totalSteps; i++) {
                document.getElementById(`step-content-${i}`).style.display = 'none';
                document.getElementById(`step-${i}`).classList.remove('active', 'completed');
            }
            
            // إظهار الخطوة الحالية
            document.getElementById(`step-content-${step}`).style.display = 'block';
            document.getElementById(`step-${step}`).classList.add('active');
            
            // تحديد الخطوات المكتملة
            for (let i = 1; i < step; i++) {
                document.getElementById(`step-${i}`).classList.add('completed');
            }
            
            currentStep = step;
        }
        
        function validateCurrentStep() {
            switch (currentStep) {
                case 1:
                    return validateStep1();
                case 2:
                    return validateStep2();
                case 3:
                    return validateStep3();
                default:
                    return true;
            }
        }
        
        function validateStep1() {
            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            
            if (!name) {
                showErrorNotification('يرجى إدخال الاسم الكامل');
                return false;
            }
            
            if (!email) {
                showErrorNotification('يرجى إدخال البريد الإلكتروني');
                return false;
            }
            
            if (!validateEmailFormat(email)) {
                showErrorNotification('تنسيق البريد الإلكتروني غير صحيح');
                return false;
            }
            
            return true;
        }
        
        function validateStep2() {
            const password = document.getElementById('password').value;
            const passwordConfirmation = document.getElementById('password_confirmation').value;
            
            if (!password) {
                showErrorNotification('يرجى إدخال كلمة المرور');
                return false;
            }
            
            if (password !== passwordConfirmation) {
                showErrorNotification('كلمات المرور غير متطابقة');
                return false;
            }
            
            // التحقق من قوة كلمة المرور
            const passwordResult = window[`checkPassword_password`]();
            if (!passwordResult.isValid) {
                showErrorNotification('كلمة المرور لا تلبي المتطلبات المطلوبة');
                return false;
            }
            
            return true;
        }
        
        function validateStep3() {
            const terms = document.getElementById('terms').checked;
            
            if (!terms) {
                showErrorNotification('يجب الموافقة على الشروط والأحكام');
                return false;
            }
            
            return true;
        }
        
        function validateEmailFormat(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        function showErrorNotification(message) {
            // إنشاء إشعار خطأ ديناميكي
            const notificationId = 'error-' + Date.now();
            const notificationHtml = `
                <div id="${notificationId}" class="notification-alert notification-error notification-top-right" style="display: none;">
                    <div class="notification-content">
                        <div class="notification-header">
                            <i class="notification-icon fas fa-times-circle"></i>
                            <h6 class="notification-title">خطأ</h6>
                            <button type="button" class="notification-close" onclick="hideNotification('${notificationId}')">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <p class="notification-message">${message}</p>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', notificationHtml);
            showNotification(notificationId);
            
            // إزالة الإشعار بعد 5 ثوان
            setTimeout(() => {
                const element = document.getElementById(notificationId);
                if (element) {
                    element.remove();
                }
            }, 5000);
        }
        
        // التحقق من تطابق كلمات المرور
        document.getElementById('password_confirmation').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmation = this.value;
            const statusDiv = document.getElementById('password-match-status');
            
            if (confirmation) {
                statusDiv.style.display = 'block';
                if (password === confirmation) {
                    statusDiv.innerHTML = '<div class="text-success"><i class="fas fa-check-circle me-1"></i> كلمات المرور متطابقة</div>';
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else {
                    statusDiv.innerHTML = '<div class="text-danger"><i class="fas fa-times-circle me-1"></i> كلمات المرور غير متطابقة</div>';
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            } else {
                statusDiv.style.display = 'none';
                this.classList.remove('is-valid', 'is-invalid');
            }
        });
        
        // معالجة إرسال النموذج
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();

            if (!validateCurrentStep()) {
                return;
            }

            const submitBtn = document.getElementById('submitBtn');
            submitBtn.classList.add('loading');
            submitBtn.disabled = true;

            // إرسال النموذج
            const formData = new FormData(this);

            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccessNotification('تم إنشاء الحساب بنجاح! جاري التوجيه...');
                    setTimeout(() => {
                        window.location.href = data.redirect || '/dashboard';
                    }, 2000);
                } else {
                    showErrorNotification(data.message || 'حدث خطأ أثناء إنشاء الحساب');
                    submitBtn.classList.remove('loading');
                    submitBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorNotification('حدث خطأ في الشبكة. يرجى المحاولة مرة أخرى.');
                submitBtn.classList.remove('loading');
                submitBtn.disabled = false;
            });
        });

        function showSuccessNotification(message) {
            const notificationId = 'success-' + Date.now();
            const notificationHtml = `
                <div id="${notificationId}" class="notification-alert notification-success notification-center" style="display: none;">
                    <div class="notification-content">
                        <div class="notification-header">
                            <i class="notification-icon fas fa-check-circle"></i>
                            <h6 class="notification-title">نجح!</h6>
                        </div>
                        <p class="notification-message">${message}</p>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', notificationHtml);
            showNotification(notificationId);
        }
    </script>
</body>
</html>
