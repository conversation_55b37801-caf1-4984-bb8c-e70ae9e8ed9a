<?php

namespace App\Http\Requests;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProfileUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return auth()->check(); // التأكد من أن المستخدم مسجل الدخول
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'email',
                'max:255',
                Rule::unique(User::class)->ignore($this->user()->id),
            ],
            'company' => ['nullable', 'array'], // nullable للسماح بعدم إرسال الخبرات
            'company.*' => ['nullable', 'string', 'max:255'],
            'position' => ['nullable', 'array'],
            'position.*' => ['nullable', 'string', 'max:255'],
            'start_date' => ['nullable', 'array'],
            'start_date.*' => [
                'nullable',
                'date',
                'before_or_equal:today', // التأكد من أن التاريخ ليس مستقبلياً
            ],
            'end_date' => ['nullable', 'array'],
            'end_date.*' => [
                'nullable',
                'date',
                'after_or_equal:start_date.*', // التأكد من أن تاريخ النهاية بعد البداية
                'before_or_equal:today', // التأكد من أن التاريخ ليس مستقبلياً
            ],
            'description' => ['nullable', 'array'],
            'description.*' => ['nullable', 'string', 'max:1000'], // حد أقصى معقول للوصف
        ];
    }

    /**
     * Get custom messages for validation errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'الاسم مطلوب',
            'name.string' => 'الاسم يجب أن يكون نصاً',
            'name.max' => 'الاسم لا يمكن أن يتجاوز 255 حرفاً',
            'email.required' => 'البريد الإلكتروني مطلوب',
            'email.email' => 'يجب أن يكون البريد الإلكتروني صالحاً',
            'email.max' => 'البريد الإلكتروني لا يمكن أن يتجاوز 255 حرفاً',
            'email.unique' => 'البريد الإلكتروني مستخدم بالفعل',
            'company.*.string' => 'اسم الشركة يجب أن يكون نصاً',
            'company.*.max' => 'اسم الشركة لا يمكن أن يتجاوز 255 حرفاً',
            'position.*.string' => 'المسمى الوظيفي يجب أن يكون نصاً',
            'position.*.max' => 'المسمى الوظيفي لا يمكن أن يتجاوز 255 حرفاً',
            'start_date.*.date' => 'تاريخ البداية يجب أن يكون تاريخاً صالحاً',
            'start_date.*.before_or_equal' => 'تاريخ البداية لا يمكن أن يكون مستقبلياً',
            'end_date.*.date' => 'تاريخ النهاية يجب أن يكون تاريخاً صالحاً',
            'end_date.*.after_or_equal' => 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية أو مساوياً له',
            'end_date.*.before_or_equal' => 'تاريخ النهاية لا يمكن أن يكون مستقبلياً',
            'description.*.string' => 'الوصف يجب أن يكون نصاً',
            'description.*.max' => 'الوصف لا يمكن أن يتجاوز 1000 حرف',
        ];
    }
}