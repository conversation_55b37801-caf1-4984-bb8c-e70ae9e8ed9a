<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class ProfileImageController extends Controller
{
    /**
     * رفع الصورة الشخصية
     */
    public function upload(Request $request)
    {
        try {
            $user = Auth::user();

            // التحقق من صحة البيانات
            $validator = Validator::make($request->all(), [
                'profile_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
            ], [
                'profile_image.required' => 'يرجى اختيار صورة',
                'profile_image.image' => 'الملف يجب أن يكون صورة',
                'profile_image.mimes' => 'نوع الصورة يجب أن يكون: jpeg, png, jpg, gif',
                'profile_image.max' => 'حجم الصورة يجب أن يكون أقل من 5 ميجابايت',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first(),
                    'errors' => $validator->errors()
                ], 422);
            }

            $image = $request->file('profile_image');

            // قراءة الصورة وتحويلها إلى base64
            $imageData = file_get_contents($image->getPathname());
            $base64Image = base64_encode($imageData);

            // الحصول على معلومات الصورة
            $imageType = $image->getClientOriginalExtension();
            $imageSize = $image->getSize();

            // ضغط الصورة إذا كانت كبيرة
            if ($imageSize > 1048576) { // 1MB
                $base64Image = $this->compressImage($imageData, $imageType);
                $imageSize = strlen(base64_decode($base64Image));
            }

            // حفظ الصورة في قاعدة البيانات
            Log::info('محاولة حفظ الصورة الشخصية', [
                'user_id' => $user->id,
                'image_type' => $imageType,
                'image_size' => $imageSize,
                'base64_length' => strlen($base64Image)
            ]);

            $user->updateProfileImage($base64Image, $imageType, $imageSize);

            // إعادة تحميل المستخدم للتأكد من الحفظ
            $user->refresh();

            Log::info('تم حفظ الصورة الشخصية', [
                'user_id' => $user->id,
                'has_profile_image' => $user->hasProfileImage(),
                'profile_image_size' => $user->profile_image_size
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم رفع الصورة الشخصية بنجاح!',
                'image_url' => $user->getProfileImageUrl(),
                'image_size' => $user->getProfileImageSizeForHumans(),
                'debug' => [
                    'has_image' => $user->hasProfileImage(),
                    'image_type' => $user->profile_image_type,
                    'image_size' => $user->profile_image_size,
                    'updated_at' => $user->profile_image_updated_at
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('خطأ في رفع الصورة الشخصية: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء رفع الصورة. يرجى المحاولة مرة أخرى.'
            ], 500);
        }
    }

    /**
     * حذف الصورة الشخصية
     */
    public function delete(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user->hasProfileImage()) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا توجد صورة شخصية لحذفها'
                ], 404);
            }

            $user->deleteProfileImage();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف الصورة الشخصية بنجاح!',
                'default_avatar' => $user->getDefaultAvatar()
            ]);

        } catch (\Exception $e) {
            Log::error('خطأ في حذف الصورة الشخصية: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف الصورة. يرجى المحاولة مرة أخرى.'
            ], 500);
        }
    }

    /**
     * عرض الصورة الشخصية
     */
    public function show(Request $request, $userId = null)
    {
        try {
            $user = $userId ? \App\Models\User::findOrFail($userId) : Auth::user();

            return response()->json([
                'success' => true,
                'has_image' => $user->hasProfileImage(),
                'image_url' => $user->getProfileImageUrl(),
                'image_size' => $user->getProfileImageSizeForHumans(),
                'updated_at' => $user->profile_image_updated_at ?
                    $user->profile_image_updated_at->diffForHumans() : null
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'المستخدم غير موجود'
            ], 404);
        }
    }

    /**
     * ضغط الصورة
     */
    private function compressImage($imageData, $imageType, $quality = 75)
    {
        try {
            // إنشاء صورة من البيانات
            $image = imagecreatefromstring($imageData);

            if (!$image) {
                return base64_encode($imageData); // إرجاع الصورة الأصلية إذا فشل الضغط
            }

            // الحصول على أبعاد الصورة
            $width = imagesx($image);
            $height = imagesy($image);

            // تحديد الحد الأقصى للأبعاد
            $maxWidth = 800;
            $maxHeight = 800;

            // حساب الأبعاد الجديدة
            if ($width > $maxWidth || $height > $maxHeight) {
                $ratio = min($maxWidth / $width, $maxHeight / $height);
                $newWidth = intval($width * $ratio);
                $newHeight = intval($height * $ratio);

                // إنشاء صورة جديدة بالأبعاد المحسوبة
                $newImage = imagecreatetruecolor($newWidth, $newHeight);

                // الحفاظ على الشفافية للـ PNG
                if ($imageType === 'png') {
                    imagealphablending($newImage, false);
                    imagesavealpha($newImage, true);
                    $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
                    imagefill($newImage, 0, 0, $transparent);
                }

                // تغيير حجم الصورة
                imagecopyresampled($newImage, $image, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);

                // تحرير الذاكرة
                imagedestroy($image);
                $image = $newImage;
            }

            // حفظ الصورة المضغوطة
            ob_start();

            switch (strtolower($imageType)) {
                case 'png':
                    imagepng($image, null, intval($quality / 10)); // PNG compression level 0-9
                    break;
                case 'gif':
                    imagegif($image);
                    break;
                default:
                    imagejpeg($image, null, $quality);
                    break;
            }

            $compressedData = ob_get_contents();
            ob_end_clean();

            // تحرير الذاكرة
            imagedestroy($image);

            return base64_encode($compressedData);

        } catch (\Exception $e) {
            Log::warning('فشل ضغط الصورة: ' . $e->getMessage());
            return base64_encode($imageData); // إرجاع الصورة الأصلية
        }
    }

    /**
     * الحصول على معلومات الصورة
     */
    public function getImageInfo(Request $request)
    {
        try {
            $user = Auth::user();

            return response()->json([
                'success' => true,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'has_profile_image' => $user->hasProfileImage(),
                    'profile_image_url' => $user->getProfileImageUrl(),
                    'profile_image_size' => $user->getProfileImageSizeForHumans(),
                    'profile_image_updated_at' => $user->profile_image_updated_at ?
                        $user->profile_image_updated_at->format('Y-m-d H:i:s') : null,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في الحصول على معلومات الصورة'
            ], 500);
        }
    }
}
