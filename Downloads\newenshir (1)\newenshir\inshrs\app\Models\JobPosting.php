<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JobPosting extends Model
{
    use HasFactory;

    protected $table = 'jobs'; // تحديد اسم الجدول يدويًا

    protected $fillable = [
        'user_id', 'job_title', 'company_name', 'location', 'salary',
        'experience_required', 'job_description', 'whatsapp', 'email', 'phone',
        'is_featured', 'featured_until'
    ];


    // في موديول JobPosting
public function user()
{
    return $this->belongsTo(User::class);
}


}
