<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تعديل الإعلان</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <!-- إضافة مكتبة Leaflet للخرائط -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    <!-- إضافة ملف الألوان المخصص -->
    <link href="{{ asset('css/colors.css') }}" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Tajawal', sans-serif;
            direction: rtl;
            text-align: right;
        }

        .card {
            border-radius: 1rem;
        }

        label {
            font-weight: 600;
        }

        .form-control:focus {
            box-shadow: 0 0 0 0.15rem rgba(13,110,253,.25);
        }

        button[type="submit"]:hover {
            transform: translateY(-2px);
            transition: all 0.3s ease-in-out;
        }

        /* أنماط الخريطة */
        #map-container {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        #map-modal {
            width: 90%;
            max-width: 800px;
            height: 80%;
            max-height: 600px;
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        #map-header {
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #eee;
        }

        #map-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
        }

        #map {
            flex: 1;
            width: 100%;
        }

        #map-footer {
            padding: 15px;
            text-align: center;
            border-top: 1px solid #eee;
        }

        #map-confirm {
            padding: 8px 20px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        #map-confirm:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
        }

        .map-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 8px 15px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
        }

        .map-button:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm p-4 bg-white">
                <h2 class="mb-4 text-center text-primary fw-bold">
                    <i class="fas fa-edit ms-2"></i>تعديل الإعلان
                </h2>

                <form action="{{ route('ads.update', $ad->id) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')

                    <div class="mb-3">
                        <label for="title" class="form-label">العنوان</label>
                        <input type="text" name="title" id="title" class="form-control" value="{{ old('title', $ad->title) }}" required>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea name="description" id="description" class="form-control" rows="4" required>{{ old('description', $ad->description) }}</textarea>
                    </div>

                    <div class="mb-3">
                        <label for="image" class="form-label">صورة الإعلان</label>
                        @if($ad->image)
                            <div class="mb-2">
                                <img src="{{ $ad->getImageUrl() }}" alt="{{ $ad->title }}" class="img-thumbnail" style="max-height: 200px;">
                                <p class="form-text">الصورة الحالية</p>
                            </div>
                        @endif
                        <input type="file" name="image" id="image" class="form-control" accept="image/*">
                        <div class="form-text">يمكنك رفع صورة جديدة بحجم أقصى 2 ميجابايت</div>
                        <div class="mt-2" id="image-preview-container" style="display: none;">
                            <img id="image-preview" src="#" alt="معاينة الصورة" style="max-width: 100%; max-height: 200px;">
                        </div>
                    </div>

                    @php
                        // التصنيفات الرئيسية والفرعية
                        $mainCategories = [
                            'vehicles' => [
                                'name' => 'مركبات',
                                'icon' => '🚗',
                                'subcategories' => [
                                    'new_cars' => 'سيارات جديدة',
                                    'used_cars' => 'سيارات مستعملة',
                                    'motorcycles' => 'دراجات نارية',
                                    'car_parts' => 'قطع غيار وإكسسوارات'
                                ]
                            ],
                            'realestate' => [
                                'name' => 'عقارات',
                                'icon' => '🏠',
                                'subcategories' => [
                                    'apartments_sale' => 'شقق للبيع',
                                    'apartments_rent' => 'شقق للإيجار',
                                    'villas' => 'فلل ومنازل',
                                    'lands' => 'أراضي للبيع'
                                ]
                            ],
                            'animals' => [
                                'name' => 'مواشي وحيوانات',
                                'icon' => '🐾',
                                'subcategories' => [
                                    'sheep' => 'أغنام',
                                    'cows' => 'أبقار',
                                    'camels' => 'إبل',
                                    'birds' => 'طيور زينة',
                                    'pets' => 'كلاب وقطط'
                                ]
                            ],
                            'electronics' => [
                                'name' => 'الكترونيات وتقنية',
                                'icon' => '💻',
                                'subcategories' => [
                                    'phones' => 'جوالات',
                                    'computers' => 'كمبيوترات',
                                    'tvs' => 'شاشات وتلفزيونات',
                                    'cameras' => 'كاميرات مراقبة'
                                ]
                            ],
                            'jobs' => [
                                'name' => 'وظائف وأعمال',
                                'icon' => '💼',
                                'subcategories' => [
                                    'admin_jobs' => 'وظائف إدارية',
                                    'tech_jobs' => 'وظائف فنية وهندسية',
                                    'sales_jobs' => 'وظائف مبيعات وتسويق',
                                    'freelance' => 'أعمال حرة ومقاولات'
                                ]
                            ],
                            'services' => [
                                'name' => 'خدمات عامة',
                                'icon' => '🛠',
                                'subcategories' => [
                                    'moving' => 'خدمات نقل عفش',
                                    'car_maintenance' => 'صيانة سيارات',
                                    'cleaning' => 'تنظيف منازل',
                                    'construction' => 'خدمات بناء وصيانة مباني'
                                ]
                            ],
                            'furniture' => [
                                'name' => 'أثاث ومستلزمات المنزل',
                                'icon' => '🛋',
                                'subcategories' => [
                                    'bedrooms' => 'غرف نوم',
                                    'kitchens' => 'مطابخ',
                                    'office_furniture' => 'أثاث مكتبي',
                                    'home_appliances' => 'أدوات منزلية وكهربائية'
                                ]
                            ],
                            'hobbies' => [
                                'name' => 'هوايات وترفيه',
                                'icon' => '🎮',
                                'subcategories' => [
                                    'sports' => 'أدوات رياضية',
                                    'games' => 'ألعاب إلكترونية',
                                    'books' => 'كتب ومجلات',
                                    'music' => 'آلات موسيقية'
                                ]
                            ],
                            'fashion' => [
                                'name' => 'ملابس وأزياء',
                                'icon' => '👕',
                                'subcategories' => [
                                    'men_clothes' => 'ملابس رجالية',
                                    'women_clothes' => 'ملابس نسائية',
                                    'shoes_bags' => 'أحذية وحقائب',
                                    'watches' => 'ساعات واكسسوارات'
                                ]
                            ],
                            'other' => [
                                'name' => 'أخرى',
                                'icon' => '📦',
                                'subcategories' => [
                                    'medical' => 'مستلزمات طبية',
                                    'gifts' => 'تحف وهدايا',
                                    'events' => 'مستلزمات مناسبات'
                                ]
                            ]
                        ];
                    @endphp

                    <div class="mb-3">
                        <label for="category" class="form-label">الفئة الرئيسية</label>
                        <select name="category" id="category" class="form-select" required onchange="updateSubcategories()">
                            <option value="" disabled>اختر الفئة الرئيسية</option>
                            @foreach($mainCategories as $key => $category)
                                <option value="{{ $key }}" {{ old('category', $ad->category) == $key ? 'selected' : '' }}>
                                    {{ $category['icon'] }} {{ $category['name'] }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="subcategory" class="form-label">الفئة الفرعية</label>
                        <select name="subcategory" id="subcategory" class="form-select">
                            <option value="" disabled selected>اختر الفئة الفرعية</option>
                            <!-- سيتم تحديث الخيارات بواسطة JavaScript -->
                        </select>
                    </div>

                    <script>
                        // تعريف التصنيفات الفرعية كمتغير JavaScript
                        const subcategories = @json($mainCategories);

                        // تحديث التصنيفات الفرعية عند تغيير التصنيف الرئيسي
                        function updateSubcategories() {
                            const categorySelect = document.getElementById('category');
                            const subcategorySelect = document.getElementById('subcategory');
                            const selectedCategory = categorySelect.value;

                            // إفراغ قائمة التصنيفات الفرعية
                            subcategorySelect.innerHTML = '<option value="" disabled selected>اختر الفئة الفرعية</option>';

                            // إذا تم اختيار تصنيف رئيسي
                            if (selectedCategory && subcategories[selectedCategory]) {
                                // إضافة التصنيفات الفرعية للتصنيف المختار
                                const subCats = subcategories[selectedCategory].subcategories;
                                for (const [key, value] of Object.entries(subCats)) {
                                    const option = document.createElement('option');
                                    option.value = key;
                                    option.textContent = value;

                                    // تحديد القيمة المحفوظة مسبقًا إن وجدت
                                    if (key === '{{ old('subcategory', $ad->subcategory) }}') {
                                        option.selected = true;
                                    }

                                    subcategorySelect.appendChild(option);
                                }
                            }
                        }

                        // تنفيذ الدالة عند تحميل الصفحة لتحديث التصنيفات الفرعية بناءً على القيمة المحفوظة
                        document.addEventListener('DOMContentLoaded', updateSubcategories);
                    </script>

                    <div class="mb-3">
                        <label for="location" class="form-label">الموقع</label>
                        <div class="input-group">
                            <input type="text" name="location" id="location" class="form-control" value="{{ old('location', $ad->location) }}">
                            <button type="button" class="btn btn-primary" id="get-location-btn">
                                <i class="fas fa-map-marker-alt"></i> تحديد موقعي
                            </button>
                            <button type="button" class="btn btn-outline-primary" id="open-map-btn">
                                <i class="fas fa-map"></i> اختيار من الخريطة
                            </button>
                        </div>
                        <div class="form-text" id="location-status"></div>
                        <!-- حقول مخفية للإحداثيات -->
                        <input type="hidden" name="latitude" id="latitude" value="{{ old('latitude', $ad->latitude) }}">
                        <input type="hidden" name="longitude" id="longitude" value="{{ old('longitude', $ad->longitude) }}">
                    </div>

                    <div class="mb-3">
                        <label for="price" class="form-label">السعر</label>
                        <input type="number" name="price" id="price" class="form-control" value="{{ old('price', $ad->price) }}">
                    </div>

                    <div class="mb-3">
                        <label for="phone" class="form-label">رقم الهاتف</label>
                        <input type="text" name="phone" id="phone" class="form-control" value="{{ old('phone', $ad->phone) }}">
                    </div>

                    <div class="mb-3">
                        <label for="whatsapp" class="form-label">رقم الواتساب</label>
                        <input type="text" name="whatsapp" id="whatsapp" class="form-control" value="{{ old('whatsapp', $ad->whatsapp) }}">
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" name="email" id="email" class="form-control" value="{{ old('email', $ad->email) }}">
                    </div>

                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="fas fa-check ms-2"></i>تحديث الإعلان
                        </button>
                    </div>

                </form>
            </div>
        </div>
    </div>
</div>

<script>
    // معاينة الصورة قبل الرفع
    document.addEventListener('DOMContentLoaded', function() {
        const imageInput = document.getElementById('image');
        const imagePreview = document.getElementById('image-preview');
        const previewContainer = document.getElementById('image-preview-container');

        imageInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    imagePreview.src = e.target.result;
                    previewContainer.style.display = 'block';
                }

                reader.readAsDataURL(this.files[0]);
            } else {
                previewContainer.style.display = 'none';
            }
        });

        // تحديد الموقع الجغرافي
        const getLocationBtn = document.getElementById('get-location-btn');
        const locationStatus = document.getElementById('location-status');
        const locationInput = document.getElementById('location');
        const latitudeInput = document.getElementById('latitude');
        const longitudeInput = document.getElementById('longitude');

        if (getLocationBtn) {
            getLocationBtn.addEventListener('click', function() {
                if (navigator.geolocation) {
                    locationStatus.textContent = 'جاري تحديد الموقع...';
                    locationStatus.style.color = 'blue';

                    navigator.geolocation.getCurrentPosition(
                        // نجاح
                        function(position) {
                            const latitude = position.coords.latitude;
                            const longitude = position.coords.longitude;

                            // تخزين الإحداثيات في الحقول المخفية
                            latitudeInput.value = latitude;
                            longitudeInput.value = longitude;

                            // الحصول على اسم الموقع باستخدام Reverse Geocoding
                            reverseGeocode(latitude, longitude);
                        },
                        // فشل
                        function(error) {
                            let errorMessage = '';
                            switch(error.code) {
                                case error.PERMISSION_DENIED:
                                    errorMessage = 'تم رفض الوصول إلى الموقع الجغرافي.';
                                    break;
                                case error.POSITION_UNAVAILABLE:
                                    errorMessage = 'معلومات الموقع غير متاحة.';
                                    break;
                                case error.TIMEOUT:
                                    errorMessage = 'انتهت مهلة طلب الموقع.';
                                    break;
                                case error.UNKNOWN_ERROR:
                                    errorMessage = 'حدث خطأ غير معروف.';
                                    break;
                            }
                            locationStatus.textContent = errorMessage;
                            locationStatus.style.color = 'red';
                        },
                        // خيارات
                        {
                            enableHighAccuracy: true,
                            timeout: 10000,
                            maximumAge: 0
                        }
                    );
                } else {
                    locationStatus.textContent = 'المتصفح لا يدعم تحديد الموقع الجغرافي.';
                    locationStatus.style.color = 'red';
                }
            });
        }

        // دالة للحصول على اسم الموقع من الإحداثيات
        function reverseGeocode(lat, lng) {
            // استخدام خدمة Nominatim OpenStreetMap للحصول على اسم الموقع
            fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&accept-language=ar`)
                .then(response => response.json())
                .then(data => {
                    if (data && data.address) {
                        // محاولة الحصول على اسم المدينة أو المنطقة
                        const city = data.address.city || data.address.town || data.address.village || data.address.county || '';
                        const country = data.address.country || '';

                        if (city && country) {
                            locationInput.value = `${country} - ${city}`;
                            locationStatus.textContent = 'تم تحديد الموقع بنجاح!';
                            locationStatus.style.color = 'green';
                        } else {
                            locationStatus.textContent = 'تم تحديد الإحداثيات ولكن لم يتم العثور على اسم المكان.';
                            locationStatus.style.color = 'orange';
                        }
                    } else {
                        locationStatus.textContent = 'تم تحديد الإحداثيات ولكن لم يتم العثور على معلومات الموقع.';
                        locationStatus.style.color = 'orange';
                    }
                })
                .catch(error => {
                    console.error('Error in reverse geocoding:', error);
                    locationStatus.textContent = 'تم تحديد الإحداثيات ولكن حدث خطأ في الحصول على اسم الموقع.';
                    locationStatus.style.color = 'orange';
                });
        }

        // تهيئة الخريطة
        let map = null;
        let marker = null;
        let selectedLat = null;
        let selectedLng = null;

        // عناصر نافذة الخريطة
        const mapContainer = document.getElementById('map-container');
        const mapCloseBtn = document.getElementById('map-close');
        const mapConfirmBtn = document.getElementById('map-confirm');
        const openMapBtn = document.getElementById('open-map-btn');
        const selectedLatSpan = document.getElementById('selected-lat');
        const selectedLngSpan = document.getElementById('selected-lng');

        // فتح نافذة الخريطة
        if (openMapBtn) {
            openMapBtn.addEventListener('click', function() {
                mapContainer.style.display = 'flex';

                // تهيئة الخريطة إذا لم تكن موجودة بالفعل
                if (!map) {
                    // تحديد مركز الخريطة (الرياض كمركز افتراضي)
                    const defaultLat = 24.7136;
                    const defaultLng = 46.6753;

                    // استخدام الإحداثيات المخزنة إذا كانت موجودة
                    const initialLat = latitudeInput.value ? parseFloat(latitudeInput.value) : defaultLat;
                    const initialLng = longitudeInput.value ? parseFloat(longitudeInput.value) : defaultLng;

                    // إنشاء الخريطة
                    map = L.map('map').setView([initialLat, initialLng], 13);

                    // إضافة طبقة الخريطة
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    }).addTo(map);

                    // إضافة علامة إذا كانت الإحداثيات موجودة
                    if (latitudeInput.value && longitudeInput.value) {
                        marker = L.marker([initialLat, initialLng], {draggable: true}).addTo(map);
                        selectedLat = initialLat;
                        selectedLng = initialLng;
                        selectedLatSpan.textContent = initialLat.toFixed(6);
                        selectedLngSpan.textContent = initialLng.toFixed(6);

                        // تحديث الإحداثيات عند سحب العلامة
                        marker.on('dragend', function(event) {
                            const position = marker.getLatLng();
                            selectedLat = position.lat;
                            selectedLng = position.lng;
                            selectedLatSpan.textContent = selectedLat.toFixed(6);
                            selectedLngSpan.textContent = selectedLng.toFixed(6);
                        });
                    }

                    // إضافة حدث النقر على الخريطة
                    map.on('click', function(e) {
                        // إزالة العلامة السابقة إذا وجدت
                        if (marker) {
                            map.removeLayer(marker);
                        }

                        // إضافة علامة جديدة
                        marker = L.marker(e.latlng, {draggable: true}).addTo(map);
                        selectedLat = e.latlng.lat;
                        selectedLng = e.latlng.lng;
                        selectedLatSpan.textContent = selectedLat.toFixed(6);
                        selectedLngSpan.textContent = selectedLng.toFixed(6);

                        // تحديث الإحداثيات عند سحب العلامة
                        marker.on('dragend', function(event) {
                            const position = marker.getLatLng();
                            selectedLat = position.lat;
                            selectedLng = position.lng;
                            selectedLatSpan.textContent = selectedLat.toFixed(6);
                            selectedLngSpan.textContent = selectedLng.toFixed(6);
                        });
                    });
                } else {
                    // تحديث حجم الخريطة عند إعادة فتحها
                    setTimeout(() => {
                        map.invalidateSize();
                    }, 100);
                }
            });
        }

        // إغلاق نافذة الخريطة
        if (mapCloseBtn) {
            mapCloseBtn.addEventListener('click', function() {
                mapContainer.style.display = 'none';
            });
        }

        // تأكيد اختيار الموقع
        if (mapConfirmBtn) {
            mapConfirmBtn.addEventListener('click', function() {
                if (selectedLat && selectedLng) {
                    // تخزين الإحداثيات في الحقول المخفية
                    latitudeInput.value = selectedLat;
                    longitudeInput.value = selectedLng;

                    // الحصول على اسم الموقع
                    reverseGeocode(selectedLat, selectedLng);

                    // إغلاق نافذة الخريطة
                    mapContainer.style.display = 'none';

                    // تحديث حالة الموقع
                    locationStatus.textContent = 'تم اختيار الموقع من الخريطة!';
                    locationStatus.style.color = 'green';
                } else {
                    alert('الرجاء اختيار موقع على الخريطة أولاً');
                }
            });
        }

        // إغلاق نافذة الخريطة عند النقر خارجها
        mapContainer.addEventListener('click', function(e) {
            if (e.target === mapContainer) {
                mapContainer.style.display = 'none';
            }
        });
    });
</script>

<!-- نافذة الخريطة -->
<div id="map-container">
    <div id="map-modal">
        <div id="map-header">
            <h3>اختر موقعًا على الخريطة</h3>
            <button id="map-close">&times;</button>
        </div>
        <div id="map"></div>
        <div id="map-footer">
            <p id="map-coordinates">الإحداثيات: <span id="selected-lat">-</span>, <span id="selected-lng">-</span></p>
            <button id="map-confirm">تأكيد الموقع</button>
        </div>
    </div>
</div>

</body>
</html>
