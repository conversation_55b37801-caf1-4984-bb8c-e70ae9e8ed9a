<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نموذج إرسال رسالة تقديم</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl; /* اتجاه النص من اليمين لليسار */
        }
        .container {
            width: 80%;
            margin: 20px auto;
            border: 1px solid #ccc;
            padding: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            cursor: pointer;
        }
    </style>


<style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3a8a, #1b6ac9);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            color: #fff;
        }

        .container {
            background-color: #1b263b;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            width: 90%;
            max-width: 600px;
            text-align: right;
            direction: rtl;
            margin-top: 20px; /* إضافة مسافة في الأعلى */
            border: 2px solid;
            border-image: linear-gradient(45deg, #ff6ec4, #7871ff) 1;
            animation: border-animation 2s linear infinite;
        }

        h1 {
            margin-bottom: 20px;
            font-size: 28px;
            color: #c3cfe2;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #e0e7ff;
        }

        input, textarea, select, button {
            width: 100%;
            padding: 12px;
            margin-bottom: 15px;
            border: 2px solid;
            border-image: linear-gradient(45deg, #ff6ec4, #7871ff) 1;
            border-radius: 8px;
            background-color: #1f2937;
            color: #e0e7ff;
            font-size: 16px;
            transition: border-color 0.3s;
            animation: border-animation 2s linear infinite;
        }

        textarea {
            height: 150px; /* زيادة الارتفاع لمربع النص */
            resize: vertical;
        }

        input:focus, textarea:focus, select:focus {
            border-color: #60a5fa;
            outline: none;
        }

        button {
            background-color: #60a5fa;
            color: #1f2937;
            border: none;
            cursor: pointer;
            font-size: 18px;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #4299e1;
        }

        .suggestions {
            list-style-type: none;
            padding: 0;
            margin: 0;
            border: 1px solid #4b5563;
            border-radius: 8px;
            max-height: 100px;
            overflow-y: auto;
            background-color: #1f2937;
            display: none;
        }

        .suggestions li {
            padding: 10px;
            cursor: pointer;
            color: #e0e7ff;
        }

        .suggestions li:hover {
            background-color: #2e3a47;
        }

        .description-section {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .description-section label {
            margin-bottom: 0;
        }

        .description-section button {
            margin-bottom: 0;
        }

        @keyframes border-animation {
            0% {
                border-image-source: linear-gradient(45deg, #ff6ec4, #7871ff);
            }
            50% {
                border-image-source: linear-gradient(45deg, #7871ff, #ff6ec4);
            }
            100% {
                border-image-source: linear-gradient(45deg, #ff6ec4, #7871ff);
            }
        }

        @media (max-width: 600px) {
            .container {
                padding: 20px;
                border-radius: 10px;
            }

            h1 {
                font-size: 24px;
            }

            input, textarea, select, button {
                font-size: 14px;
            }

            .description-section {
                flex-direction: column;
                align-items: flex-start;
            }

            .description-section button {
                margin-top: 10px;
            }
        }
    </style>



</head>
<body>

<div class="container">
    <h2>إرسال رسالة تقديم</h2>
    <form id="applicationForm">
        <label for="name">الاسم:</label>
        <input type="text" id="name" name="name" required>

        <label for="email">البريد الإلكتروني:</label>
        <input type="email" id="email" name="email" required>

        <label for="phone">رقم الهاتف:</label>
        <input type="tel" id="phone" name="phone" required>

        <label for="message">الرسالة:</label>
        <textarea id="message" name="message" rows="4" required></textarea>

        <input type="hidden" id="jobPostId" name="jobPostId" value="123">  <input type="hidden" id="companyId" name="companyId" value="456"> <button type="submit">إرسال</button>
    </form>
</div>

<script>
    document.getElementById('applicationForm').addEventListener('submit', function(event) {
        event.preventDefault(); // منع الإرسال الافتراضي

        // جمع بيانات النموذج
        const name = document.getElementById('name').value;
        const email = document.getElementById('email').value;
        const phone = document.getElementById('phone').value;
        const message = document.getElementById('message').value;
        const jobPostId = document.getElementById('jobPostId').value;
        const companyId = document.getElementById('companyId').value;

        // هنا يمكنك إرسال البيانات إلى الخادم باستخدام fetch أو XMLHttpRequest
        // مثال باستخدام fetch:
        fetch('/send_application', { // استبدل هذا بعنوان URL للخادم
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ name, email, phone, message, jobPostId, companyId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إرسال رسالة التقديم بنجاح!');
                document.getElementById('applicationForm').reset(); // إعادة تعيين النموذج
            } else {
                alert('حدث خطأ أثناء إرسال رسالة التقديم.');
            }
        });
    });
</script>

</body>
</html>