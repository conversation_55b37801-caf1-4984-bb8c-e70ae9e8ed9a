@extends('layouts.app')

@section('title', 'فئات الوظائف')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="text-center mb-10">
        <h1 class="text-3xl font-bold text-gray-800 mb-2">تصفح فئات الوظائف</h1>
        <p class="text-gray-600">اختر من بين مجموعة واسعة من فئات الوظائف لتجد الوظيفة المناسبة</p>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        @foreach($jobCategories as $category)
        <a href="{{ route('jobs.category', $category->slug) }}" class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
            <div class="p-6 text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas {{ $category->icon }} text-green-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">{{ $category->name }}</h3>
                <p class="text-sm text-gray-500">{{ $category->jobTitles->count() }} عنوان وظيفي</p>
            </div>
        </a>
        @endforeach
    </div>
</div>
@endsection
