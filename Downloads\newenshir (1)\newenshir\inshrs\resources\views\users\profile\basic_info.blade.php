<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل البيانات الشخصية</title>
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@2.8.2/dist/alpine.min.js" defer></script>

    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .gradient-bg { background: linear-gradient(135deg, #1e3c72, #2a5298); }
        .input-focus { transition: all 0.3s ease; }
        .input-focus:focus { box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5); border-color: #3b82f6; }
        .btn-hover { transition: transform 0.2s ease, box-shadow 0.2s ease; }
        .btn-hover:hover { transform: translateY(-3px); box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2); }
        .section-header { background: #f3f4f6; padding: 1rem; border-radius: 0.5rem; margin-bottom: 1rem; }
        .dynamic-field { margin-bottom: 1rem; }
    </style>
</head>
<body class="bg-gray-100 flex items-center justify-center min-h-screen p-4">
    <div class="bg-white p-8 rounded-xl shadow-2xl max-w-4xl w-full relative overflow-hidden">
        <div class="absolute top-0 left-0 w-full h-36 gradient-bg rounded-t-xl"></div>
        <div class="relative z-10">
            <h1 class="text-4xl font-bold text-white mb-4 text-center">تعديل البيانات الشخصية</h1>
            <p class="text-center text-gray-200 mb-8">نموذج ديناميكي واحترافي</p>
        </div>

        <form method="POST" action="{{ route('basic_info.update') }}" class="space-y-6 relative z-10">     @csrf
            @method('PUT')

            <!-- البيانات الشخصية -->
            <div class="bg-gray-50 p-6 rounded-lg shadow-inner">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4 section-header">البيانات الشخصية</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="full-name" class="block text-gray-700 mb-1">الاسم الكامل</label>
                        <input type="text" id="full-name" name="name" class="w-full p-3 border border-gray-300 rounded-lg input-focus" value="{{ old('full_name', 'أحمد عبدالله') }}" required>
                        @error('name') <span class="text-red-600">{{ $message }}</span> @enderror
                    </div>
                    <div>
                        <label for="email" class="block text-gray-700 mb-1">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" class="w-full p-3 border border-gray-300 rounded-lg input-focus" value="{{ old('email', '<EMAIL>') }}" required>
                        @error('email') <span class="text-red-600">{{ $message }}</span> @enderror
                    </div>
                </div>
            </div>

            
{{-- ملف تعديل كلمة المرور --}}
@include('profile.partials.update-password-form')


<div class="p-4 sm:p-8 bg-white shadow sm:rounded-lg">
                <div class="max-w-xl">
                    @include('profile.partials.delete-user-form')
                </div>
            </div>

             

{{-- زر الحفظ --}}
<div class="text-center">
    <button type="submit" class="bg-gradient-to-r from-blue-600 to-blue-800 text-white px-8 py-3 rounded-lg btn-hover">حفظ الملف الشخصي</button>
</div>


            <!-- المهارات التقنية -->
            <div class="bg-gray-50 p-6 rounded-lg shadow-inner">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4 section-header">المهارات التقنية</h2>
                <div id="skills-container">
                    @foreach(old('skill', ['React JS', 'Node.js']) as $index => $skill)
                        <div class="dynamic-field">
                            <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                                <div>
                                    <label class="block text-gray-700 mb-1">المهارة</label>
                                    <input type="text" name="skill[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus" value="{{ $skill }}">
                                </div>
                            </div>
                            <button type="button" class="text-red-600 hover:text-red-800 mt-2" onclick="removeField(this)">إزالة</button>
                        </div>
                    @endforeach
                </div>
                <button type="button" class="text-blue-600 hover:text-blue-800 mt-4" onclick="addSkill()">+ إضافة مهارة أخرى</button>
            </div>

            <!-- الخبرات العملية -->
            <div class="bg-gray-50 p-6 rounded-lg shadow-inner">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4 section-header">الخبرات العملية</h2>
                <div id="work-experience-container">
                    @foreach(old('company', ['شركة التقنية المتقدمة', 'شركة الويب الإبداعي']) as $index => $company)
                        <div class="dynamic-field">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-gray-700 mb-1">اسم الشركة</label>
                                    <input type="text" name="company[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus" value="{{ $company }}">
                                </div>
                                <div>
                                    <label class="block text-gray-700 mb-1">المنصب</label>
                                    <input type="text" name="position[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus" value="{{ old('position.' . $index, $index == 0 ? 'مطور ويب رئيسي' : 'مطور واجهات أمامية') }}">
                                </div>
                            </div>
                            <button type="button" class="text-red-600 hover:text-red-800 mt-2" onclick="removeField(this)">إزالة</button>
                        </div>
                    @endforeach
                </div>
                <button type="button" class="text-blue-600 hover:text-blue-800 mt-4" onclick="addWorkExperience()">+ إضافة خبرة أخرى</button>
            </div>

            <!-- اللغات -->
            <div class="bg-gray-50 p-6 rounded-lg shadow-inner">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4 section-header">اللغات</h2>
                <div id="languages-container">
                    @foreach(old('language', ['العربية', 'الإنجليزية']) as $index => $language)
                        <div class="dynamic-field">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-gray-700 mb-1">اللغة</label>
                                    <input type="text" name="language[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus" value="{{ $language }}">
                                </div>
                                <div>
                                    <label class="block text-gray-700 mb-1">مستوى الإجادة</label>
                                    <select name="proficiency[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus">
                                        <option value="" {{ old('proficiency.' . $index) == '' ? 'selected' : '' }}>اختر المستوى</option>
                                        <option value="اللغة الأم" {{ old('proficiency.' . $index, $index == 0 ? 'اللغة الأم' : '') == 'اللغة الأم' ? 'selected' : '' }}>اللغة الأم</option>
                                        <option value="مبتدئ" {{ old('proficiency.' . $index) == 'مبتدئ' ? 'selected' : '' }}>مبتدئ</option>
                                        <option value="متوسط" {{ old('proficiency.' . $index) == 'متوسط' ? 'selected' : '' }}>متوسط</option>
                                        <option value="متقدم" {{ old('proficiency.' . $index, $index == 1 ? 'متقدم' : '') == 'متقدم' ? 'selected' : '' }}>متقدم</option>
                                    </select>
                                </div>
                            </div>
                            <button type="button" class="text-red-600 hover:text-red-800 mt-2" onclick="removeField(this)">إزالة</button>
                        </div>
                    @endforeach
                </div>
                <button type="button" class="text-blue-600 hover:text-blue-800 mt-4" onclick="addLanguage()">+ إضافة لغة أخرى</button>
            </div>

            <!-- الدورات التدريبية -->
            <div class="bg-gray-50 p-6 rounded-lg shadow-inner">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4 section-header">الدورات التدريبية</h2>
                <div id="courses-container">
                    @foreach(old('course', ['دورة تطوير الويب الكامل', 'دورة تصميم واجهات المستخدم']) as $index => $course)
                        <div class="dynamic-field">
                            <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                                <div>
                                    <label class="block text-gray-700 mb-1">اسم الدورة</label>
                                    <input type="text" name="course[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus" value="{{ $course }}">
                                </div>
                            </div>
                            <button type="button" class="text-red-600 hover:text-red-800 mt-2" onclick="removeField(this)">إزالة</button>
                        </div>
                    @endforeach
                </div>
                <button type="button" class="text-blue-600 hover:text-blue-800 mt-4" onclick="addCourse()">+ إضافة دورة أخرى</button>
            </div>

            <!-- معلومات الاتصال الأخرى -->
            <div class="bg-gray-50 p-6 rounded-lg shadow-inner">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4 section-header">معلومات الاتصال الأخرى</h2>
                <div id="contact-info-container">
                    @foreach(old('contact_type', ['LinkedIn', 'Twitter']) as $index => $contact_type)
                        <div class="dynamic-field">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-gray-700 mb-1">نوع الاتصال</label>
                                    <input type="text" name="contact_type[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus" placeholder="مثال: LinkedIn, Twitter" value="{{ $contact_type }}">
                                </div>
                                <div>
                                    <label class="block text-gray-700 mb-1">القيمة</label>
                                    <input type="text" name="contact_value[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus" placeholder="مثال: رابط أو رقم" value="{{ old('contact_value.' . $index, $index == 0 ? 'linkedin.com/in/ahmedabdullah' : 'twitter.com/ahmedabdullah') }}">
                                </div>
                            </div>
                            <button type="button" class="text-red-600 hover:text-red-800 mt-2" onclick="removeField(this)">إزالة</button>
                        </div>
                    @endforeach
                </div>
                <button type="button" class="text-blue-600 hover:text-blue-800 mt-4" onclick="addContactInfo()">+ إضافة معلومات اتصال أخرى</button>
            </div>

            <!-- زر الحفظ -->
            <div class="text-center">
                <button type="submit" class="bg-gradient-to-r from-blue-600 to-blue-800 text-white px-8 py-3 rounded-lg btn-hover">حفظ الملف الشخصي</button>
            </div>
        </form>
    </div>

    <script>
        function addSkill() {
            const container = document.getElementById('skills-container');
            const newField = document.createElement('div');
            newField.classList.add('dynamic-field');
            newField.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                    <div>
                        <label class="block text-gray-700 mb-1">المهارة</label>
                        <input type="text" name="skill[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus">
                    </div>
                </div>
                <button type="button" class="text-red-600 hover:text-red-800 mt-2" onclick="removeField(this)">إزالة</button>
            `;
            container.appendChild(newField);
        }

        function addWorkExperience() {
            const container = document.getElementById('work-experience-container');
            const newField = document.createElement('div');
            newField.classList.add('dynamic-field');
            newField.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-gray-700 mb-1">اسم الشركة</label>
                        <input type="text" name="company[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus">
                    </div>
                    <div>
                        <label class="block text-gray-700 mb-1">المنصب</label>
                        <input type="text" name="position[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus">
                    </div>
                </div>
                <button type="button" class="text-red-600 hover:text-red-800 mt-2" onclick="removeField(this)">إزالة</button>
            `;
            container.appendChild(newField);
        }

        function addLanguage() {
            const container = document.getElementById('languages-container');
            const newField = document.createElement('div');
            newField.classList.add('dynamic-field');
            newField.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-gray-700 mb-1">اللغة</label>
                        <input type="text" name="language[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus">
                    </div>
                    <div>
                        <label class="block text-gray-700 mb-1">مستوى الإجادة</label>
                        <select name="proficiency[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus">
                            <option value="">اختر المستوى</option>
                            <option value="اللغة الأم">اللغة الأم</option>
                            <option value="مبتدئ">مبتدئ</option>
                            <option value="متوسط">متوسط</option>
                            <option value="متقدم">متقدم</option>
                        </select>
                    </div>
                </div>
                <button type="button" class="text-red-600 hover:text-red-800 mt-2" onclick="removeField(this)">إزالة</button>
            `;
            container.appendChild(newField);
        }

        function addCourse() {
            const container = document.getElementById('courses-container');
            const newField = document.createElement('div');
            newField.classList.add('dynamic-field');
            newField.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                    <div>
                        <label class="block text-gray-700 mb-1">اسم الدورة</label>
                        <input type="text" name="course[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus">
                    </div>
                </div>
                <button type="button" class="text-red-600 hover:text-red-800 mt-2" onclick="removeField(this)">إزالة</button>
            `;
            container.appendChild(newField);
        }

        function addContactInfo() {
            const container = document.getElementById('contact-info-container');
            const newField = document.createElement('div');
            newField.classList.add('dynamic-field');
            newField.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-gray-700 mb-1">نوع الاتصال</label>
                        <input type="text" name="contact_type[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus" placeholder="مثال: LinkedIn, Twitter">
                    </div>
                    <div>
                        <label class="block text-gray-700 mb-1">القيمة</label>
                        <input type="text" name="contact_value[]" class="w-full p-3 border border-gray-300 rounded-lg input-focus" placeholder="مثال: رابط أو رقم">
                    </div>
                </div>
                <button type="button" class="text-red-600 hover:text-red-800 mt-2" onclick="removeField(this)">إزالة</button>
            `;
            container.appendChild(newField);
        }

        function removeField(button) {
            button.parentElement.remove();
        }
    </script>
</body>
</html>