<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Image extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'data',
    ];

    /**
     * Get the base64 encoded image data with the appropriate data URI scheme
     *
     * @return string
     */
    public function getBase64Attribute()
    {
        return 'data:' . $this->type . ';base64,' . $this->data;
    }
}
