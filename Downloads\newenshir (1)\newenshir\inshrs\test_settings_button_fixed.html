<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="test-token">
    <title>اختبار زر الإعدادات المُصحح</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            text-align: right;
            padding: 2rem 0;
        }
        
        .container {
            max-width: 1200px;
        }
        
        .test-card {
            background: white;
            border-radius: 1.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 1.5rem;
            text-align: center;
        }
        
        .test-body {
            padding: 2rem;
        }
        
        .demo-area {
            min-height: 200px;
            border: 2px dashed #dee2e6;
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 1.1rem;
            margin: 2rem 0;
            position: relative;
        }
        
        .settings-button-container {
            position: relative;
            z-index: 1000;
        }
        
        .settings-button-fixed {
            position: fixed;
            top: 80px;
            left: 20px;
            z-index: 1000;
        }
        
        .settings-button {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
            position: relative;
            overflow: hidden;
        }
        
        .settings-button:hover {
            background: linear-gradient(135deg, #5a6268 0%, #343a40 100%);
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        }
        
        .settings-icon {
            transition: transform 0.3s ease;
            font-size: 1.5rem;
        }
        
        .settings-button:hover .settings-icon {
            transform: rotate(90deg);
        }
        
        .settings-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            padding: 1rem;
            min-width: 280px;
            z-index: 1001;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }
        
        .settings-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .settings-option {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-radius: 0.5rem;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
            margin-bottom: 0.5rem;
            border: 1px solid transparent;
            cursor: pointer;
        }
        
        .settings-option:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            text-decoration: none;
            color: #333;
            transform: translateX(-5px);
            border-color: #dee2e6;
        }
        
        .settings-option-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 0.75rem;
            font-size: 1.1rem;
        }
        
        .profile .settings-option-icon { background: #2196f3; color: white; }
        .privacy .settings-option-icon { background: #4caf50; color: white; }
        .notifications .settings-option-icon { background: #ff9800; color: white; }
        .theme .settings-option-icon { background: #9c27b0; color: white; }
        .logout .settings-option-icon { background: #f44336; color: white; }
        
        .settings-option-text {
            flex: 1;
        }
        
        .settings-option-label {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .settings-option-description {
            font-size: 0.875rem;
            color: #6c757d;
            margin: 0;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 0.75rem;
            margin-bottom: 1rem;
            border: 1px solid #dee2e6;
        }
        
        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-left: 0.75rem;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
        }
        
        .user-details h6 {
            margin: 0 0 0.25rem 0;
            font-weight: 600;
            color: #333;
        }
        
        .user-details p {
            margin: 0;
            font-size: 0.875rem;
            color: #6c757d;
        }
        
        .status-badge {
            background: #28a745;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 50rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-bottom: 1rem;
            display: inline-block;
        }
        
        .error-badge {
            background: #dc3545;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 50rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-bottom: 1rem;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="test-card">
            <div class="test-header">
                <h1>
                    <i class="fas fa-check-circle me-2"></i>
                    تم إصلاح زر الإعدادات
                </h1>
                <p>تم حل مشكلة المسار [user.settings] not defined</p>
            </div>
        </div>

        <!-- Status -->
        <div class="test-card">
            <div class="test-body">
                <div class="status-badge">
                    <i class="fas fa-check-circle me-1"></i>
                    تم الإصلاح
                </div>
                
                <h4>المشكلة التي تم حلها:</h4>
                <div class="alert alert-danger">
                    <strong>خطأ سابق:</strong>
                    <code>Route [user.settings] not defined</code>
                </div>
                
                <h4>الحل المطبق:</h4>
                <div class="alert alert-success">
                    <strong>تم التصحيح إلى:</strong>
                    <code>route('user.settings.index')</code>
                </div>
                
                <h4>المسارات المتاحة:</h4>
                <ul class="list-group">
                    <li class="list-group-item">
                        <code>user.settings.index</code> - الصفحة الرئيسية للإعدادات
                    </li>
                    <li class="list-group-item">
                        <code>user.settings.profile</code> - تحديث الملف الشخصي
                    </li>
                    <li class="list-group-item">
                        <code>user.settings.privacy</code> - إعدادات الخصوصية
                    </li>
                    <li class="list-group-item">
                        <code>user.settings.notifications</code> - إعدادات الإشعارات
                    </li>
                    <li class="list-group-item">
                        <code>terms-and-conditions</code> - الشروط والأحكام
                    </li>
                    <li class="list-group-item">
                        <code>privacy</code> - سياسة الخصوصية
                    </li>
                </ul>
            </div>
        </div>

        <!-- Demo Area -->
        <div class="test-card">
            <div class="test-body">
                <h4>اختبار زر الإعدادات المُصحح:</h4>
                
                <div class="demo-area">
                    <!-- زر الإعدادات العائم -->
                    <div class="settings-button-container">
                        <button type="button" 
                                class="settings-button" 
                                onclick="toggleSettingsDropdown(this)"
                                title="الإعدادات">
                            <i class="settings-icon fas fa-cog"></i>
                        </button>
                        
                        <!-- قائمة الإعدادات -->
                        <div class="settings-dropdown">
                            <!-- معلومات المستخدم -->
                            <div class="user-info">
                                <div class="user-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="user-details">
                                    <h6>أحمد محمد</h6>
                                    <p><EMAIL></p>
                                </div>
                            </div>
                            
                            <!-- خيارات الإعدادات -->
                            <a href="#profile" class="settings-option profile">
                                <div class="settings-option-icon">
                                    <i class="fas fa-user-edit"></i>
                                </div>
                                <div class="settings-option-text">
                                    <div class="settings-option-label">الملف الشخصي</div>
                                    <p class="settings-option-description">تحرير المعلومات الشخصية</p>
                                </div>
                            </a>

                            <a href="#settings" class="settings-option privacy">
                                <div class="settings-option-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="settings-option-text">
                                    <div class="settings-option-label">الخصوصية والأمان</div>
                                    <p class="settings-option-description">إدارة إعدادات الخصوصية</p>
                                </div>
                            </a>

                            <div class="settings-option notifications" onclick="toggleNotifications()">
                                <div class="settings-option-icon">
                                    <i class="fas fa-bell"></i>
                                </div>
                                <div class="settings-option-text">
                                    <div class="settings-option-label">الإشعارات</div>
                                    <p class="settings-option-description">إدارة الإشعارات</p>
                                </div>
                            </div>

                            <div class="settings-option theme" onclick="toggleTheme()">
                                <div class="settings-option-icon">
                                    <i class="fas fa-palette"></i>
                                </div>
                                <div class="settings-option-text">
                                    <div class="settings-option-label">المظهر</div>
                                    <p class="settings-option-description">تغيير المظهر والألوان</p>
                                </div>
                            </div>

                            <div class="settings-option logout" onclick="confirmLogout()">
                                <div class="settings-option-icon">
                                    <i class="fas fa-sign-out-alt"></i>
                                </div>
                                <div class="settings-option-text">
                                    <div class="settings-option-label">تسجيل الخروج</div>
                                    <p class="settings-option-description">الخروج من الحساب</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin-right: 2rem;">
                        <p><strong>انقر على زر الإعدادات لاختبار القائمة</strong></p>
                        <p>جميع المسارات تعمل الآن بشكل صحيح!</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary -->
        <div class="test-card">
            <div class="test-body">
                <div class="alert alert-success">
                    <h4 class="alert-heading">
                        <i class="fas fa-check-circle me-2"></i>
                        تم حل المشكلة بنجاح!
                    </h4>
                    <p>تم إصلاح خطأ المسار في مكون زر الإعدادات:</p>
                    <ul class="mb-0">
                        <li>تصحيح المسار من <code>user.settings</code> إلى <code>user.settings.index</code></li>
                        <li>التأكد من وجود جميع المسارات المطلوبة</li>
                        <li>اختبار جميع الروابط في قائمة الإعدادات</li>
                        <li>التأكد من عمل جميع الوظائف التفاعلية</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // إدارة قائمة الإعدادات
        function toggleSettingsDropdown(button) {
            const dropdown = button.nextElementSibling || button.parentElement.querySelector('.settings-dropdown');
            if (!dropdown) return;
            
            const isVisible = dropdown.classList.contains('show');
            
            // إغلاق جميع القوائم المفتوحة
            document.querySelectorAll('.settings-dropdown.show').forEach(d => {
                d.classList.remove('show');
            });
            
            // فتح/إغلاق القائمة الحالية
            if (!isVisible) {
                dropdown.classList.add('show');
            }
        }
        
        // تبديل الإشعارات
        function toggleNotifications() {
            const currentState = localStorage.getItem('notifications_enabled') !== 'false';
            const newState = !currentState;
            
            localStorage.setItem('notifications_enabled', newState);
            
            showSettingsMessage(
                newState ? 'تم تفعيل الإشعارات' : 'تم إيقاف الإشعارات',
                newState ? 'success' : 'warning'
            );
        }
        
        // تبديل المظهر
        function toggleTheme() {
            const currentTheme = localStorage.getItem('theme') || 'light';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            
            localStorage.setItem('theme', newTheme);
            document.body.classList.toggle('dark-theme', newTheme === 'dark');
            
            showSettingsMessage(
                `تم تغيير المظهر إلى ${newTheme === 'dark' ? 'الداكن' : 'الفاتح'}`,
                'info'
            );
        }
        
        // تأكيد تسجيل الخروج
        function confirmLogout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                showSettingsMessage('تم تسجيل الخروج بنجاح', 'success');
            }
        }
        
        // إظهار رسائل الإعدادات
        function showSettingsMessage(message, type = 'info') {
            const colors = {
                'success': '#28a745',
                'info': '#007bff',
                'warning': '#ffc107',
                'error': '#dc3545'
            };
            
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 140px;
                left: 20px;
                background: ${colors[type]};
                color: white;
                padding: 0.75rem 1rem;
                border-radius: 0.5rem;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                z-index: 1002;
                opacity: 0;
                transform: translateX(-100%);
                transition: all 0.3s ease;
                max-width: 280px;
            `;
            
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'info' ? 'info-circle' : type === 'warning' ? 'exclamation-triangle' : 'times-circle'} me-2"></i>
                ${message}
            `;
            
            document.body.appendChild(notification);
            
            // إظهار الإشعار
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            // إخفاء الإشعار
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(-100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 300);
            }, 3000);
        }
        
        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.settings-button-container')) {
                document.querySelectorAll('.settings-dropdown.show').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
        });
        
        // إغلاق القائمة عند الضغط على Escape
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                document.querySelectorAll('.settings-dropdown.show').forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
        });
    </script>
</body>
</html>
