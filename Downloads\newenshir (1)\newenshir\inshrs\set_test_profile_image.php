<?php

// ملف لإضافة صورة شخصية تجريبية دائمة
// تشغيل: php set_test_profile_image.php

require_once 'vendor/autoload.php';

// تحميل إعدادات Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;

echo "🖼️ إضافة صورة شخصية تجريبية دائمة\n";
echo "===================================\n\n";

try {
    // الحصول على أول مستخدم
    $user = User::first();
    if (!$user) {
        echo "❌ لا يوجد مستخدمون في قاعدة البيانات\n";
        exit;
    }

    echo "👤 المستخدم: {$user->name} (ID: {$user->id})\n";
    echo "📧 البريد: {$user->email}\n\n";

    // إنشاء صورة تجريبية أكبر وأجمل
    echo "🎨 إنشاء صورة تجريبية جميلة...\n";
    
    // إنشاء صورة 100x100 بكسل
    $image = imagecreate(100, 100);
    
    // ألوان جميلة
    $backgroundColor = imagecolorallocate($image, 52, 152, 219); // أزرق جميل
    $textColor = imagecolorallocate($image, 255, 255, 255); // أبيض
    $borderColor = imagecolorallocate($image, 41, 128, 185); // أزرق داكن
    
    // رسم حدود
    imagerectangle($image, 0, 0, 99, 99, $borderColor);
    imagerectangle($image, 1, 1, 98, 98, $borderColor);
    
    // كتابة النص
    $initials = mb_substr($user->name, 0, 2); // أول حرفين من الاسم
    
    // حساب موضع النص للوسط
    $fontSize = 5;
    $textWidth = imagefontwidth($fontSize) * strlen($initials);
    $textHeight = imagefontheight($fontSize);
    $x = (100 - $textWidth) / 2;
    $y = (100 - $textHeight) / 2;
    
    imagestring($image, $fontSize, $x, $y, $initials, $textColor);
    
    // إضافة دائرة صغيرة للزينة
    $circleColor = imagecolorallocate($image, 46, 204, 113); // أخضر
    imagefilledellipse($image, 80, 20, 15, 15, $circleColor);
    
    // تحويل إلى PNG
    ob_start();
    imagepng($image);
    $imageData = ob_get_contents();
    ob_end_clean();
    imagedestroy($image);
    
    $base64Image = base64_encode($imageData);
    $imageType = 'png';
    $imageSize = strlen($imageData);
    
    echo "   📸 نوع الصورة: {$imageType}\n";
    echo "   📏 حجم الصورة: {$imageSize} بايت (" . round($imageSize/1024, 2) . " KB)\n";
    echo "   📝 طول Base64: " . strlen($base64Image) . " حرف\n";
    echo "   🎨 النص على الصورة: {$initials}\n\n";

    // حفظ الصورة
    echo "💾 حفظ الصورة الشخصية...\n";
    
    $user->updateProfileImage($base64Image, $imageType, $imageSize);
    
    // إعادة تحميل البيانات للتأكد
    $user = $user->fresh();
    
    echo "   📊 النتيجة:\n";
    echo "      hasProfileImage(): " . ($user->hasProfileImage() ? 'نعم' : 'لا') . "\n";
    echo "      profile_image_type: " . ($user->profile_image_type ?? 'فارغ') . "\n";
    echo "      profile_image_size: " . ($user->profile_image_size ?? 'فارغ') . " بايت\n";
    echo "      getProfileImageSizeForHumans(): " . ($user->getProfileImageSizeForHumans() ?? 'فارغ') . "\n";
    echo "      profile_image_updated_at: " . ($user->profile_image_updated_at ? $user->profile_image_updated_at->format('Y-m-d H:i:s') : 'فارغ') . "\n\n";

    if ($user->hasProfileImage()) {
        echo "🎉 تم حفظ الصورة الشخصية بنجاح!\n\n";
        
        // عرض معلومات إضافية
        echo "🔗 معلومات الصورة:\n";
        echo "   📱 رابط الصورة: data:image/{$user->profile_image_type};base64,[base64_data]\n";
        echo "   🎨 الصورة الافتراضية: " . substr($user->getDefaultAvatar(), 0, 80) . "...\n\n";
        
        echo "📋 الخطوات التالية:\n";
        echo "==================\n";
        echo "1. اذهب إلى: /profile\n";
        echo "2. ستجد الصورة التجريبية معروضة\n";
        echo "3. يمكنك تغييرها بالنقر عليها\n";
        echo "4. اختبر الصفحات الأخرى:\n";
        echo "   - /test-profile-image\n";
        echo "   - /jobs/show_job_company/1\n";
        echo "   - /show-job-user/1\n";
        echo "   - /ads/1\n";
        echo "   - /job-seekers\n";
        echo "   - /my-jobs\n\n";
        
        echo "🎯 النظام جاهز للاستخدام!\n";
        
    } else {
        echo "❌ فشل في حفظ الصورة الشخصية\n";
        echo "🔧 تحقق من:\n";
        echo "   - صلاحيات قاعدة البيانات\n";
        echo "   - User Model methods\n";
        echo "   - fillable attributes\n";
    }

    // إضافة صور لمستخدمين آخرين إذا وجدوا
    echo "\n👥 إضافة صور لمستخدمين آخرين...\n";
    
    $otherUsers = User::where('id', '!=', $user->id)->limit(5)->get();
    
    if ($otherUsers->count() > 0) {
        foreach ($otherUsers as $index => $otherUser) {
            echo "   👤 {$otherUser->name}... ";
            
            // إنشاء صورة مختلفة لكل مستخدم
            $colors = [
                [231, 76, 60],   // أحمر
                [155, 89, 182],  // بنفسجي
                [52, 152, 219],  // أزرق
                [46, 204, 113],  // أخضر
                [241, 196, 15],  // أصفر
            ];
            
            $color = $colors[$index % count($colors)];
            
            $userImage = imagecreate(80, 80);
            $bgColor = imagecolorallocate($userImage, $color[0], $color[1], $color[2]);
            $textColor = imagecolorallocate($userImage, 255, 255, 255);
            
            $userInitials = mb_substr($otherUser->name, 0, 2);
            $x = (80 - imagefontwidth(4) * strlen($userInitials)) / 2;
            $y = (80 - imagefontheight(4)) / 2;
            
            imagestring($userImage, 4, $x, $y, $userInitials, $textColor);
            
            ob_start();
            imagepng($userImage);
            $userImageData = ob_get_contents();
            ob_end_clean();
            imagedestroy($userImage);
            
            $userBase64 = base64_encode($userImageData);
            $userSize = strlen($userImageData);
            
            $otherUser->updateProfileImage($userBase64, 'png', $userSize);
            
            echo "✅ تم\n";
        }
        
        echo "   🎉 تم إضافة صور لـ " . $otherUsers->count() . " مستخدمين إضافيين\n\n";
    } else {
        echo "   ℹ️ لا يوجد مستخدمون آخرون\n\n";
    }

} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    echo "\nتفاصيل الخطأ:\n";
    echo $e->getTraceAsString() . "\n";
}

echo "\n🚀 انتهى! النظام جاهز للاستخدام\n";

?>
