<?php

// ملف لإضافة حقول الصورة الشخصية مباشرة إلى قاعدة البيانات
// تشغيل: php add_profile_columns_direct.php

require_once 'vendor/autoload.php';

// تحميل إعدادات Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

echo "🔧 إضافة حقول الصورة الشخصية مباشرة...\n";
echo "==========================================\n\n";

try {
    // التحقق من الاتصال بقاعدة البيانات
    echo "1️⃣ فحص الاتصال بقاعدة البيانات...\n";
    $connection = DB::connection();
    $databaseName = $connection->getDatabaseName();
    echo "   ✅ متصل بقاعدة البيانات: {$databaseName}\n\n";

    // فحص الحقول الموجودة
    echo "2️⃣ فحص الحقول الموجودة...\n";
    $columns = Schema::getColumnListing('users');
    echo "   📋 الحقول الموجودة في جدول users:\n";
    foreach ($columns as $column) {
        echo "      - {$column}\n";
    }
    echo "\n";

    // التحقق من وجود حقول الصورة الشخصية
    $profileImageColumns = [
        'profile_image',
        'profile_image_type',
        'profile_image_size', 
        'profile_image_updated_at'
    ];

    $missingColumns = [];
    echo "3️⃣ فحص حقول الصورة الشخصية...\n";
    foreach ($profileImageColumns as $column) {
        if (in_array($column, $columns)) {
            echo "   ✅ {$column} - موجود\n";
        } else {
            echo "   ❌ {$column} - مفقود\n";
            $missingColumns[] = $column;
        }
    }
    echo "\n";

    if (empty($missingColumns)) {
        echo "🎉 جميع الحقول موجودة بالفعل!\n";
        
        // اختبار النظام
        echo "\n4️⃣ اختبار النظام...\n";
        $user = \App\Models\User::first();
        if ($user) {
            echo "   👤 المستخدم التجريبي: {$user->name}\n";
            echo "   📸 hasProfileImage(): " . ($user->hasProfileImage() ? 'نعم' : 'لا') . "\n";
            echo "   🖼️ getProfileImageUrl(): " . substr($user->getProfileImageUrl(), 0, 50) . "...\n";
            echo "   📊 getProfileImageSizeForHumans(): " . ($user->getProfileImageSizeForHumans() ?? 'لا توجد صورة') . "\n";
        }
        
        exit;
    }

    // إضافة الحقول المفقودة
    echo "4️⃣ إضافة الحقول المفقودة...\n";
    
    Schema::table('users', function (Blueprint $table) use ($missingColumns) {
        foreach ($missingColumns as $column) {
            switch ($column) {
                case 'profile_image':
                    echo "   ➕ إضافة حقل profile_image...\n";
                    $table->longText('profile_image')->nullable()->comment('الصورة الشخصية (Base64)');
                    break;
                    
                case 'profile_image_type':
                    echo "   ➕ إضافة حقل profile_image_type...\n";
                    $table->string('profile_image_type', 20)->nullable()->comment('نوع الصورة (jpeg, png, gif)');
                    break;
                    
                case 'profile_image_size':
                    echo "   ➕ إضافة حقل profile_image_size...\n";
                    $table->integer('profile_image_size')->nullable()->comment('حجم الصورة بالبايت');
                    break;
                    
                case 'profile_image_updated_at':
                    echo "   ➕ إضافة حقل profile_image_updated_at...\n";
                    $table->timestamp('profile_image_updated_at')->nullable()->comment('آخر تحديث للصورة الشخصية');
                    break;
            }
        }
    });

    echo "   ✅ تم إضافة جميع الحقول بنجاح!\n\n";

    // التحقق من الإضافة
    echo "5️⃣ التحقق من الإضافة...\n";
    $newColumns = Schema::getColumnListing('users');
    foreach ($profileImageColumns as $column) {
        if (in_array($column, $newColumns)) {
            echo "   ✅ {$column} - تم إضافته بنجاح\n";
        } else {
            echo "   ❌ {$column} - فشل في الإضافة\n";
        }
    }
    echo "\n";

    // اختبار النظام
    echo "6️⃣ اختبار النظام...\n";
    $user = \App\Models\User::first();
    if ($user) {
        echo "   👤 المستخدم التجريبي: {$user->name}\n";
        
        // اختبار helper methods
        try {
            echo "   📸 hasProfileImage(): " . ($user->hasProfileImage() ? 'نعم' : 'لا') . "\n";
            echo "   🖼️ getProfileImageUrl(): " . substr($user->getProfileImageUrl(), 0, 50) . "...\n";
            echo "   📊 getProfileImageSizeForHumans(): " . ($user->getProfileImageSizeForHumans() ?? 'لا توجد صورة') . "\n";
            echo "   🎨 getDefaultAvatar(): " . substr($user->getDefaultAvatar(), 0, 50) . "...\n";
            
            echo "   ✅ جميع helper methods تعمل بشكل صحيح!\n";
        } catch (Exception $e) {
            echo "   ❌ خطأ في helper methods: {$e->getMessage()}\n";
        }
        
        // اختبار إضافة صورة تجريبية
        echo "\n7️⃣ اختبار إضافة صورة تجريبية...\n";
        try {
            // إنشاء صورة تجريبية صغيرة (1x1 pixel PNG)
            $testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
            
            $user->updateProfileImage($testImageBase64, 'png', 95);
            echo "   ✅ تم حفظ صورة تجريبية بنجاح!\n";
            
            // التحقق من الحفظ
            $user->refresh();
            if ($user->hasProfileImage()) {
                echo "   ✅ تم التحقق من وجود الصورة: {$user->getProfileImageSizeForHumans()}\n";
                
                // حذف الصورة التجريبية
                $user->deleteProfileImage();
                echo "   ✅ تم حذف الصورة التجريبية\n";
            } else {
                echo "   ❌ فشل في حفظ الصورة\n";
            }
            
        } catch (Exception $e) {
            echo "   ❌ خطأ في اختبار الصورة: {$e->getMessage()}\n";
        }
        
    } else {
        echo "   ⚠️ لا يوجد مستخدمون في قاعدة البيانات\n";
    }

    echo "\n🎉 تم الانتهاء بنجاح!\n\n";
    
    echo "📋 الخطوات التالية:\n";
    echo "==================\n";
    echo "1. اذهب إلى: /profile\n";
    echo "2. انقر على الصورة لرفع صورة جديدة\n";
    echo "3. اختبر رفع صور مختلفة\n";
    echo "4. تصفح الصفحات الأخرى للتأكد من عرض الصورة\n";
    echo "5. اختبر النظام عبر: /test-profile-image\n\n";

} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    echo "\nتفاصيل الخطأ:\n";
    echo $e->getTraceAsString() . "\n\n";
    
    echo "🔧 حلول بديلة:\n";
    echo "================\n";
    echo "1. شغل SQL مباشرة في phpMyAdmin:\n";
    echo "   ALTER TABLE users ADD COLUMN profile_image LONGTEXT NULL;\n";
    echo "   ALTER TABLE users ADD COLUMN profile_image_type VARCHAR(20) NULL;\n";
    echo "   ALTER TABLE users ADD COLUMN profile_image_size INT NULL;\n";
    echo "   ALTER TABLE users ADD COLUMN profile_image_updated_at TIMESTAMP NULL;\n\n";
    
    echo "2. استخدم ملف fix_profile_image_database.sql\n";
    echo "3. تحقق من إعدادات قاعدة البيانات في .env\n";
    echo "4. تأكد من صلاحيات المستخدم في قاعدة البيانات\n";
}

echo "\n📞 للمساعدة:\n";
echo "============\n";
echo "- راجع ملف profile_image_troubleshooting.md\n";
echo "- تحقق من logs/laravel.log\n";
echo "- اختبر الاتصال بقاعدة البيانات\n";

?>
