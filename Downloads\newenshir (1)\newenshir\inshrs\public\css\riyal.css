 
            @font-face {
    font-family: 'Saudi Riyal';
    src: url('fonts/SaudiRiyal.woff2') format('woff2'),
         url('fonts/SaudiRiyal.woff') format('woff'),
         url('fonts/SaudiRiyal.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}
  
.ryal-symbol {
    display: inline-block;
    width: 1em;
    height: 1em;
    vertical-align: middle;
}

.rotate {
    animation: rotate-animation 2s linear infinite;
}

@keyframes rotate-animation {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.pulse {
    animation: pulse-animation 2s infinite;
}

@keyframes pulse-animation {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.blink {
    animation: blink-animation 1s steps(5, start) infinite;
}

@keyframes blink-animation {
    to {
        visibility: hidden;
    }
}

.shake {
    animation: shake-animation 0.5s infinite;
}

@keyframes shake-animation {
    0% { transform: translate(2px, 1px) rotate(0deg); }
    10% { transform: translate(-1px, -2px) rotate(-1deg); }
    20% { transform: translate(-3px, 0px) rotate(1deg); }
    30% { transform: translate(0px, 2px) rotate(0deg); }
    40% { transform: translate(1px, -1px) rotate(1deg); }
    50% { transform: translate(-1px, 2px) rotate(-1deg); }
    60% { transform: translate(-3px, 1px) rotate(0deg); }
    70% { transform: translate(2px, 1px) rotate(-1deg); }
    80% { transform: translate(-1px, -1px) rotate(1deg); }
    90% { transform: translate(2px, 2px) rotate(0deg); }
    100% { transform: translate(1px, -2px) rotate(-1deg); }
}

.custom-style {
    /* يمكنك إضافة أنماط CSS مخصصة هنا */
    color: #dc3545;
    font-size: 1.2em;
    padding: 5px 10px;
    border-radius: 5px;
    background-color: #f8d7da;
}

.fade {
    animation: fade-animation 2s ease-in-out;
}

@keyframes fade-animation {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
} 