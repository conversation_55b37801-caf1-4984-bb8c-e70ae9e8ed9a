<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'transaction_id',
        'payment_method',
        'amount',
        'points_amount',
        'status',
        'payment_details',
        'completed_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_details' => 'array',
        'completed_at' => 'datetime',
    ];

    /**
     * العلاقة مع المستخدم
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * تحديد ما إذا كانت المعاملة مكتملة
     */
    public function isCompleted()
    {
        return $this->status === 'COMPLETED';
    }

    /**
     * تحديد ما إذا كانت المعاملة فاشلة
     */
    public function isFailed()
    {
        return in_array($this->status, ['FAILED', 'DECLINED', 'EXPIRED']);
    }

    /**
     * تحديد ما إذا كانت المعاملة معلقة
     */
    public function isPending()
    {
        return in_array($this->status, ['PENDING', 'PROCESSING', 'CREATED']);
    }
}
