<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ViewSpamProtection
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // التحقق من spam المشاهدات فقط لصفحات عرض الإعلانات
        if ($request->route()->getName() === 'ads.show') {
            $ip = $request->ip();
            $userAgent = $request->header('User-Agent', '');
            
            // إنشاء مفتاح فريد للجهاز
            $deviceKey = 'view_spam_' . md5($ip . $userAgent);
            
            // الحصول على عدد المشاهدات في آخر دقيقة
            $viewsInLastMinute = Cache::get($deviceKey, 0);
            
            // الحد الأقصى: 10 مشاهدات في الدقيقة الواحدة
            $maxViewsPerMinute = 10;
            
            if ($viewsInLastMinute >= $maxViewsPerMinute) {
                Log::warning('View spam detected', [
                    'ip' => $ip,
                    'user_agent' => substr($userAgent, 0, 100),
                    'views_count' => $viewsInLastMinute,
                    'url' => $request->fullUrl()
                ]);
                
                // إرجاع استجابة عادية لكن بدون تسجيل المشاهدة
                $request->attributes->set('skip_view_tracking', true);
            } else {
                // زيادة العداد
                Cache::put($deviceKey, $viewsInLastMinute + 1, 60); // 60 ثانية
            }
        }
        
        return $next($request);
    }
}
