<?php
/**
 * سكريبت لإنشاء جدول site_settings يدوياً
 * يمكن تشغيله من المتصفح أو من سطر الأوامر
 */

// التأكد من أن هذا السكريبت يعمل في بيئة Laravel
if (!function_exists('env')) {
    die('هذا السكريبت يجب أن يعمل في بيئة Laravel');
}

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

try {
    echo "<h2>بدء إنشاء جدول site_settings...</h2>\n";

    // التحقق من وجود الجدول
    if (Schema::hasTable('site_settings')) {
        echo "<p style='color: orange;'>⚠️ الجدول موجود بالفعل. سيتم حذفه وإعادة إنشاؤه.</p>\n";
        Schema::dropIfExists('site_settings');
    }

    // إنشاء الجدول
    Schema::create('site_settings', function ($table) {
        $table->id();
        $table->string('key')->unique();
        $table->text('value')->nullable();
        $table->string('type')->default('text');
        $table->string('group')->default('general');
        $table->string('label');
        $table->text('description')->nullable();
        $table->integer('sort_order')->default(0);
        $table->boolean('is_active')->default(true);
        $table->timestamps();

        // إضافة فهارس
        $table->index(['group']);
        $table->index(['is_active']);
        $table->index(['sort_order']);
        $table->index(['type']);
        $table->index(['group', 'is_active']);
    });

    echo "<p style='color: green;'>✅ تم إنشاء الجدول بنجاح!</p>\n";

    // البيانات الافتراضية
    $defaultSettings = [
        [
            'key' => 'site_name',
            'value' => 'منصة إنشر',
            'type' => 'text',
            'group' => 'general',
            'label' => 'اسم الموقع',
            'description' => 'اسم الموقع الذي يظهر في العنوان والهيدر',
            'sort_order' => 1,
        ],
        [
            'key' => 'site_logo',
            'value' => 'images/enshir.ico',
            'type' => 'image',
            'group' => 'appearance',
            'label' => 'لوجو الموقع',
            'description' => 'لوجو الموقع الذي يظهر في الهيدر',
            'sort_order' => 2,
        ],
        [
            'key' => 'site_favicon',
            'value' => 'images/enshir.ico',
            'type' => 'image',
            'group' => 'appearance',
            'label' => 'أيقونة الموقع (Favicon)',
            'description' => 'الأيقونة التي تظهر في تبويب المتصفح',
            'sort_order' => 3,
        ],
        [
            'key' => 'site_description',
            'value' => 'منصة إعلانات متنوعة تجمع بين البائعين والمشترين في مكان واحد',
            'type' => 'textarea',
            'group' => 'general',
            'label' => 'وصف الموقع',
            'description' => 'وصف مختصر للموقع يظهر في محركات البحث',
            'sort_order' => 4,
        ],
        [
            'key' => 'site_keywords',
            'value' => 'إعلانات, تسوق, بيع, شراء, وظائف, عقارات, سيارات, خدمات',
            'type' => 'textarea',
            'group' => 'general',
            'label' => 'الكلمات المفتاحية',
            'description' => 'الكلمات المفتاحية للموقع لمحركات البحث',
            'sort_order' => 5,
        ],
        [
            'key' => 'contact_email',
            'value' => '<EMAIL>',
            'type' => 'email',
            'group' => 'contact',
            'label' => 'البريد الإلكتروني للتواصل',
            'description' => 'البريد الإلكتروني الرئيسي للموقع',
            'sort_order' => 6,
        ],
        [
            'key' => 'contact_phone',
            'value' => '+966500000000',
            'type' => 'text',
            'group' => 'contact',
            'label' => 'رقم الهاتف',
            'description' => 'رقم الهاتف للتواصل',
            'sort_order' => 7,
        ],
        [
            'key' => 'primary_color',
            'value' => '#3AB0FF',
            'type' => 'color',
            'group' => 'appearance',
            'label' => 'اللون الأساسي',
            'description' => 'اللون الأساسي للموقع',
            'sort_order' => 8,
        ],
        [
            'key' => 'secondary_color',
            'value' => '#E67E22',
            'type' => 'color',
            'group' => 'appearance',
            'label' => 'اللون الثانوي',
            'description' => 'اللون الثانوي للموقع',
            'sort_order' => 9,
        ],
        [
            'key' => 'maintenance_mode',
            'value' => '0',
            'type' => 'boolean',
            'group' => 'general',
            'label' => 'وضع الصيانة',
            'description' => 'تفعيل وضع الصيانة للموقع',
            'sort_order' => 10,
        ],
        [
            'key' => 'site_slogan',
            'value' => 'سوقك لكل شيء',
            'type' => 'text',
            'group' => 'general',
            'label' => 'شعار الموقع',
            'description' => 'الشعار الذي يظهر تحت اسم الموقع',
            'sort_order' => 11,
        ],
        [
            'key' => 'admin_email',
            'value' => '<EMAIL>',
            'type' => 'email',
            'group' => 'contact',
            'label' => 'بريد المدير',
            'description' => 'البريد الإلكتروني للمدير',
            'sort_order' => 12,
        ],
        [
            'key' => 'facebook_url',
            'value' => 'https://facebook.com/enshir',
            'type' => 'text',
            'group' => 'social',
            'label' => 'رابط فيسبوك',
            'description' => 'رابط صفحة الفيسبوك',
            'sort_order' => 13,
        ],
        [
            'key' => 'twitter_url',
            'value' => 'https://twitter.com/enshir',
            'type' => 'text',
            'group' => 'social',
            'label' => 'رابط تويتر',
            'description' => 'رابط حساب تويتر',
            'sort_order' => 14,
        ],
        [
            'key' => 'instagram_url',
            'value' => 'https://instagram.com/enshir',
            'type' => 'text',
            'group' => 'social',
            'label' => 'رابط إنستغرام',
            'description' => 'رابط حساب إنستغرام',
            'sort_order' => 15,
        ],
        [
            'key' => 'whatsapp_number',
            'value' => '+966500000000',
            'type' => 'text',
            'group' => 'contact',
            'label' => 'رقم واتساب',
            'description' => 'رقم واتساب للتواصل السريع',
            'sort_order' => 16,
        ],
        [
            'key' => 'site_address',
            'value' => 'الرياض، المملكة العربية السعودية',
            'type' => 'textarea',
            'group' => 'contact',
            'label' => 'عنوان الموقع',
            'description' => 'العنوان الفيزيائي للشركة',
            'sort_order' => 17,
        ],
        [
            'key' => 'working_hours',
            'value' => 'الأحد - الخميس: 9:00 ص - 6:00 م',
            'type' => 'textarea',
            'group' => 'contact',
            'label' => 'ساعات العمل',
            'description' => 'ساعات العمل الرسمية',
            'sort_order' => 18,
        ],
        [
            'key' => 'google_analytics',
            'value' => '',
            'type' => 'textarea',
            'group' => 'seo',
            'label' => 'كود Google Analytics',
            'description' => 'كود تتبع Google Analytics',
            'sort_order' => 19,
        ],
        [
            'key' => 'meta_author',
            'value' => 'منصة إنشر',
            'type' => 'text',
            'group' => 'seo',
            'label' => 'مؤلف الموقع',
            'description' => 'اسم مؤلف الموقع للـ meta tags',
            'sort_order' => 20,
        ],
        [
            'key' => 'copyright_text',
            'value' => '© 2024 منصة إنشر. جميع الحقوق محفوظة.',
            'type' => 'text',
            'group' => 'general',
            'label' => 'نص حقوق الطبع',
            'description' => 'النص الذي يظهر في footer',
            'sort_order' => 21,
        ],
        [
            'key' => 'max_upload_size',
            'value' => '5',
            'type' => 'number',
            'group' => 'general',
            'label' => 'الحد الأقصى لحجم الملف (MB)',
            'description' => 'الحد الأقصى لحجم الملفات المرفوعة بالميجابايت',
            'sort_order' => 22,
        ],
        [
            'key' => 'default_language',
            'value' => 'ar',
            'type' => 'text',
            'group' => 'general',
            'label' => 'اللغة الافتراضية',
            'description' => 'اللغة الافتراضية للموقع',
            'sort_order' => 23,
        ],
        [
            'key' => 'timezone',
            'value' => 'Asia/Riyadh',
            'type' => 'text',
            'group' => 'general',
            'label' => 'المنطقة الزمنية',
            'description' => 'المنطقة الزمنية للموقع',
            'sort_order' => 24,
        ],
        [
            'key' => 'currency',
            'value' => 'SAR',
            'type' => 'text',
            'group' => 'general',
            'label' => 'العملة الافتراضية',
            'description' => 'العملة المستخدمة في الموقع',
            'sort_order' => 25,
        ],
    ];

    // إدراج البيانات
    echo "<h3>إدراج البيانات الافتراضية...</h3>\n";
    
    foreach ($defaultSettings as $setting) {
        $setting['created_at'] = now();
        $setting['updated_at'] = now();
        $setting['is_active'] = true;
        
        DB::table('site_settings')->insert($setting);
        echo "<p>✅ تم إدراج: {$setting['label']}</p>\n";
    }

    // إحصائيات
    $totalSettings = DB::table('site_settings')->count();
    $groups = DB::table('site_settings')->distinct()->pluck('group');

    echo "<hr>\n";
    echo "<h3 style='color: green;'>🎉 تم إنشاء الجدول بنجاح!</h3>\n";
    echo "<p><strong>إجمالي الإعدادات:</strong> {$totalSettings}</p>\n";
    echo "<p><strong>المجموعات:</strong> " . implode(', ', $groups->toArray()) . "</p>\n";
    
    echo "<hr>\n";
    echo "<h3>الخطوات التالية:</h3>\n";
    echo "<ol>\n";
    echo "<li>انتقل إلى لوحة تحكم المدير</li>\n";
    echo "<li>ابحث عن 'إعدادات الموقع' في القائمة الجانبية</li>\n";
    echo "<li>ابدأ في تخصيص إعدادات موقعك</li>\n";
    echo "</ol>\n";

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ حدث خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تأكد من:</p>\n";
    echo "<ul>\n";
    echo "<li>اتصال قاعدة البيانات</li>\n";
    echo "<li>صلاحيات إنشاء الجداول</li>\n";
    echo "<li>تشغيل السكريبت في بيئة Laravel</li>\n";
    echo "</ul>\n";
}
?>
