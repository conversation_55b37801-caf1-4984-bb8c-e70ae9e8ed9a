<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\SiteSetting;

class CheckMaintenanceMode
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // التحقق من وضع الصيانة
        if (SiteSetting::isMaintenanceMode()) {
            // السماح للمدير بالوصول
            if (auth()->check() && auth()->user()->is_admin) {
                return $next($request);
            }

            // السماح بالوصول لصفحات تسجيل الدخول والخروج
            $allowedRoutes = [
                'login',
                'logout',
                'admin.site-settings.index',
                'admin.site-settings.update'
            ];

            if (in_array($request->route()->getName(), $allowedRoutes)) {
                return $next($request);
            }

            // عرض صفحة الصيانة
            return response()->view('maintenance', [
                'site_name' => SiteSetting::siteName(),
                'contact_email' => SiteSetting::contactEmail(),
                'contact_phone' => SiteSetting::contactPhone()
            ], 503);
        }

        return $next($request);
    }
}
