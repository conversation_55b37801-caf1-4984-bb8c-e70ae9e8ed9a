<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class EmailValidationController extends Controller
{
    /**
     * التحقق من توفر البريد الإلكتروني
     */
    public function checkEmailAvailability(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email'
            ], [
                'email.required' => 'البريد الإلكتروني مطلوب',
                'email.email' => 'تنسيق البريد الإلكتروني غير صحيح'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'available' => false,
                    'message' => $validator->errors()->first('email'),
                    'errors' => $validator->errors()
                ], 422);
            }

            $email = $request->input('email');
            
            // التحقق من وجود البريد في قاعدة البيانات
            $emailExists = User::where('email', $email)->exists();
            
            if ($emailExists) {
                return response()->json([
                    'available' => false,
                    'message' => 'البريد الإلكتروني مستخدم بالفعل',
                    'suggestions' => $this->generateEmailSuggestions($email)
                ]);
            }

            // التحقق من صحة النطاق
            $domainValidation = $this->validateEmailDomain($email);
            
            if (!$domainValidation['valid']) {
                return response()->json([
                    'available' => false,
                    'message' => $domainValidation['message'],
                    'suggestions' => $this->generateEmailSuggestions($email)
                ]);
            }

            return response()->json([
                'available' => true,
                'message' => 'البريد الإلكتروني متاح ✓',
                'domain_info' => $domainValidation['info']
            ]);

        } catch (\Exception $e) {
            Log::error('Email validation error: ' . $e->getMessage());
            
            return response()->json([
                'available' => false,
                'message' => 'حدث خطأ أثناء التحقق من البريد الإلكتروني',
                'error' => 'server_error'
            ], 500);
        }
    }

    /**
     * التحقق من صحة نطاق البريد الإلكتروني
     */
    private function validateEmailDomain($email)
    {
        $domain = substr(strrchr($email, "@"), 1);
        
        // قائمة النطاقات المحظورة
        $blockedDomains = [
            '10minutemail.com',
            'tempmail.org',
            'guerrillamail.com',
            'mailinator.com',
            'throwaway.email'
        ];
        
        if (in_array($domain, $blockedDomains)) {
            return [
                'valid' => false,
                'message' => 'لا يمكن استخدام عناوين البريد المؤقتة'
            ];
        }
        
        // التحقق من وجود MX record
        if (!checkdnsrr($domain, 'MX')) {
            return [
                'valid' => false,
                'message' => 'نطاق البريد الإلكتروني غير صحيح'
            ];
        }
        
        // معلومات إضافية عن النطاق
        $domainInfo = $this->getDomainInfo($domain);
        
        return [
            'valid' => true,
            'message' => 'نطاق البريد الإلكتروني صحيح',
            'info' => $domainInfo
        ];
    }

    /**
     * الحصول على معلومات النطاق
     */
    private function getDomainInfo($domain)
    {
        $popularDomains = [
            'gmail.com' => [
                'name' => 'Gmail',
                'provider' => 'Google',
                'icon' => 'fab fa-google',
                'secure' => true
            ],
            'yahoo.com' => [
                'name' => 'Yahoo Mail',
                'provider' => 'Yahoo',
                'icon' => 'fab fa-yahoo',
                'secure' => true
            ],
            'hotmail.com' => [
                'name' => 'Hotmail',
                'provider' => 'Microsoft',
                'icon' => 'fab fa-microsoft',
                'secure' => true
            ],
            'outlook.com' => [
                'name' => 'Outlook',
                'provider' => 'Microsoft',
                'icon' => 'fab fa-microsoft',
                'secure' => true
            ],
            'icloud.com' => [
                'name' => 'iCloud Mail',
                'provider' => 'Apple',
                'icon' => 'fab fa-apple',
                'secure' => true
            ]
        ];
        
        return $popularDomains[$domain] ?? [
            'name' => $domain,
            'provider' => 'Unknown',
            'icon' => 'fas fa-envelope',
            'secure' => true
        ];
    }

    /**
     * إنشاء اقتراحات بديلة للبريد الإلكتروني
     */
    private function generateEmailSuggestions($email)
    {
        $localPart = substr($email, 0, strpos($email, '@'));
        
        $suggestions = [];
        $popularDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'];
        
        foreach ($popularDomains as $domain) {
            $suggestions[] = [
                'email' => $localPart . '@' . $domain,
                'domain' => $domain,
                'available' => !User::where('email', $localPart . '@' . $domain)->exists()
            ];
        }
        
        return array_filter($suggestions, function($suggestion) {
            return $suggestion['available'];
        });
    }

    /**
     * التحقق من قوة كلمة المرور
     */
    public function checkPasswordStrength(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'password' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'valid' => false,
                    'message' => 'كلمة المرور مطلوبة'
                ], 422);
            }

            $password = $request->input('password');
            $analysis = $this->analyzePassword($password);
            
            return response()->json([
                'valid' => $analysis['score'] >= 80,
                'score' => $analysis['score'],
                'strength' => $analysis['strength'],
                'feedback' => $analysis['feedback'],
                'requirements' => $analysis['requirements']
            ]);

        } catch (\Exception $e) {
            Log::error('Password strength check error: ' . $e->getMessage());
            
            return response()->json([
                'valid' => false,
                'message' => 'حدث خطأ أثناء التحقق من كلمة المرور'
            ], 500);
        }
    }

    /**
     * تحليل قوة كلمة المرور
     */
    private function analyzePassword($password)
    {
        $score = 0;
        $feedback = [];
        $requirements = [];
        
        // طول كلمة المرور
        $length = strlen($password);
        $requirements['length'] = $length >= 8;
        if ($length >= 8) {
            $score += 20;
        } else {
            $feedback[] = 'يجب أن تكون كلمة المرور 8 أحرف على الأقل';
        }
        
        // الأحرف الكبيرة
        $requirements['uppercase'] = preg_match('/[A-Z]/', $password);
        if ($requirements['uppercase']) {
            $score += 20;
        } else {
            $feedback[] = 'أضف حرف كبير واحد على الأقل';
        }
        
        // الأحرف الصغيرة
        $requirements['lowercase'] = preg_match('/[a-z]/', $password);
        if ($requirements['lowercase']) {
            $score += 20;
        } else {
            $feedback[] = 'أضف حرف صغير واحد على الأقل';
        }
        
        // الأرقام
        $requirements['numbers'] = preg_match('/[0-9]/', $password);
        if ($requirements['numbers']) {
            $score += 20;
        } else {
            $feedback[] = 'أضف رقم واحد على الأقل';
        }
        
        // الرموز الخاصة
        $requirements['special'] = preg_match('/[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]/', $password);
        if ($requirements['special']) {
            $score += 20;
        } else {
            $feedback[] = 'أضف رمز خاص واحد على الأقل';
        }
        
        // تحديد مستوى القوة
        $strength = 'ضعيفة جداً';
        if ($score >= 100) {
            $strength = 'قوية جداً';
        } elseif ($score >= 80) {
            $strength = 'قوية';
        } elseif ($score >= 60) {
            $strength = 'متوسطة';
        } elseif ($score >= 40) {
            $strength = 'ضعيفة';
        }
        
        return [
            'score' => $score,
            'strength' => $strength,
            'feedback' => $feedback,
            'requirements' => $requirements
        ];
    }

    /**
     * إرسال رمز التحقق عبر البريد الإلكتروني
     */
    public function sendVerificationCode(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first('email')
                ], 422);
            }

            $email = $request->input('email');
            
            // إنشاء رمز التحقق
            $code = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
            
            // حفظ الرمز في الجلسة أو قاعدة البيانات
            session(['verification_code' => $code, 'verification_email' => $email]);
            
            // إرسال البريد الإلكتروني
            // هنا يمكنك استخدام نظام البريد الإلكتروني المحدث
            
            return response()->json([
                'success' => true,
                'message' => 'تم إرسال رمز التحقق إلى بريدك الإلكتروني',
                'expires_in' => 600 // 10 دقائق
            ]);

        } catch (\Exception $e) {
            Log::error('Verification code send error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إرسال رمز التحقق'
            ], 500);
        }
    }
}
