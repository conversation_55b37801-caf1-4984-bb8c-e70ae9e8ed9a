# نظام إعدادات الخصوصية للمستخدمين - دليل شامل

## 🎯 **نظرة عامة:**

تم إنشاء نظام شامل لإعدادات الخصوصية يسمح للمستخدمين بالتحكم الكامل في خصوصيتهم وتفاعلهم مع المستخدمين الآخرين.

## 📁 **الملفات المنشأة:**

### 1. **قاعدة البيانات:**
- `database/migrations/2024_01_02_000000_add_privacy_settings_to_users_table.php`

### 2. **Controllers:**
- `app/Http/Controllers/UserSettingsController.php`

### 3. **Views:**
- `resources/views/user/settings/index.blade.php`

### 4. **Middleware:**
- `app/Http/Middleware/CheckPrivacySettings.php`

### 5. **Helpers:**
- `app/Helpers/PrivacyHelper.php`

### 6. **Models:**
- تحديث `app/Models/User.php`

### 7. **Routes:**
- تحديث `routes/web.php`

## 🔧 **الميزات المتاحة:**

### 1. **إعدادات المراسلة:**
- ✅ **السماح بالمراسلة:** تحكم في من يمكنه إرسال رسائل خاصة
- ✅ **السماح بالتعليقات:** تحكم في من يمكنه التعليق على الإعلانات
- ✅ **حظر المستخدمين:** منع مستخدمين محددين من التواصل

### 2. **إعدادات العرض:**
- ✅ **إظهار رقم الهاتف:** تحكم في عرض رقم الهاتف
- ✅ **إظهار البريد الإلكتروني:** تحكم في عرض البريد الإلكتروني
- ✅ **إظهار حالة الاتصال:** تحكم في عرض حالة الاتصال (متصل/غير متصل)

### 3. **إعدادات الملف الشخصي:**
- ✅ **الملف الشخصي عام:** تحكم في من يمكنه رؤية الملف الشخصي
- ✅ **إظهار عدد الإعلانات:** تحكم في عرض عدد الإعلانات المنشورة
- ✅ **إظهار تاريخ الانضمام:** تحكم في عرض تاريخ التسجيل
- ✅ **قابلية البحث:** تحكم في ظهور الملف الشخصي في نتائج البحث
- ✅ **الظهور في الاقتراحات:** تحكم في ظهور الملف في اقتراحات المستخدمين

### 4. **إعدادات الإشعارات:**
- ✅ **إشعارات البريد الإلكتروني:** تحكم في الإشعارات عبر البريد
- ✅ **الرسائل النصية:** تحكم في الإشعارات عبر SMS
- ✅ **الإشعارات المنبثقة:** تحكم في إشعارات المتصفح
- ✅ **رسائل التسويق:** تحكم في الرسائل الترويجية
- ✅ **تنبيهات الأمان:** تحكم في تنبيهات تسجيل الدخول

### 5. **إعدادات التفضيلات:**
- ✅ **اللغة المفضلة:** اختيار بين العربية والإنجليزية
- ✅ **المظهر:** اختيار بين الفاتح والداكن والتلقائي
- ✅ **المنطقة الزمنية:** تحديد المنطقة الزمنية

### 6. **إعدادات الأمان:**
- ✅ **تغيير كلمة المرور:** تحديث كلمة المرور بأمان
- ✅ **المصادقة الثنائية:** تفعيل/إلغاء المصادقة الثنائية
- ✅ **حذف الحساب:** حذف الحساب نهائياً مع التأكيد

## 🎨 **واجهة المستخدم:**

### **التصميم:**
- 🎨 تصميم عصري ومتجاوب
- 📱 متوافق مع جميع الأجهزة
- 🎯 تبويبات منظمة وسهلة التنقل
- ✨ ألوان ديناميكية من إعدادات الموقع

### **التبويبات:**
1. **الخصوصية** - إعدادات المراسلة والعرض والملف الشخصي
2. **الإشعارات** - تحكم في جميع أنواع الإشعارات
3. **التفضيلات** - اللغة والمظهر والمنطقة الزمنية
4. **الأمان** - كلمة المرور والمصادقة الثنائية
5. **المحظورون** - إدارة المستخدمين المحظورين

## 🔒 **نظام الحماية:**

### **Middleware:**
```php
CheckPrivacySettings::class
```
- التحقق من إعدادات الخصوصية قبل الوصول
- منع المستخدمين المحظورين من التفاعل
- حماية المحتوى الخاص

### **Helper Class:**
```php
PrivacyHelper::class
```
- فحص إمكانية إرسال الرسائل
- فحص إمكانية التعليق
- فحص إمكانية رؤية الملف الشخصي
- فلترة المستخدمين حسب الخصوصية

## 📊 **قاعدة البيانات:**

### **الحقول المضافة لجدول users:**

#### **إعدادات الخصوصية:**
- `allow_messages` - السماح بالمراسلة
- `allow_comments` - السماح بالتعليقات
- `show_phone` - إظهار رقم الهاتف
- `show_email` - إظهار البريد الإلكتروني
- `show_online_status` - إظهار حالة الاتصال

#### **إعدادات الإشعارات:**
- `email_notifications` - إشعارات البريد الإلكتروني
- `sms_notifications` - الرسائل النصية
- `push_notifications` - الإشعارات المنبثقة
- `marketing_emails` - رسائل التسويق

#### **إعدادات الملف الشخصي:**
- `profile_public` - الملف الشخصي عام
- `show_ads_count` - إظهار عدد الإعلانات
- `show_join_date` - إظهار تاريخ الانضمام

#### **إعدادات الأمان:**
- `two_factor_enabled` - المصادقة الثنائية
- `login_alerts` - تنبيهات تسجيل الدخول
- `blocked_users` - المستخدمون المحظورون (JSON)

#### **إعدادات اللغة والمظهر:**
- `preferred_language` - اللغة المفضلة
- `theme_preference` - تفضيل المظهر
- `timezone` - المنطقة الزمنية

#### **إعدادات البحث:**
- `searchable_profile` - قابلية البحث
- `show_in_suggestions` - الظهور في الاقتراحات

#### **تواريخ التتبع:**
- `privacy_updated_at` - آخر تحديث للخصوصية
- `last_seen` - آخر ظهور

## 🚀 **كيفية الاستخدام:**

### **1. الوصول للإعدادات:**
```
/user/settings
```

### **2. تحديث إعدادات الخصوصية:**
```php
POST /user/settings/privacy
```

### **3. تحديث إعدادات الإشعارات:**
```php
POST /user/settings/notifications
```

### **4. تحديث التفضيلات:**
```php
POST /user/settings/preferences
```

### **5. تغيير كلمة المرور:**
```php
POST /user/settings/password
```

### **6. حظر/إلغاء حظر مستخدم:**
```php
POST /user/settings/block
POST /user/settings/unblock
```

## 🔧 **Helper Methods في User Model:**

### **فحص الخصوصية:**
```php
$user->canReceiveMessages()      // هل يقبل الرسائل؟
$user->canReceiveComments()      // هل يقبل التعليقات؟
$user->shouldShowPhone()         // هل يظهر الهاتف؟
$user->shouldShowEmail()         // هل يظهر البريد؟
$user->hasPublicProfile()        // هل الملف عام؟
```

### **إدارة الحظر:**
```php
$user->hasBlocked($userId)       // هل حظر مستخدم؟
$user->isBlockedBy($userId)      // هل محظور من مستخدم؟
$user->getBlockedUsers()         // قائمة المحظورين
```

### **حالة الاتصال:**
```php
$user->isOnline()                // هل متصل؟
$user->getOnlineStatus()         // حالة الاتصال
$user->getLastSeenForHumans()    // آخر ظهور
$user->updateLastSeen()          // تحديث آخر ظهور
```

## 🎯 **تطبيق الخصوصية:**

### **في المحادثات:**
```php
if (!PrivacyHelper::canSendMessage($senderId, $receiverId)) {
    return abort(403, 'لا يمكن إرسال رسالة لهذا المستخدم');
}
```

### **في التعليقات:**
```php
if (!PrivacyHelper::canComment($commenterId, $adOwnerId)) {
    return abort(403, 'لا يمكن التعليق على هذا الإعلان');
}
```

### **في عرض الملفات الشخصية:**
```php
if (!PrivacyHelper::canViewProfile($viewerId, $profileOwnerId)) {
    return abort(403, 'الملف الشخصي غير متاح');
}
```

## 📱 **التوافق:**

### **المتصفحات:**
- ✅ Chrome
- ✅ Firefox
- ✅ Safari
- ✅ Edge

### **الأجهزة:**
- ✅ Desktop
- ✅ Mobile
- ✅ Tablet

## 🔄 **التحديثات التلقائية:**

### **آخر ظهور:**
- تحديث تلقائي كل دقيقة
- تتبع حالة الاتصال
- عرض "متصل الآن" أو "آخر ظهور"

### **الإشعارات:**
- إشعارات فورية للتغييرات
- رسائل تأكيد للعمليات
- تنبيهات الأمان

## 🛡️ **الأمان:**

### **التحقق:**
- التحقق من كلمة المرور للعمليات الحساسة
- تأكيد مضاعف لحذف الحساب
- حماية من CSRF

### **التشفير:**
- تشفير كلمات المرور
- حماية البيانات الحساسة
- جلسات آمنة

## 🎉 **النتيجة:**

الآن المستخدمون يمكنهم:

### **التحكم الكامل في:**
- ✅ من يمكنه مراسلتهم
- ✅ من يمكنه التعليق على إعلاناتهم
- ✅ ما يظهر من معلوماتهم الشخصية
- ✅ كيفية ظهورهم للآخرين
- ✅ نوع الإشعارات التي يتلقونها

### **الحماية من:**
- ❌ الرسائل غير المرغوبة
- ❌ التعليقات المزعجة
- ❌ انتهاك الخصوصية
- ❌ المضايقات

### **تجربة مستخدم محسنة:**
- 🎨 واجهة عصرية وسهلة
- ⚡ استجابة سريعة
- 🔒 أمان عالي
- 📱 متوافق مع جميع الأجهزة

النظام جاهز للاستخدام ويوفر تحكم شامل في الخصوصية! 🚀✨
