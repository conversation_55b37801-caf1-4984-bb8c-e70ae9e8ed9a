<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Validator;

class UserRoleController extends Controller
{
    /**
     * عرض صفحة إدارة أدوار المستخدمين
     */
    public function index()
    {
        $users = User::with('roles')->where('is_admin', true)->get();
        return view('admin.user-roles.index', compact('users'));
    }

    /**
     * عرض نموذج تعيين أدوار للمستخدم
     */
    public function edit($id)
    {
        $user = User::with('roles')->findOrFail($id);
        $roles = Role::all();
        $userRoles = $user->roles->pluck('id')->toArray();

        return view('admin.user-roles.edit', compact('user', 'roles', 'userRoles'));
    }

    /**
     * تحديث أدوار المستخدم
     */
    public function update(Request $request, $id)
    {
        $user = User::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'roles' => 'required|array',
            'roles.*' => 'exists:roles,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $user->syncRoles($request->roles);

        // تحديث حالة المسؤول للمستخدم
        $user->is_admin = true;
        $user->save();

        return redirect()->route('admin.user-roles.index')
            ->with('success', 'تم تحديث أدوار المستخدم بنجاح');
    }

    /**
     * عرض نموذج إضافة مسؤول جديد
     */
    public function create()
    {
        $users = User::where('is_admin', false)->get();
        $roles = Role::all();

        return view('admin.user-roles.create', compact('users', 'roles'));
    }

    /**
     * حفظ مسؤول جديد
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'roles' => 'required|array',
            'roles.*' => 'exists:roles,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $user = User::findOrFail($request->user_id);
        
        // تعيين المستخدم كمسؤول
        $user->is_admin = true;
        $user->save();
        
        // تعيين الأدوار للمستخدم
        $user->syncRoles($request->roles);

        return redirect()->route('admin.user-roles.index')
            ->with('success', 'تم إضافة المسؤول بنجاح');
    }

    /**
     * إزالة صلاحيات المسؤول من المستخدم
     */
    public function destroy($id)
    {
        $user = User::findOrFail($id);
        
        // التحقق من أنه ليس المستخدم الحالي
        if ($user->id === auth()->id()) {
            return redirect()->back()
                ->with('error', 'لا يمكنك إزالة صلاحيات المسؤول من نفسك');
        }
        
        // إزالة جميع الأدوار
        $user->roles()->detach();
        
        // إزالة صلاحية المسؤول
        $user->is_admin = false;
        $user->save();

        return redirect()->route('admin.user-roles.index')
            ->with('success', 'تم إزالة صلاحيات المسؤول من المستخدم بنجاح');
    }
}
