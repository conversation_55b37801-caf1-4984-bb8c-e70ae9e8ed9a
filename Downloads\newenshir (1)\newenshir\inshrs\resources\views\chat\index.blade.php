@extends('layouts.dashboard')

@section('title', 'المحادثات')

@section('content')
<div class="chat-container">
    <div class="chat-header">
        <h1 class="chat-title">المحادثات</h1>
    </div>

    @if(isset($error))
        <div class="error-message">
            <i class="fas fa-exclamation-circle"></i>
            <p>{{ $error }}</p>
        </div>
    @endif

    <div class="conversations-list">
        @if($conversations->count() > 0)
            @foreach($conversations as $conversation)
                @php
                    $otherUser = ($conversation->sender_id == Auth::id())
                        ? $conversation->receiver
                        : $conversation->sender;

                    $lastMessage = $conversation->lastMessage;
                    $unread = !$conversation->is_read && $conversation->lastMessage && $conversation->lastMessage->sender_id != Auth::id();
                @endphp

                <a href="{{ route('chat.show', $conversation->id) }}" class="conversation-item {{ $unread ? 'unread' : '' }}">
                    <div class="conversation-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="conversation-content">
                        <div class="conversation-header">
                            <h3 class="conversation-name">{{ $otherUser->name }}</h3>
                            @if($lastMessage)
                                <span class="conversation-time">{{ $lastMessage->created_at->diffForHumans() }}</span>
                            @endif
                        </div>
                        <div class="conversation-message">
                            @if($lastMessage)
                                {{ \Illuminate\Support\Str::limit($lastMessage->message, 50) }}
                            @else
                                <span class="no-messages">لا توجد رسائل</span>
                            @endif
                        </div>
                        @if($conversation->ad_id)
                            <div class="conversation-item-type">
                                <i class="fas fa-ad"></i> إعلان
                            </div>
                        @elseif($conversation->job_id)
                            <div class="conversation-item-type">
                                <i class="fas fa-briefcase"></i> وظيفة
                            </div>
                        @endif
                    </div>
                    @if($unread)
                        <div class="unread-badge"></div>
                    @endif
                </a>
            @endforeach
        @else
            <div class="no-conversations">
                <i class="fas fa-comments"></i>
                <p>لا توجد محادثات</p>
            </div>
        @endif
    </div>
</div>

<style>
    .chat-container {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }

    .chat-header {
        padding: 1.5rem;
        border-bottom: 1px solid #f0f0f0;
    }

    .chat-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    .conversations-list {
        padding: 0;
    }

    .conversation-item {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #f0f0f0;
        text-decoration: none;
        color: #333;
        transition: background-color 0.3s ease;
        position: relative;
    }

    .conversation-item:hover {
        background-color: #f9f9f9;
    }

    .conversation-item.unread {
        background-color: rgba(230, 126, 34, 0.05);
    }

    .conversation-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: #f3f4f6;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        color: #E67E22;
        font-size: 1.5rem;
    }

    .conversation-content {
        flex: 1;
    }

    .conversation-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.25rem;
    }

    .conversation-name {
        font-size: 1rem;
        font-weight: 600;
        margin: 0;
    }

    .conversation-time {
        font-size: 0.75rem;
        color: #666;
    }

    .conversation-message {
        font-size: 0.875rem;
        color: #666;
        margin-bottom: 0.25rem;
    }

    .conversation-item-type {
        font-size: 0.75rem;
        color: #E67E22;
    }

    .unread-badge {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #E67E22;
        position: absolute;
        left: 1.5rem;
    }

    .no-conversations {
        padding: 3rem 1.5rem;
        text-align: center;
        color: #666;
    }

    .no-conversations i {
        font-size: 3rem;
        color: #ddd;
        margin-bottom: 1rem;
    }

    .no-conversations p {
        font-size: 1rem;
        margin: 0;
    }

    .no-messages {
        font-style: italic;
        color: #999;
    }

    .error-message {
        background-color: #fff3f3;
        border: 1px solid #ffcaca;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        color: #e53e3e;
    }

    .error-message i {
        font-size: 1.5rem;
        margin-left: 1rem;
    }

    .error-message p {
        margin: 0;
        font-weight: 500;
    }
</style>
@endsection
