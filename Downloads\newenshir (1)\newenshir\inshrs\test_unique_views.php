<?php

// اختبار نظام المشاهدات الفريدة
// تشغيل: php test_unique_views.php

require_once 'vendor/autoload.php';

// تحميل إعدادات Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Ad;
use App\Models\AdView;
use App\Services\ViewTrackingService;
use Illuminate\Support\Facades\Schema;

echo "🧪 اختبار نظام المشاهدات الفريدة\n";
echo "==================================\n\n";

try {
    // 1. فحص جدول ad_views
    echo "1️⃣ فحص جدول المشاهدات:\n";
    
    if (Schema::hasTable('ad_views')) {
        echo "   ✅ جدول 'ad_views' موجود\n";
        
        // فحص الأعمدة المطلوبة
        $requiredColumns = ['ad_id', 'ip_address', 'user_agent_hash', 'user_id', 'session_id', 'fingerprint', 'viewed_at'];
        $missingColumns = [];
        
        foreach ($requiredColumns as $column) {
            if (!Schema::hasColumn('ad_views', $column)) {
                $missingColumns[] = $column;
            }
        }
        
        if (empty($missingColumns)) {
            echo "   ✅ جميع الأعمدة المطلوبة موجودة\n";
        } else {
            echo "   ❌ أعمدة مفقودة: " . implode(', ', $missingColumns) . "\n";
        }
        
        // إحصائيات الجدول
        $totalViews = AdView::count();
        echo "   📊 إجمالي المشاهدات المسجلة: {$totalViews}\n";
        
        if ($totalViews > 0) {
            $uniqueIps = AdView::distinct('ip_address')->count();
            $registeredUsers = AdView::whereNotNull('user_id')->distinct('user_id')->count();
            $todayViews = AdView::whereDate('viewed_at', today())->count();
            
            echo "   🌐 عناوين IP فريدة: {$uniqueIps}\n";
            echo "   👤 مستخدمين مسجلين: {$registeredUsers}\n";
            echo "   📅 مشاهدات اليوم: {$todayViews}\n";
        }
        
    } else {
        echo "   ❌ جدول 'ad_views' غير موجود\n";
        echo "   🔧 تشغيل: php artisan migrate\n";
    }

    // 2. فحص نموذج AdView
    echo "\n2️⃣ فحص نموذج AdView:\n";
    
    if (class_exists('App\Models\AdView')) {
        echo "   ✅ نموذج AdView موجود\n";
        
        // فحص الدوال المطلوبة
        $requiredMethods = ['hasViewed', 'recordView', 'getAdViewStats', 'generateFingerprint'];
        $missingMethods = [];
        
        foreach ($requiredMethods as $method) {
            if (!method_exists(AdView::class, $method)) {
                $missingMethods[] = $method;
            }
        }
        
        if (empty($missingMethods)) {
            echo "   ✅ جميع الدوال المطلوبة موجودة\n";
        } else {
            echo "   ❌ دوال مفقودة: " . implode(', ', $missingMethods) . "\n";
        }
        
    } else {
        echo "   ❌ نموذج AdView غير موجود\n";
    }

    // 3. فحص خدمة ViewTrackingService
    echo "\n3️⃣ فحص خدمة ViewTrackingService:\n";
    
    if (class_exists('App\Services\ViewTrackingService')) {
        echo "   ✅ خدمة ViewTrackingService موجودة\n";
        
        // اختبار إنشاء instance
        try {
            $service = new ViewTrackingService();
            echo "   ✅ يمكن إنشاء instance من الخدمة\n";
            
            // فحص الدوال المطلوبة
            $requiredMethods = ['trackAdView', 'getAdViewStats', 'cleanOldViews', 'recalculateAdViews'];
            $missingMethods = [];
            
            foreach ($requiredMethods as $method) {
                if (!method_exists($service, $method)) {
                    $missingMethods[] = $method;
                }
            }
            
            if (empty($missingMethods)) {
                echo "   ✅ جميع دوال الخدمة موجودة\n";
            } else {
                echo "   ❌ دوال مفقودة: " . implode(', ', $missingMethods) . "\n";
            }
            
        } catch (Exception $e) {
            echo "   ❌ خطأ في إنشاء الخدمة: " . $e->getMessage() . "\n";
        }
        
    } else {
        echo "   ❌ خدمة ViewTrackingService غير موجودة\n";
    }

    // 4. فحص Commands
    echo "\n4️⃣ فحص Commands:\n";
    
    $commands = [
        'CleanOldAdViews' => 'app/Console/Commands/CleanOldAdViews.php',
        'RecalculateAdViews' => 'app/Console/Commands/RecalculateAdViews.php'
    ];
    
    foreach ($commands as $name => $path) {
        if (file_exists($path)) {
            echo "   ✅ {$name} موجود\n";
        } else {
            echo "   ❌ {$name} غير موجود\n";
        }
    }

    // 5. فحص Middleware
    echo "\n5️⃣ فحص Middleware:\n";
    
    $middlewarePath = 'app/Http/Middleware/ViewSpamProtection.php';
    if (file_exists($middlewarePath)) {
        echo "   ✅ ViewSpamProtection middleware موجود\n";
    } else {
        echo "   ❌ ViewSpamProtection middleware غير موجود\n";
    }

    // 6. اختبار وظائف النظام
    echo "\n6️⃣ اختبار وظائف النظام:\n";
    
    if (Schema::hasTable('ad_views') && class_exists('App\Models\AdView')) {
        
        // اختبار hash الـ user agent
        $testUserAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';
        $hash1 = AdView::hashUserAgent($testUserAgent);
        $hash2 = AdView::hashUserAgent($testUserAgent);
        
        if ($hash1 === $hash2 && strlen($hash1) === 64) {
            echo "   ✅ دالة hash الـ user agent تعمل بشكل صحيح\n";
        } else {
            echo "   ❌ مشكلة في دالة hash الـ user agent\n";
        }
        
        // اختبار إنشاء بصمة الجهاز
        try {
            // محاكاة request
            $mockRequest = new \Illuminate\Http\Request();
            $mockRequest->server->set('REMOTE_ADDR', '192.168.1.1');
            $mockRequest->headers->set('User-Agent', $testUserAgent);
            $mockRequest->headers->set('Accept-Language', 'ar,en');
            
            $fingerprint = AdView::generateFingerprint($mockRequest);
            
            if (strlen($fingerprint) === 64) {
                echo "   ✅ دالة إنشاء بصمة الجهاز تعمل بشكل صحيح\n";
            } else {
                echo "   ❌ مشكلة في دالة إنشاء بصمة الجهاز\n";
            }
            
        } catch (Exception $e) {
            echo "   ❌ خطأ في اختبار بصمة الجهاز: " . $e->getMessage() . "\n";
        }
    }

    // 7. إحصائيات الإعلانات
    echo "\n7️⃣ إحصائيات الإعلانات:\n";
    
    $totalAds = Ad::count();
    echo "   📊 إجمالي الإعلانات: {$totalAds}\n";
    
    if ($totalAds > 0 && Schema::hasTable('ad_views')) {
        // الإعلانات التي لديها مشاهدات مسجلة
        $adsWithTrackedViews = Ad::whereHas('adViews')->count();
        echo "   👁️ إعلانات لديها مشاهدات مسجلة: {$adsWithTrackedViews}\n";
        
        // أكثر الإعلانات مشاهدة
        $mostViewed = Ad::orderBy('views', 'desc')->first();
        if ($mostViewed) {
            $title = substr($mostViewed->title, 0, 30) . '...';
            echo "   🏆 أكثر إعلان مشاهدة: {$title} ({$mostViewed->views} مشاهدة)\n";
        }
        
        // إحصائيات اليوم
        if (class_exists('App\Services\ViewTrackingService')) {
            try {
                $service = new ViewTrackingService();
                $generalStats = $service->getGeneralStats();
                
                echo "   📅 مشاهدات اليوم: {$generalStats['total_views_today']}\n";
                echo "   👥 زوار فريدين اليوم: {$generalStats['unique_viewers_today']}\n";
                
            } catch (Exception $e) {
                echo "   ⚠️ لا يمكن الحصول على الإحصائيات: " . $e->getMessage() . "\n";
            }
        }
    }

    echo "\n🎯 خلاصة الاختبار:\n";
    echo "==================\n";
    
    $allGood = true;
    $issues = [];
    
    // فحص المتطلبات الأساسية
    if (!Schema::hasTable('ad_views')) {
        $issues[] = "جدول ad_views غير موجود";
        $allGood = false;
    }
    
    if (!class_exists('App\Models\AdView')) {
        $issues[] = "نموذج AdView غير موجود";
        $allGood = false;
    }
    
    if (!class_exists('App\Services\ViewTrackingService')) {
        $issues[] = "خدمة ViewTrackingService غير موجودة";
        $allGood = false;
    }
    
    if ($allGood) {
        echo "🎉 نظام المشاهدات الفريدة جاهز للاستخدام!\n";
        echo "✅ جدول المشاهدات موجود ومكتمل\n";
        echo "✅ جميع النماذج والخدمات متاحة\n";
        echo "✅ الدوال تعمل بشكل صحيح\n";
        echo "✅ Commands و Middleware جاهزة\n\n";
        
        echo "📋 خطوات التشغيل:\n";
        echo "================\n";
        echo "1. php artisan migrate (إذا لم يتم تنفيذه)\n";
        echo "2. تطبيق middleware على الروابط (اختياري)\n";
        echo "3. php artisan ads:recalculate-views (لإعادة حساب المشاهدات)\n";
        echo "4. تصفح الإعلانات لاختبار النظام\n";
        
    } else {
        echo "⚠️ هناك مشاكل تحتاج إلى حل:\n";
        foreach ($issues as $issue) {
            echo "❌ {$issue}\n";
        }
        
        echo "\n🔧 حلول مقترحة:\n";
        echo "1. php artisan migrate\n";
        echo "2. التأكد من وجود جميع الملفات المطلوبة\n";
        echo "3. php artisan config:clear\n";
        echo "4. composer dump-autoload\n";
    }

} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
    echo "\nتفاصيل الخطأ:\n";
    echo $e->getTraceAsString() . "\n";
}

echo "\n📚 للمزيد من المعلومات:\n";
echo "========================\n";
echo "- راجع ملف UNIQUE_VIEWS_SYSTEM.md\n";
echo "- راجع ملف ADS_VIEWS_SYSTEM.md\n";

?>
