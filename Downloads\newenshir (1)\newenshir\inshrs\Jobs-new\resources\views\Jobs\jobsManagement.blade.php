<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الوظائف</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --light-bg: #f3f4f6;
            --border-color: #e5e7eb;
        }

        body {
            background-color: var(--light-bg);
            padding: 10px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
        }

        .header {
            background-color: var(--primary-color);
            color: white;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .header h1 {
            font-size: clamp(1.2rem, 4vw, 1.5rem);
        }

        .add-job-btn {
            background-color: white;
            color: var(--primary-color);
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            white-space: nowrap;
        }

        .filters {
            padding: 15px;
            background: #f8fafc;
            border-bottom: 1px solid var(--border-color);
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .filter-input {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            width: 100%;
        }

        /* جدول للشاشات الكبيرة */
        .jobs-table {
            width: 100%;
            border-collapse: collapse;
        }

        .jobs-table th,
        .jobs-table td {
            padding: 12px 15px;
            text-align: right;
            border-bottom: 1px solid var(--border-color);
        }

        .jobs-table th {
            background-color: #f8fafc;
            font-weight: 600;
            color: #374151;
        }

        /* بطاقات للشاشات الصغيرة */
        .jobs-cards {
            display: none;
            padding: 15px;
        }

        .job-card {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .job-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .job-card-title {
            font-size: 1.1rem;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .job-card-company {
            color: #666;
            font-size: 0.9rem;
        }

        .job-card-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }

        .job-card-detail {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-size: 0.8rem;
            color: #666;
        }

        .detail-value {
            font-size: 0.9rem;
            color: #333;
        }

        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.85rem;
            font-weight: 500;
            display: inline-block;
        }

        .status-active {
            background-color: #dcfce7;
            color: #166534;
        }

        .status-closed {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .actions {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            flex: 1;
        }

        .edit-btn {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .delete-btn {
            background-color: #fee2e2;
            color: #991b1b;
        }
        
        .stop-btn {
            background-color: #ffedd5;
            color: #b45309;
        }

        .pagination {
            display: flex;
            justify-content: center;
            padding: 15px;
            gap: 8px;
            flex-wrap: wrap;
        }

        .page-btn {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            background: white;
            border-radius: 4px;
            cursor: pointer;
            min-width: 40px;
            text-align: center;
        }

        .page-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

 
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>إدارة الوظائف</h1>
            <button class="add-job-btn"  onclick="window.location='{{ url('/post-job-company') }}'" >
                           <i class="fas fa-plus"></i>
                           إضافة وظيفة جديدة
                       </button>
                       
                   </header>
                     <a href="{{ url('/job-applications') }}">عرض طلبات التوظيف</a>
        <div class="filters">
            <input type="text" class="filter-input" placeholder="البحث بعنوان الوظيفة">
            <select class="filter-input">
                <option value="">الموقع</option>
                <option value="riyadh">الرياض</option>
                <option value="jeddah">جدة</option>
                <option value="dammam">الدمام</option>
            </select>
            <select class="filter-input">
                <option value="">التصنيف</option>
                <option value="it">تقنية المعلومات</option>
                <option value="marketing">تسويق</option>
                <option value="management">إدارة</option>
            </select>
            <select class="filter-input">
                <option value="">الحالة</option>
                <option value="active">نشط</option>
                <option value="closed">مغلق</option>
            </select>
        </div>

        <!-- جدول للشاشات الكبيرة -->
        <table class="jobs-table">
            <thead>
                <tr>
                    <th>عنوان الوظيفة</th>
                    <th>الشركة</th>
                    <th>الموقع</th>
                    <th>التصنيف</th>
                    <th>نطاق الراتب</th>
                    <th>تاريخ النشر</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>مطور واجهات أمامية</td>
                    <td>شركة التقنية المتقدمة</td>
                    <td>الرياض</td>
                    <td>تقنية المعلومات</td>
                    <td>15,000 - 20,000 ريال</td>
                    <td>2024-02-13</td>
                    <td><span class="status status-active">نشط</span></td>
                    <td>
                        <div class="actions">
                           <button class="action-btn edit-btn" onclick="window.location='{{ url('/edit-job') }}'">
                                <i class="fas fa-edit"></i>
                                 تعديل
                            </button>
                            <button class="action-btn stop-btn">
                               <i class="fas fa-stop-circle"></i>
                               توقيف
                           </button>
                           <button class="action-btn delete-btn">
                               <i class="fas fa-trash-alt"></i>
                               حذف
                           </button>
                       </div>
                    </td>
                </tr>
                <tr>
                    <td>مدير تسويق</td>
                    <td>شركة الحلول الرقمية</td>
                    <td>جدة</td>
                    <td>تسويق</td>
                    <td>20,000 - 25,000 ريال</td>
                    <td>2024-02-12</td>
                    <td><span class="status status-closed">مغلق</span></td>
                    <td>
                        <div class="actions">
                            <button class="action-btn edit-btn">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </button>
                            <button class="action-btn delete-btn">
                                <i class="fas fa-trash-alt"></i>
                                حذف
                            </button>
                            <button class="action-btn stop-btn">
                                <i class="fas fa-stop-circle"></i>
                                توقيف
                            </button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>


<script>


const jobsPerPage = 4;
let currentPage = 1;
let jobs = [];

function fetchJobs() {
    // بيانات تجريبية للوظائف
    jobs = [
        { title: "مطور واجهات أمامية", company: "شركة التقنية", location: "الرياض", category: "تقنية المعلومات", salary: "15,000 - 20,000 ريال", date: "2024-02-13", status: "نشط" },
        { title: "مدير تسويق", company: "شركة الحلول", location: "جدة", category: "تسويق", salary: "20,000 - 25,000 ريال", date: "2024-02-12", status: "مغلق" },
        { title: "محلل بيانات", company: "شركة البيانات", location: "الدمام", category: "تحليل البيانات", salary: "18,000 - 22,000 ريال", date: "2024-02-10", status: "نشط" },
        { title: "مهندس شبكات", company: "شركة الاتصالات", location: "الرياض", category: "الشبكات", salary: "14,000 - 19,000 ريال", date: "2024-02-08", status: "نشط" },
        { title: "مصمم جرافيك", company: "وكالة الإبداع", location: "جدة", category: "التصميم", salary: "10,000 - 15,000 ريال", date: "2024-02-06", status: "مغلق" }
    ];
    renderJobs();
}
function renderJobs() {
    const start = (currentPage - 1) * jobsPerPage;
    const end = start + jobsPerPage;
    const visibleJobs = jobs.slice(start, end);
    const tableBody = document.querySelector(".jobs-table tbody");
    tableBody.innerHTML = "";
    
    visibleJobs.forEach(job => {
        const row = `<tr>
            <td>${job.title}</td>
            <td>${job.company}</td>
            <td>${job.location}</td>
            <td>${job.category}</td>
            <td>${job.salary}</td>
            <td>${job.date}</td>
            <td><span class="status ${job.status === 'نشط' ? 'status-active' : 'status-closed'}">${job.status}</span></td>
            <td><button class="action-btn edit-btn" onclick="window.location='{{ url('/edit-job') }}'">تعديل</button>
                     <button class="action-btn stop-btn">توقيف</button>
                    <button class="action-btn delete-btn" onclick="return confirm('هل أنت متأكد أنك تريد حذف هذه الوظيفة؟')">حذف</button>
                       <button class="action-btn stop-btn" onclick="window.location='{{ url('/show-job-company') }}'">عرض</button>
</td>
        </tr>`;
        tableBody.innerHTML += row;
    });
    updatePagination();
}

function updatePagination() {
    const pageCount = Math.ceil(jobs.length / jobsPerPage);
    const pagination = document.querySelector(".pagination");
    pagination.innerHTML = `<button class="page-btn" onclick="changePage(-1)" ${currentPage === 1 ? 'disabled' : ''}>السابق</button>`;
    
    for (let i = 1; i <= pageCount; i++) {
        pagination.innerHTML += `<button class="page-btn ${currentPage === i ? 'active' : ''}" onclick="goToPage(${i})">${i}</button>`;
    }
    
    pagination.innerHTML += `<button class="page-btn" onclick="changePage(1)" ${currentPage === pageCount ? 'disabled' : ''}>التالي</button>`;
}

function changePage(step) {
    currentPage += step;
    renderJobs();
}

function goToPage(page) {
    currentPage = page;
    renderJobs();
}

document.addEventListener("DOMContentLoaded", fetchJobs);

</script>

        
         
    </div>
</body>
</html>