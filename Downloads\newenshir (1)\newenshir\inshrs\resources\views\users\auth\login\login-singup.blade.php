<!DOCTYPE html>
<html lang="en" >
<head>
  <meta charset="UTF-8">
  <title>Sign up / Login Form</title>
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="{{ asset('css/style.css') }}">
  <style>
    .error-message {
      color: #e74c3c;
      background-color: rgba(231, 76, 60, 0.1);
      border-left: 3px solid #e74c3c;
      padding: 10px;
      margin: 10px 0;
      font-size: 14px;
      border-radius: 4px;
    }
  </style>
</head>
<body>
<!-- partial:index.partial.html -->
<!DOCTYPE html>
<html>
<head>
	<title>Slide Navbar</title>
	<link rel="stylesheet" type="text/css" href="slide navbar style.css">
<link href="https://fonts.googleapis.com/css2?family=Jost:wght@500&display=swap" rel="stylesheet">
</head>
<body>
	<div class="main">
		<input type="checkbox" id="chk" aria-hidden="true">

			<div class="signup">
				<form method="POST" action="{{ route('register') }}">
					@csrf
					<label for="chk" aria-hidden="true">Sign up</label>

					@if ($errors->any() && !old('email', false))
						<div class="error-message">
							<ul>
								@foreach ($errors->all() as $error)
									<li>{{ $error }}</li>
								@endforeach
							</ul>
						</div>
					@endif

					<input type="text" name="name" placeholder="User name" required="" value="{{ old('name') }}">
					<input type="email" name="email" placeholder="Email" required="" value="{{ old('email') }}">
					<input type="number" name="phone" placeholder="Phone Number" value="{{ old('phone') }}">
					<input type="password" name="password" placeholder="Password" required="">
					<input type="password" name="password_confirmation" placeholder="Confirm Password" required="">
					<button type="submit">Sign up</button>
				</form>
			</div>

			<div class="login">
				<form method="POST" action="{{ route('login') }}">
					@csrf
					<label for="chk" aria-hidden="true" >Login</label>

					@if ($errors->any() && old('email', false))
						<div class="error-message">
							<ul>
								@foreach ($errors->all() as $error)
									<li>{{ $error }}</li>
								@endforeach
							</ul>
						</div>
					@endif

					<input type="email" name="email" placeholder="Email" required="" value="{{ old('email') }}">
					<input type="password" name="password" placeholder="Password" required="">
					<button type="submit">Login</button>
				</form>
			</div>
	</div>
</body>
</html>
<!-- partial -->

</body>
</html>
