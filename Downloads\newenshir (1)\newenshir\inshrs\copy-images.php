<?php
/**
 * سكريبت لنسخ الصور من storage/app/public إلى public/images
 */

// تحديد المسارات
$sourceDir = __DIR__ . '/storage/app/public';
$destDir = __DIR__ . '/public/images';

// التأكد من وجود المجلدات
if (!is_dir($sourceDir)) {
    die("المجلد المصدر غير موجود: $sourceDir\n");
}

if (!is_dir($destDir)) {
    if (!mkdir($destDir, 0755, true)) {
        die("فشل في إنشاء المجلد الهدف: $destDir\n");
    }
    echo "تم إنشاء المجلد الهدف: $destDir\n";
}

/**
 * دالة لنسخ المجلد بشكل متكرر
 */
function copyDirectory($source, $destination) {
    if (!is_dir($destination)) {
        mkdir($destination, 0755, true);
    }
    
    $dir = opendir($source);
    while (($file = readdir($dir)) !== false) {
        if ($file != '.' && $file != '..') {
            $sourcePath = $source . '/' . $file;
            $destPath = $destination . '/' . $file;
            
            if (is_dir($sourcePath)) {
                copyDirectory($sourcePath, $destPath);
            } else {
                if (file_exists($destPath)) {
                    echo "الملف موجود بالفعل، تخطي: $destPath\n";
                } else {
                    if (copy($sourcePath, $destPath)) {
                        echo "تم نسخ: $sourcePath إلى $destPath\n";
                    } else {
                        echo "فشل في نسخ: $sourcePath إلى $destPath\n";
                    }
                }
            }
        }
    }
    closedir($dir);
}

// نسخ المجلد
echo "بدء نسخ الملفات من $sourceDir إلى $destDir\n";
copyDirectory($sourceDir, $destDir);
echo "تم الانتهاء من نسخ الملفات\n";

// تحديث قاعدة البيانات (اختياري - يمكن تنفيذه لاحقًا)
echo "\nملاحظة: قد تحتاج إلى تحديث مسارات الصور في قاعدة البيانات.\n";
echo "يمكنك استخدام سكريبت migrate-images.php لتحديث قاعدة البيانات.\n";
