# حل خطأ إعدادات المستخدم

## 🚨 **المشكلة:**
```
Call to a member function count() on array
```

## 🔧 **الحل:**

### 1. **تشغيل Migration أولاً:**
```bash
php artisan migrate
```

### 2. **إذا لم تعمل Migration، قم بإنشائها يدوياً:**
```sql
ALTER TABLE users ADD COLUMN allow_messages BOOLEAN DEFAULT TRUE;
ALTER TABLE users ADD COLUMN allow_comments BOOLEAN DEFAULT TRUE;
ALTER TABLE users ADD COLUMN show_phone BOOLEAN DEFAULT TRUE;
ALTER TABLE users ADD COLUMN show_email BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN show_online_status BOOLEAN DEFAULT TRUE;
ALTER TABLE users ADD COLUMN email_notifications BOOLEAN DEFAULT TRUE;
ALTER TABLE users ADD COLUMN sms_notifications BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN push_notifications BOOLEAN DEFAULT TRUE;
ALTER TABLE users ADD COLUMN marketing_emails BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN profile_public BOOLEAN DEFAULT TRUE;
ALTER TABLE users ADD COLUMN show_ads_count BOOLEAN DEFAULT TRUE;
ALTER TABLE users ADD COLUMN show_join_date BOOLEAN DEFAULT TRUE;
ALTER TABLE users ADD COLUMN two_factor_enabled BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN login_alerts BOOLEAN DEFAULT TRUE;
ALTER TABLE users ADD COLUMN blocked_users TEXT NULL;
ALTER TABLE users ADD COLUMN preferred_language VARCHAR(10) DEFAULT 'ar';
ALTER TABLE users ADD COLUMN theme_preference VARCHAR(20) DEFAULT 'light';
ALTER TABLE users ADD COLUMN timezone VARCHAR(50) DEFAULT 'Asia/Riyadh';
ALTER TABLE users ADD COLUMN searchable_profile BOOLEAN DEFAULT TRUE;
ALTER TABLE users ADD COLUMN show_in_suggestions BOOLEAN DEFAULT TRUE;
ALTER TABLE users ADD COLUMN privacy_updated_at TIMESTAMP NULL;
ALTER TABLE users ADD COLUMN last_seen TIMESTAMP NULL;
```

### 3. **اختبار الصفحة:**
اذهب إلى:
```
/user/settings/test
```

### 4. **إذا كانت المشكلة مستمرة، استخدم هذا الحل المؤقت:**

في `app/Http/Controllers/UserSettingsController.php`:

```php
public function index()
{
    $user = Auth::user();
    
    // حل مؤقت للمستخدمين المحظورين
    $blockedUsers = collect();
    
    try {
        if ($user->blocked_users) {
            $blockedUserIds = json_decode($user->blocked_users, true);
            if (is_array($blockedUserIds) && !empty($blockedUserIds)) {
                $blockedUsers = User::whereIn('id', $blockedUserIds)->get(['id', 'name', 'email']);
            }
        }
    } catch (\Exception $e) {
        // في حالة حدوث خطأ، استخدم collection فارغ
        $blockedUsers = collect();
    }

    return view('user.settings.index', [
        'user' => $user,
        'blockedUsers' => $blockedUsers
    ]);
}
```

### 5. **في View، استخدم:**

```blade
@if($blockedUsers && $blockedUsers->count() > 0)
    <!-- عرض المستخدمين المحظورين -->
@else
    <!-- لا توجد مستخدمون محظورون -->
@endif
```

## 🎯 **خطوات الاختبار:**

### 1. **تسجيل الدخول:**
- سجل دخول كمستخدم

### 2. **اذهب للاختبار:**
```
/user/settings/test
```

### 3. **تحقق من البيانات:**
- هل تظهر معلومات المستخدم؟
- هل تظهر إعدادات الخصوصية؟

### 4. **اذهب للإعدادات:**
```
/user/settings
```

### 5. **اختبر التبويبات:**
- الخصوصية
- الإشعارات  
- التفضيلات
- الأمان
- المحظورون

## 🔄 **إذا استمرت المشكلة:**

### **الحل البديل:**
قم بتعديل View مؤقتاً:

```blade
@php
    $blockedCount = 0;
    $blockedUsersList = [];
    
    if (Auth::user()->blocked_users) {
        $blockedIds = json_decode(Auth::user()->blocked_users, true);
        if (is_array($blockedIds)) {
            $blockedCount = count($blockedIds);
            $blockedUsersList = \App\Models\User::whereIn('id', $blockedIds)->get();
        }
    }
@endphp

@if($blockedCount > 0)
    <div class="blocked-users-list">
        @foreach($blockedUsersList as $blockedUser)
            <!-- عرض المستخدم المحظور -->
        @endforeach
    </div>
@else
    <div class="text-center py-4">
        <p class="text-muted mt-3">لا توجد مستخدمون محظورون</p>
    </div>
@endif
```

## ✅ **التأكد من النجاح:**

### **يجب أن تعمل الميزات التالية:**
- ✅ عرض صفحة الإعدادات
- ✅ تبديل التبويبات
- ✅ حفظ إعدادات الخصوصية
- ✅ حفظ إعدادات الإشعارات
- ✅ تغيير التفضيلات
- ✅ عرض قائمة المحظورين (حتى لو فارغة)

## 🚀 **بعد الحل:**

### **اختبر:**
1. احفظ إعدادات الخصوصية
2. احفظ إعدادات الإشعارات  
3. غير اللغة والمظهر
4. جرب حظر مستخدم (بـ ID صحيح)
5. جرب إلغاء الحظر

### **تأكد من:**
- عدم ظهور أخطاء
- حفظ البيانات في قاعدة البيانات
- عمل جميع التبويبات

النظام سيعمل بشكل مثالي بعد هذه الخطوات! 🎉
