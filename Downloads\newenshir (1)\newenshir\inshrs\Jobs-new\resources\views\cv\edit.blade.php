<!-- resources/views/cv/edit.blade.php -->
@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row">
        <div class="col-md-4">
            <!-- لوحة التحكم -->
            <div class="card">
                <div class="card-header">تخصيص السيرة الذاتية</div>
                <div class="card-body">
                    <form id="colorForm">
                        <div class="mb-3">
                            <label>اللون الرئيسي</label>
                            <input type="color" class="form-control" name="primary_color" 
                                   value="{{ $cv->primary_color }}" data-color-update>
                        </div>
                        <div class="mb-3">
                            <label>اللون الثانوي</label>
                            <input type="color" class="form-control" name="secondary_color" 
                                   value="{{ $cv->secondary_color }}" data-color-update>
                        </div>
                    </form>

                    <!-- منطقة السحب والإفلات -->
                    <div class="mt-4">
                        <h5>ترتيب الأقسام</h5>
                        <ul class="list-group" id="sortableSections">
                            @foreach($cv->sections_order as $section)
                                <li class="list-group-item" data-section="{{ $section }}">
                                    {{ __("cv.sections.$section") }}
                                    <i class="bi bi-grip-vertical float-end"></i>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <!-- معاينة السيرة الذاتية -->
            <div class="card">
                <div class="card-body" id="cvPreview">
                    <!-- نفس محتوى السيرة الذاتية السابق -->
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.14.0/Sortable.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة السحب والإفلات
    new Sortable(document.getElementById('sortableSections'), {
        animation: 150,
        ghostClass: 'bg-light',
        onEnd: function() {
            const newOrder = Array.from(document.querySelectorAll('#sortableSections li'))
                .map(item => item.dataset.section);
            
            fetch('{{ route("cv.sections") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({ sections_order: newOrder })
            });
        }
    });

    // تحديث الألوان مباشرة
    document.querySelectorAll('[data-color-update]').forEach(input => {
        input.addEventListener('change', function() {
            const formData = new FormData(document.getElementById('colorForm'));
            
            fetch('{{ route("cv.colors") }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: formData
            });

            // تحديث الألوان مباشرة في المعاينة
            document.documentElement.style.setProperty(
                `--${this.name.replace('_', '-')}`, 
                this.value
            );
        });
    });
});
</script>
@endpush