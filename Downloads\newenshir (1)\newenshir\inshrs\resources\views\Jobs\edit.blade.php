<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل الوظيفة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .form-label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }
        .form-control:focus {
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
            border-color: #86b7fe;
        }
        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
            padding: 10px 24px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background-color: #0b5ed7;
            border-color: #0a58ca;
            transform: translateY(-2px);
        }
        .featured-box {
            background-color: #fff8e1;
            border: 1px solid #ffecb3;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .page-header {
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 30px;
            padding-bottom: 15px;
        }
        .alert {
            border-radius: 8px;
            padding: 12px 20px;
        }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #6c757d;
            text-decoration: none;
            transition: color 0.2s;
        }
        .back-link:hover {
            color: #0d6efd;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="{{ route('jobs.myJobs') }}" class="back-link">
            <i class="fas fa-arrow-right ml-1"></i> العودة إلى قائمة الوظائف
        </a>

        <div class="page-header">
            <h1 class="mb-0 fw-bold text-primary">
                <i class="fas fa-edit me-2"></i>تعديل الوظيفة
            </h1>
        </div>

        @if ($errors->any())
            <div class="alert alert-danger mb-4">
                <ul class="mb-0">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form method="POST" action="{{ route('jobs.update', $job->id) }}" class="needs-validation" novalidate>
            @csrf
            @method('POST')

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="job_title" class="form-label">عنوان الوظيفة</label>
                    <input type="text" id="job_title" name="job_title" class="form-control @error('job_title') is-invalid @enderror" value="{{ old('job_title', $job->job_title) }}" required>
                    @error('job_title')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="col-md-6 mb-3">
                    <label for="company_name" class="form-label">اسم الشركة</label>
                    <input type="text" id="company_name" name="company_name" class="form-control @error('company_name') is-invalid @enderror" value="{{ old('company_name', $job->company_name) }}" required>
                    @error('company_name')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="location" class="form-label">الموقع</label>
                    <input type="text" id="location" name="location" class="form-control @error('location') is-invalid @enderror" value="{{ old('location', $job->location) }}" required>
                    @error('location')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="col-md-6 mb-3">
                    <label for="salary" class="form-label">الراتب</label>
                    <input type="number" id="salary" name="salary" class="form-control @error('salary') is-invalid @enderror" value="{{ old('salary', $job->salary) }}" required>
                    @error('salary')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <div class="mb-3">
                <label for="experience_required" class="form-label">الخبرة المطلوبة</label>
                <input type="text" id="experience_required" name="experience_required" class="form-control @error('experience_required') is-invalid @enderror" value="{{ old('experience_required', $job->experience_required) }}" required>
                @error('experience_required')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3">
                <label for="job_description" class="form-label">الوصف الوظيفي</label>
                <textarea id="job_description" name="job_description" class="form-control @error('job_description') is-invalid @enderror" rows="6" required>{{ old('job_description', $job->job_description) }}</textarea>
                @error('job_description')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <h4 class="mt-4 mb-3 text-primary">معلومات الاتصال</h4>

            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="whatsapp" class="form-label">رقم الواتساب</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fab fa-whatsapp text-success"></i></span>
                        <input type="text" id="whatsapp" name="whatsapp" class="form-control @error('whatsapp') is-invalid @enderror" value="{{ old('whatsapp', $job->whatsapp) }}" required>
                    </div>
                    @error('whatsapp')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="col-md-4 mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-envelope text-primary"></i></span>
                        <input type="email" id="email" name="email" class="form-control @error('email') is-invalid @enderror" value="{{ old('email', $job->email) }}" required>
                    </div>
                    @error('email')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="col-md-4 mb-3">
                    <label for="phone" class="form-label">رقم الهاتف</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-phone text-secondary"></i></span>
                        <input type="text" id="phone" name="phone" class="form-control @error('phone') is-invalid @enderror" value="{{ old('phone', $job->phone) }}" required>
                    </div>
                    @error('phone')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <!-- خيار الإعلان المميز -->
            @php
                $userPoints = auth()->user()->points ?? 0;
            @endphp

            @if ($userPoints > 0)
            <div class="featured-box mt-4">
                <h4 class="mb-3 text-warning">
                    <i class="fas fa-star me-2"></i>تمييز الإعلان
                </h4>

                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" name="is_featured" id="is_featured"
                        {{ $job->is_featured && $job->featured_until && \Carbon\Carbon::parse($job->featured_until)->gt(now()) ? 'checked' : '' }}
                        onchange="toggleDaysInput(this)">
                    <label class="form-check-label fw-bold" for="is_featured">
                        تثبيت الإعلان كـ إعلان مميز (خارجي)
                    </label>
                </div>

                <div id="days-input" class="mt-3 {{ $job->is_featured && $job->featured_until && \Carbon\Carbon::parse($job->featured_until)->gt(now()) ? '' : 'd-none' }}">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <label for="featured_days" class="form-label">مدة التثبيت (بالأيام):</label>
                            <input type="number" name="featured_days" id="featured_days" class="form-control" min="1" max="{{ $userPoints }}" value="1">
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle me-1"></i> لديك <strong>{{ $userPoints }}</strong> نقطة متاحة
                            </div>
                        </div>
                    </div>

                    @if($job->is_featured && $job->featured_until && \Carbon\Carbon::parse($job->featured_until)->gt(now()))
                        <div class="alert alert-success mt-3">
                            <i class="fas fa-check-circle me-1"></i> الإعلان مميز حتى: <strong>{{ \Carbon\Carbon::parse($job->featured_until)->format('Y-m-d') }}</strong>
                        </div>
                    @endif
                </div>
            </div>
            @endif

            <div class="d-flex justify-content-between mt-4">
                <a href="{{ route('jobs.myJobs') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i> إلغاء
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> حفظ التغييرات
                </button>
            </div>
        </form>
    </div>

    <script>
        function toggleDaysInput(checkbox) {
            const daysInput = document.getElementById('days-input');
            if (checkbox.checked) {
                daysInput.classList.remove('d-none');
            } else {
                daysInput.classList.add('d-none');
            }
        }

        // تفعيل التحقق من النموذج
        (function() {
            'use strict';
            var forms = document.querySelectorAll('.needs-validation');
            Array.prototype.slice.call(forms).forEach(function(form) {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        })();
    </script>
</body>
</html>
