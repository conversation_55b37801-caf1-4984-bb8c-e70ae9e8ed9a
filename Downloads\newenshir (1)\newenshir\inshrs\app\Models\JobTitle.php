<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JobTitle extends Model
{
    use HasFactory;

    protected $fillable = [
        'job_category_id', 'slug', 'name', 'is_active'
    ];

    /**
     * العلاقة مع فئة الوظيفة
     */
    public function jobCategory()
    {
        return $this->belongsTo(JobCategory::class);
    }

    /**
     * العلاقة مع الوظائف
     */
    public function jobs()
    {
        return $this->hasMany(JobPosting::class);
    }
}
