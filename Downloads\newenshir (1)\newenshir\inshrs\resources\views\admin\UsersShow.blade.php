<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بروفايل المستخدم</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: #f0f2f5;
            padding: 10px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 10px;
        }

        .profile-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
        }

        .profile-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background-color: #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .profile-info {
            flex-grow: 1;
            min-width: 200px;
        }

        .profile-info h2 {
            font-size: 1.5rem;
            margin-bottom: 5px;
        }

        .profile-info p {
            color: #666;
            font-size: 0.9rem;
        }

        .table-container {
            overflow-x: auto;
            margin-top: 20px;
            -webkit-overflow-scrolling: touch;
        }

        .user-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 600px;
        }

        .user-table th,
        .user-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e2e8f0;
        }

        .user-table th {
            background-color: #f8fafc;
            font-weight: bold;
            white-space: nowrap;
        }

        .user-table td {
            vertical-align: middle;
        }

        .button-group {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .button {
            padding: 8px 16px;
            border-radius: 5px;
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
            white-space: nowrap;
            transition: opacity 0.2s;
        }

        .button:hover {
            opacity: 0.9;
        }

        .button-edit {
            background-color: #3b82f6;
            color: white;
        }

        .button-delete {
            background-color: #ef4444;
            color: white;
        }

        .button-activate {
            background-color: #22c55e;
            color: white;
        }

        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.85rem;
            display: inline-block;
            white-space: nowrap;
        }

        .status-active {
            background-color: #dcfce7;
            color: #166534;
        }

        .status-inactive {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .search-bar {
            margin: 10px 0;
            padding: 12px;
            border: 1px solid #e2e8f0;
            border-radius: 5px;
            width: 100%;
            font-size: 1rem;
        }

        /* التصميم المتجاوب */
        @media screen and (max-width: 768px) {
            .container {
                padding: 5px;
            }

            .profile-card {
                padding: 10px;
            }

            .profile-header {
                gap: 10px;
            }

            .profile-avatar {
                width: 60px;
                height: 60px;
            }

            .profile-info h2 {
                font-size: 1.2rem;
            }

            .button {
                padding: 6px 12px;
                font-size: 0.8rem;
            }

            .status {
                font-size: 0.8rem;
            }
        }

        @media screen and (max-width: 480px) {
            .profile-header {
                flex-direction: column;
                text-align: center;
            }

            .profile-info {
                text-align: center;
            }

            .button-group {
                justify-content: center;
            }

            .search-bar {
                font-size: 0.9rem;
                padding: 10px;
            }
        }

        /* تحسين عرض الجدول على الشاشات الصغيرة */
        @media screen and (max-width: 640px) {
            .user-table {
                display: block;
            }

            .user-table thead {
                display: none;
            }

            .user-table tbody,
            .user-table tr,
            .user-table td {
                display: block;
                width: 100%;
            }

            .user-table tr {
                margin-bottom: 15px;
                border: 1px solid #e2e8f0;
                border-radius: 5px;
                padding: 10px;
            }

            .user-table td {
                text-align: right;
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
                position: relative;
            }

            .user-table td:before {
                content: attr(data-label);
                font-weight: bold;
                float: right;
                margin-left: 10px;
            }

            .user-table td:last-child {
                border-bottom: none;
            }

            .button-group {
                justify-content: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="profile-card">
            <div class="profile-header">
                <div class="profile-avatar">👤</div>
                <div class="profile-info">
                    <h2>لوحة إدارة المستخدمين</h2>
                    <p>إدارة حسابات المستخدمين والشركات</p>
                </div>
            </div>
            
            <input type="text" class="search-bar" id="searchInput" placeholder="بحث عن مستخدم...">
            
            <div class="table-container">
                <table class="user-table">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الدور</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="userTableBody">
                        <!-- سيتم ملء البيانات عن طريق JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // بيانات المستخدمين
        const users = [
            {
                user_id: 1,
                user_name: "أحمد محمد",
                email: "<EMAIL>",
                role: "user",
                is_active: true,
                created_at: "2024-02-13"
            },
            {
                user_id: 2,
                user_name: "سارة خالد",
                email: "<EMAIL>",
                role: "company",
                is_active: true,
                created_at: "2024-02-13"
            },
            {
                user_id: 3,
                user_name: "محمد علي",
                email: "<EMAIL>",
                role: "admin",
                is_active: false,
                created_at: "2024-02-13"
            }
        ];

        // عرض المستخدمين في الجدول
        function displayUsers(usersToDisplay) {
            const tableBody = document.getElementById('userTableBody');
            tableBody.innerHTML = '';

            usersToDisplay.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td data-label="الاسم">${user.user_name}</td>
                    <td data-label="البريد الإلكتروني">${user.email}</td>
                    <td data-label="الدور">${translateRole(user.role)}</td>
                    <td data-label="الحالة">
                        <span class="status ${user.is_active ? 'status-active' : 'status-inactive'}">
                            ${user.is_active ? 'نشط' : 'غير نشط'}
                        </span>
                    </td>
                    <td data-label="تاريخ الإنشاء">${user.created_at}</td>
                    <td data-label="الإجراءات">
                        <div class="button-group">
                            <button class="button button-edit" onclick="editUser(${user.user_id})">تعديل</button>
                            <button class="button button-delete" onclick="deleteUser(${user.user_id})">حذف</button>
                            <button class="button button-activate" onclick="toggleUserStatus(${user.user_id})">
                                ${user.is_active ? 'إيقاف' : 'تفعيل'}
                            </button>
                        </div>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        // ترجمة الأدوار
        function translateRole(role) {
            const roles = {
                'user': 'مستخدم',
                'company': 'شركة',
                'admin': 'مدير'
            };
            return roles[role] || role;
        }

        // وظائف التعديل والحذف وتغيير الحالة
        function editUser(userId) {
            alert(`تعديل المستخدم رقم ${userId}`);
        }

        function deleteUser(userId) {
            if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
                alert(`تم حذف المستخدم رقم ${userId}`);
            }
        }

        function toggleUserStatus(userId) {
            const user = users.find(u => u.user_id === userId);
            if (user) {
                user.is_active = !user.is_active;
                displayUsers(users);
            }
        }

        // وظيفة البحث
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const filteredUsers = users.filter(user => 
                user.user_name.toLowerCase().includes(searchTerm) ||
                user.email.toLowerCase().includes(searchTerm)
            );
            displayUsers(filteredUsers);
        });

        // عرض المستخدمين عند تحميل الصفحة
        displayUsers(users);
    </script>
</body>
</html>