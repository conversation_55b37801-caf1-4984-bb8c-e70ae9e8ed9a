<?php

namespace App\Services;

use App\Models\Ad;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class AdLimitService
{
    /**
     * الحصول على الحد الأقصى للإعلانات لكل مستخدم
     */
    public function getMaxAdsPerUser(): int
    {
        return config('ads.max_ads_per_user', 2);
    }

    /**
     * الحصول على عدد الإعلانات النشطة للمستخدم
     */
    public function getUserActiveAdsCount(User $user): int
    {
        return Ad::where('user_id', $user->id)->count();
    }

    /**
     * الحصول على عدد الإعلانات المتبقية للمستخدم
     */
    public function getRemainingAdsCount(User $user): int
    {
        $currentCount = $this->getUserActiveAdsCount($user);
        $maxAllowed = $this->getMaxAdsPerUser();
        
        return max(0, $maxAllowed - $currentCount);
    }

    /**
     * التحقق من إمكانية إضافة إعلان جديد
     */
    public function canUserAddAd(User $user): bool
    {
        return $this->getRemainingAdsCount($user) > 0;
    }

    /**
     * التحقق من وصول المستخدم للحد الأقصى
     */
    public function hasUserReachedLimit(User $user): bool
    {
        return $this->getUserActiveAdsCount($user) >= $this->getMaxAdsPerUser();
    }

    /**
     * الحصول على رسالة الحالة للمستخدم
     */
    public function getUserStatusMessage(User $user): array
    {
        $currentCount = $this->getUserActiveAdsCount($user);
        $maxAllowed = $this->getMaxAdsPerUser();
        $remaining = $this->getRemainingAdsCount($user);

        if ($remaining === 0) {
            return [
                'type' => 'warning',
                'message' => str_replace(':max', $maxAllowed, config('ads.messages.max_ads_reached')),
                'can_add' => false
            ];
        } elseif ($remaining === 1) {
            return [
                'type' => 'info',
                'message' => str_replace(':max', $maxAllowed, config('ads.messages.one_ad_remaining')),
                'can_add' => true
            ];
        } else {
            return [
                'type' => 'success',
                'message' => str_replace([':remaining', ':max'], [$remaining, $maxAllowed], config('ads.messages.ads_remaining')),
                'can_add' => true
            ];
        }
    }

    /**
     * الحصول على إحصائيات المستخدم
     */
    public function getUserStats(User $user): array
    {
        $currentCount = $this->getUserActiveAdsCount($user);
        $maxAllowed = $this->getMaxAdsPerUser();
        $remaining = $this->getRemainingAdsCount($user);
        $percentage = $maxAllowed > 0 ? ($currentCount / $maxAllowed) * 100 : 0;

        return [
            'current_count' => $currentCount,
            'max_allowed' => $maxAllowed,
            'remaining' => $remaining,
            'percentage_used' => round($percentage, 1),
            'can_add' => $remaining > 0,
            'has_reached_limit' => $remaining === 0
        ];
    }

    /**
     * تسجيل محاولة تجاوز الحد الأقصى
     */
    public function logLimitExceeded(User $user, string $action = 'create_ad'): void
    {
        $stats = $this->getUserStats($user);
        
        Log::warning('🚫 محاولة تجاوز الحد الأقصى للإعلانات', [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'action' => $action,
            'current_ads' => $stats['current_count'],
            'max_allowed' => $stats['max_allowed'],
            'timestamp' => now()->toDateTimeString()
        ]);
    }

    /**
     * تسجيل إنشاء إعلان جديد
     */
    public function logAdCreated(User $user, Ad $ad): void
    {
        $stats = $this->getUserStats($user);
        
        Log::info('✅ تم إنشاء إعلان جديد', [
            'user_id' => $user->id,
            'ad_id' => $ad->id,
            'ad_title' => $ad->title,
            'current_ads' => $stats['current_count'],
            'max_allowed' => $stats['max_allowed'],
            'remaining' => $stats['remaining'],
            'timestamp' => now()->toDateTimeString()
        ]);
    }

    /**
     * تسجيل حذف إعلان
     */
    public function logAdDeleted(User $user, Ad $ad): void
    {
        $stats = $this->getUserStats($user);
        
        Log::info('🗑️ تم حذف إعلان', [
            'user_id' => $user->id,
            'ad_id' => $ad->id,
            'ad_title' => $ad->title,
            'current_ads' => $stats['current_count'],
            'max_allowed' => $stats['max_allowed'],
            'remaining' => $stats['remaining'],
            'timestamp' => now()->toDateTimeString()
        ]);
    }

    /**
     * التحقق من إمكانية تعديل الحد الأقصى للمستخدم (للمديرين)
     */
    public function canModifyUserLimit(User $admin, User $targetUser): bool
    {
        return $admin->is_admin || $admin->hasRole('super_admin');
    }

    /**
     * تعديل الحد الأقصى لمستخدم محدد (للمديرين)
     */
    public function setUserCustomLimit(User $user, int $customLimit, User $admin): bool
    {
        if (!$this->canModifyUserLimit($admin, $user)) {
            Log::warning('🚫 محاولة تعديل حد الإعلانات بدون صلاحية', [
                'admin_id' => $admin->id,
                'target_user_id' => $user->id,
                'requested_limit' => $customLimit
            ]);
            return false;
        }

        // يمكن إضافة حقل custom_ads_limit في جدول المستخدمين
        // $user->custom_ads_limit = $customLimit;
        // $user->save();

        Log::info('⚙️ تم تعديل حد الإعلانات للمستخدم', [
            'admin_id' => $admin->id,
            'target_user_id' => $user->id,
            'old_limit' => $this->getMaxAdsPerUser(),
            'new_limit' => $customLimit,
            'timestamp' => now()->toDateTimeString()
        ]);

        return true;
    }

    /**
     * الحصول على تقرير إحصائي عام
     */
    public function getGeneralStats(): array
    {
        $totalUsers = User::count();
        $usersWithAds = User::whereHas('ads')->count();
        $usersAtLimit = User::whereHas('ads', function($query) {
            $query->havingRaw('COUNT(*) >= ?', [$this->getMaxAdsPerUser()]);
        })->count();
        
        $totalAds = Ad::count();
        $averageAdsPerUser = $usersWithAds > 0 ? round($totalAds / $usersWithAds, 2) : 0;

        return [
            'total_users' => $totalUsers,
            'users_with_ads' => $usersWithAds,
            'users_at_limit' => $usersAtLimit,
            'total_ads' => $totalAds,
            'average_ads_per_user' => $averageAdsPerUser,
            'max_ads_per_user' => $this->getMaxAdsPerUser(),
            'limit_utilization_percentage' => $usersWithAds > 0 ? round(($usersAtLimit / $usersWithAds) * 100, 1) : 0
        ];
    }

    /**
     * التحقق من الحاجة لتنبيه المستخدم
     */
    public function shouldShowWarning(User $user): bool
    {
        $remaining = $this->getRemainingAdsCount($user);
        return $remaining <= 1 && $remaining > 0;
    }

    /**
     * التحقق من الحاجة لإخفاء زر إضافة إعلان
     */
    public function shouldHideAddButton(User $user): bool
    {
        return $this->hasUserReachedLimit($user);
    }

    /**
     * الحصول على CSS class للحالة
     */
    public function getStatusCssClass(User $user): string
    {
        $remaining = $this->getRemainingAdsCount($user);
        
        if ($remaining === 0) {
            return 'danger';
        } elseif ($remaining === 1) {
            return 'warning';
        } else {
            return 'success';
        }
    }

    /**
     * الحصول على أيقونة للحالة
     */
    public function getStatusIcon(User $user): string
    {
        $remaining = $this->getRemainingAdsCount($user);
        
        if ($remaining === 0) {
            return 'fas fa-ban';
        } elseif ($remaining === 1) {
            return 'fas fa-exclamation-triangle';
        } else {
            return 'fas fa-check-circle';
        }
    }
}
