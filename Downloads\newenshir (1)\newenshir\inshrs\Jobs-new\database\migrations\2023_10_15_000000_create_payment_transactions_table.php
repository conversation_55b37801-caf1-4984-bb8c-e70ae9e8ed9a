<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // تحقق مما إذا كان الجدول موجودًا بالفعل
        if (!Schema::hasTable('payment_transactions')) {
            Schema::create('payment_transactions', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->string('transaction_id')->unique();
                $table->string('payment_method');
                $table->decimal('amount', 10, 2);
                $table->integer('points_amount');
                $table->string('status');
                $table->json('payment_details')->nullable();
                $table->timestamp('completed_at')->nullable();
                $table->timestamps();
            });
        } else {
            // إذا كان الجدول موجودًا بالفعل، تحقق من نوع العمود payment_details
            if (Schema::hasColumn('payment_transactions', 'payment_details')) {
                Schema::table('payment_transactions', function (Blueprint $table) {
                    // تغيير نوع العمود إلى json إذا لم يكن كذلك بالفعل
                    $table->json('payment_details')->nullable()->change();
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_transactions');
    }
};
