<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdImage extends Model
{
    use HasFactory;

    protected $fillable = [
        'ad_id',
        'image',
        'image_id',
        'order',
        'name',
        'mime_type',
        'data'
    ];

    /**
     * العلاقة مع الإعلان
     */
    public function ad()
    {
        return $this->belongsTo(Ad::class);
    }

    /**
     * العلاقة مع نموذج الصورة (للتوافق مع النظام القديم)
     */
    public function dbImage()
    {
        return $this->belongsTo(Image::class, 'image_id');
    }

    /**
     * الحصول على مسار الصورة مع صورة افتراضية إذا لم تكن موجودة
     */
    public function getImageUrl()
    {
        // إذا كانت الصورة مخزنة مباشرة في هذا النموذج
        if ($this->data) {
            // إرجاع رابط الصورة من خلال المسار المخصص
            return route('ad-images.show', $this->id) . '?v=' . time();
        }
        // التحقق من وجود صورة في نموذج Image (للتوافق مع النظام القديم)
        elseif ($this->image_id && $this->dbImage) {
            // إرجاع رابط الصورة من قاعدة البيانات
            return route('images.show', $this->image_id) . '?v=' . time();
        }
        // إذا كان هناك صورة مخزنة في نظام الملفات (للتوافق مع النظام القديم)
        elseif ($this->image) {
            // تحديد ما إذا كانت الصورة في المجلد الجديد (public/images)
            if (strpos($this->image, 'images/ads/') === 0) {
                // الصورة في المجلد الجديد - استخدام المسار المباشر
                $imagePath = public_path($this->image);

                if (file_exists($imagePath)) {
                    // إضافة معلمة عشوائية لتجنب التخزين المؤقت للمتصفح
                    return url($this->image) . '?v=' . time();
                } else {
                    // تسجيل خطأ إذا كان الملف غير موجود
                    \Illuminate\Support\Facades\Log::error('Ad image file not found in public directory: ' . $imagePath . ' for ad image ID: ' . $this->id);
                }
            }
        }

        // إرجاع الصورة الافتراضية
        return 'https://png.pngtree.com/element_our/20190528/ourlarge/pngtree-no-photography-image_1128321.jpg';
    }
}
