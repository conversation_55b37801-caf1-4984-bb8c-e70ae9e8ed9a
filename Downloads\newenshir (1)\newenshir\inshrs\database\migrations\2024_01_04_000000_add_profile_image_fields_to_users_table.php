<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // التحقق من عدم وجود الحقول مسبقاً
            if (!Schema::hasColumn('users', 'profile_image')) {
                $table->longText('profile_image')->nullable()->comment('الصورة الشخصية (Base64)');
            }
            
            if (!Schema::hasColumn('users', 'profile_image_type')) {
                $table->string('profile_image_type', 20)->nullable()->comment('نوع الصورة (jpeg, png, gif)');
            }
            
            if (!Schema::hasColumn('users', 'profile_image_size')) {
                $table->integer('profile_image_size')->nullable()->comment('حجم الصورة بالبايت');
            }
            
            if (!Schema::hasColumn('users', 'profile_image_updated_at')) {
                $table->timestamp('profile_image_updated_at')->nullable()->comment('آخر تحديث للصورة الشخصية');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'profile_image',
                'profile_image_type', 
                'profile_image_size',
                'profile_image_updated_at'
            ]);
        });
    }
};
