<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام التنبيهات والإشعارات الشامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            text-align: right;
            padding: 2rem 0;
        }
        
        .container {
            max-width: 1200px;
        }
        
        .test-card {
            background: white;
            border-radius: 1.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            text-align: center;
        }
        
        .test-body {
            padding: 2rem;
        }
        
        .feature-section {
            background: #f8f9fa;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .feature-title {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }
        
        .feature-title i {
            margin-left: 0.5rem;
        }
        
        .demo-button {
            margin: 0.5rem;
            border-radius: 0.75rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        .test-form {
            background: white;
            border-radius: 1rem;
            padding: 1.5rem;
            border: 2px solid #e9ecef;
            margin-bottom: 1rem;
        }
        
        .form-control {
            border-radius: 0.5rem;
            border: 2px solid #e9ecef;
            padding: 0.75rem;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .stat-card {
            background: white;
            border-radius: 1rem;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .stat-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .demo-area {
            min-height: 300px;
            border: 2px dashed #dee2e6;
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 1.1rem;
            margin: 1rem 0;
        }
        
        .code-example {
            background: #f8f9fa;
            border-radius: 0.5rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 1rem 0;
            border: 1px solid #dee2e6;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="test-card">
            <div class="test-header">
                <h1>
                    <i class="fas fa-bell me-2"></i>
                    نظام التنبيهات والإشعارات الشامل
                </h1>
                <p>اختبار شامل لجميع مكونات نظام التنبيهات المطور</p>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number">5</div>
                <div class="stat-label">مكونات أساسية</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon info">
                    <i class="fas fa-cogs"></i>
                </div>
                <div class="stat-number">15+</div>
                <div class="stat-label">ميزة متقدمة</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon warning">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <div class="stat-number">100%</div>
                <div class="stat-label">متجاوب</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon error">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="stat-number">آمن</div>
                <div class="stat-label">حماية البيانات</div>
            </div>
        </div>

        <!-- Notification Types Test -->
        <div class="test-card">
            <div class="test-body">
                <div class="feature-section">
                    <h3 class="feature-title">
                        <i class="fas fa-bell"></i>
                        اختبار أنواع التنبيهات
                    </h3>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <button class="btn btn-success demo-button w-100" onclick="showTestNotification('success')">
                                <i class="fas fa-check-circle me-1"></i>
                                تنبيه نجاح
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-danger demo-button w-100" onclick="showTestNotification('error')">
                                <i class="fas fa-times-circle me-1"></i>
                                تنبيه خطأ
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-warning demo-button w-100" onclick="showTestNotification('warning')">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                تنبيه تحذير
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-info demo-button w-100" onclick="showTestNotification('info')">
                                <i class="fas fa-info-circle me-1"></i>
                                تنبيه معلومات
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Position Test -->
                <div class="feature-section">
                    <h3 class="feature-title">
                        <i class="fas fa-arrows-alt"></i>
                        اختبار مواضع التنبيهات
                    </h3>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <button class="btn btn-outline-primary demo-button w-100" onclick="showPositionNotification('top-right')">
                                أعلى يسار
                            </button>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-outline-primary demo-button w-100" onclick="showPositionNotification('top-left')">
                                أعلى يمين
                            </button>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-outline-primary demo-button w-100" onclick="showPositionNotification('center')">
                                وسط الشاشة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Email Validation Test -->
                <div class="feature-section">
                    <h3 class="feature-title">
                        <i class="fas fa-envelope-open-text"></i>
                        اختبار التحقق من البريد الإلكتروني
                    </h3>
                    
                    <div class="test-form">
                        <label for="testEmail" class="form-label">
                            <i class="fas fa-envelope me-2"></i>
                            اختبر التحقق من البريد الإلكتروني
                        </label>
                        <input type="email" class="form-control" id="testEmail" placeholder="أدخل بريد إلكتروني للاختبار">
                        <div id="emailValidationResult" class="mt-2"></div>
                    </div>
                </div>

                <!-- Password Strength Test -->
                <div class="feature-section">
                    <h3 class="feature-title">
                        <i class="fas fa-lock"></i>
                        اختبار قوة كلمة المرور
                    </h3>
                    
                    <div class="test-form">
                        <label for="testPassword" class="form-label">
                            <i class="fas fa-key me-2"></i>
                            اختبر قوة كلمة المرور
                        </label>
                        <input type="password" class="form-control" id="testPassword" placeholder="أدخل كلمة مرور للاختبار">
                        <div id="passwordStrengthResult" class="mt-2"></div>
                    </div>
                </div>

                <!-- Advanced Features -->
                <div class="feature-section">
                    <h3 class="feature-title">
                        <i class="fas fa-magic"></i>
                        الميزات المتقدمة
                    </h3>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <button class="btn btn-outline-success demo-button w-100" onclick="showAutoHideNotification()">
                                <i class="fas fa-clock me-1"></i>
                                إخفاء تلقائي
                            </button>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-outline-info demo-button w-100" onclick="showActionNotification()">
                                <i class="fas fa-mouse-pointer me-1"></i>
                                تنبيه مع أزرار
                            </button>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-outline-warning demo-button w-100" onclick="showProgressNotification()">
                                <i class="fas fa-progress-bar me-1"></i>
                                شريط تقدم
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Authentication Scenarios -->
                <div class="feature-section">
                    <h3 class="feature-title">
                        <i class="fas fa-user-shield"></i>
                        سيناريوهات المصادقة
                    </h3>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <button class="btn btn-outline-danger demo-button w-100" onclick="showAuthError('login')">
                                خطأ تسجيل دخول
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-warning demo-button w-100" onclick="showAuthError('register')">
                                خطأ تسجيل
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-info demo-button w-100" onclick="showAuthError('reset')">
                                استعادة كلمة مرور
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-success demo-button w-100" onclick="showAuthSuccess()">
                                نجاح المصادقة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Code Examples -->
                <div class="feature-section">
                    <h3 class="feature-title">
                        <i class="fas fa-code"></i>
                        أمثلة الكود
                    </h3>
                    
                    <div class="code-example">
<pre>// استخدام مكون التنبيهات
@include('components.notification-system', [
    'type' => 'success',
    'title' => 'نجح!',
    'message' => 'تم حفظ البيانات بنجاح',
    'position' => 'top-right',
    'autoHide' => true,
    'duration' => 3000
])

// استخدام مكون التحقق من البريد
@include('components.email-validator', [
    'inputId' => 'email',
    'checkAvailability' => true,
    'realTimeValidation' => true
])

// استخدام مكون قوة كلمة المرور
@include('components.password-strength', [
    'inputId' => 'password',
    'showRequirements' => true,
    'showStrengthBar' => true
])</pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary -->
        <div class="test-card">
            <div class="test-body">
                <div class="alert alert-success">
                    <h4 class="alert-heading">
                        <i class="fas fa-check-circle me-2"></i>
                        تم تطوير نظام التنبيهات والإشعارات بنجاح!
                    </h4>
                    <p>تم إنشاء نظام شامل يتضمن:</p>
                    <ul class="mb-0">
                        <li>مكون تنبيهات عام قابل للإعادة الاستخدام</li>
                        <li>نظام التحقق الفوري من البريد الإلكتروني</li>
                        <li>مؤشر قوة كلمة المرور التفاعلي</li>
                        <li>صفحات مصادقة محسنة (تسجيل، دخول، استعادة)</li>
                        <li>تكامل مع نظام البريد الإلكتروني المحدث</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let notificationCounter = 0;
        
        function showTestNotification(type) {
            const messages = {
                success: 'تم تنفيذ العملية بنجاح!',
                error: 'حدث خطأ أثناء تنفيذ العملية',
                warning: 'تحذير: يرجى التحقق من البيانات',
                info: 'معلومات: هذا تنبيه إعلامي'
            };
            
            const titles = {
                success: 'نجح!',
                error: 'خطأ!',
                warning: 'تحذير!',
                info: 'معلومات'
            };
            
            createNotification(type, titles[type], messages[type], 'top-right');
        }
        
        function showPositionNotification(position) {
            createNotification('info', 'اختبار الموضع', `تنبيه في موضع: ${position}`, position);
        }
        
        function showAutoHideNotification() {
            createNotification('success', 'إخفاء تلقائي', 'سيختفي هذا التنبيه تلقائياً خلال 3 ثوان', 'top-right', true, 3000);
        }
        
        function showActionNotification() {
            const actions = [
                { text: 'موافق', type: 'primary', onclick: 'hideNotification(this.closest(".notification-alert").id)' },
                { text: 'إلغاء', type: 'secondary', onclick: 'hideNotification(this.closest(".notification-alert").id)' }
            ];
            
            createNotification('warning', 'تأكيد العملية', 'هل أنت متأكد من المتابعة؟', 'center', false, 0, actions);
        }
        
        function showProgressNotification() {
            const notificationId = createNotification('info', 'جاري التحميل...', 'يرجى الانتظار', 'top-right', false, 0, [], true);
            
            // محاكاة شريط التقدم
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                const progressBar = document.querySelector(`#${notificationId} .notification-progress`);
                if (progressBar) {
                    progressBar.style.width = `${100 - progress}%`;
                }
                
                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        hideNotification(notificationId);
                    }, 500);
                }
            }, 200);
        }
        
        function showAuthError(type) {
            const errors = {
                login: 'بيانات تسجيل الدخول غير صحيحة. يرجى التحقق من البريد الإلكتروني وكلمة المرور.',
                register: 'البريد الإلكتروني مستخدم بالفعل. يرجى استخدام بريد إلكتروني آخر.',
                reset: 'البريد الإلكتروني غير مسجل في النظام. يرجى التحقق من البريد المدخل.'
            };
            
            createNotification('error', 'خطأ في المصادقة', errors[type], 'top-right');
        }
        
        function showAuthSuccess() {
            createNotification('success', 'مرحباً بك!', 'تم تسجيل الدخول بنجاح. جاري التوجيه إلى لوحة التحكم...', 'center', true, 3000);
        }
        
        function createNotification(type, title, message, position, autoHide = false, duration = 5000, actions = [], showProgress = false) {
            notificationCounter++;
            const notificationId = `notification-${notificationCounter}`;
            
            const colors = {
                success: { bg: '#d4edda', border: '#28a745', text: '#155724' },
                error: { bg: '#f8d7da', border: '#dc3545', text: '#721c24' },
                warning: { bg: '#fff3cd', border: '#ffc107', text: '#856404' },
                info: { bg: '#d1ecf1', border: '#17a2b8', text: '#0c5460' }
            };
            
            const icons = {
                success: 'fas fa-check-circle',
                error: 'fas fa-times-circle',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle'
            };
            
            const currentColors = colors[type] || colors.info;
            const currentIcon = icons[type] || icons.info;
            
            const actionsHtml = actions.length > 0 ? `
                <div class="notification-actions">
                    ${actions.map(action => `
                        <button type="button" class="notification-action ${action.type}" onclick="${action.onclick}">
                            ${action.text}
                        </button>
                    `).join('')}
                </div>
            ` : '';
            
            const progressHtml = showProgress ? '<div class="notification-progress" style="width: 100%;"></div>' : '';
            
            const notificationHtml = `
                <div id="${notificationId}" 
                     class="notification-alert notification-${type} notification-${position}"
                     style="display: none; position: fixed; z-index: 9999; min-width: 300px; max-width: 500px; padding: 1rem 1.25rem; border-radius: 0.75rem; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15); border: 2px solid ${currentColors.border}; background: linear-gradient(135deg, ${currentColors.bg} 0%, rgba(${type === 'success' ? '40, 167, 69' : type === 'error' ? '220, 53, 69' : type === 'warning' ? '255, 193, 7' : '23, 162, 184'}, 0.1) 100%); color: ${currentColors.text}; opacity: 0; transform: translateY(-20px); transition: all 0.4s ease; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; text-align: right;">
                    
                    <div class="notification-content">
                        <div class="notification-header" style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                            <i class="notification-icon ${currentIcon}" style="font-size: 1.25rem; margin-left: 0.75rem;"></i>
                            <h6 class="notification-title" style="font-weight: 600; font-size: 1rem; margin: 0; flex: 1;">${title}</h6>
                            <button type="button" class="notification-close" onclick="hideNotification('${notificationId}')" style="background: none; border: none; font-size: 1.25rem; cursor: pointer; opacity: 0.7; color: inherit; padding: 0; margin-right: 0.5rem;">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <p class="notification-message" style="font-size: 0.9rem; line-height: 1.5; margin: 0;">${message}</p>
                        ${actionsHtml}
                    </div>
                    ${progressHtml}
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', notificationHtml);
            
            // تحديد الموضع
            const notification = document.getElementById(notificationId);
            const positions = {
                'top-right': { top: '20px', left: '20px' },
                'top-left': { top: '20px', right: '20px' },
                'bottom-right': { bottom: '20px', left: '20px' },
                'bottom-left': { bottom: '20px', right: '20px' },
                'center': { top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }
            };
            
            Object.assign(notification.style, positions[position] || positions['top-right']);
            
            // إظهار التنبيه
            setTimeout(() => {
                notification.style.display = 'block';
                setTimeout(() => {
                    notification.style.opacity = '1';
                    notification.style.transform = position === 'center' ? 'translate(-50%, -50%)' : 'translateY(0)';
                }, 10);
            }, 100);
            
            // الإخفاء التلقائي
            if (autoHide) {
                setTimeout(() => {
                    hideNotification(notificationId);
                }, duration);
            }
            
            return notificationId;
        }
        
        function hideNotification(id) {
            const notification = document.getElementById(id);
            if (notification) {
                notification.style.opacity = '0';
                notification.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    notification.remove();
                }, 400);
            }
        }
        
        // اختبار التحقق من البريد الإلكتروني
        document.getElementById('testEmail').addEventListener('input', function() {
            const email = this.value.trim();
            const resultDiv = document.getElementById('emailValidationResult');
            
            if (!email) {
                resultDiv.innerHTML = '';
                return;
            }
            
            const isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
            
            if (isValid) {
                resultDiv.innerHTML = '<div class="text-success"><i class="fas fa-check-circle me-1"></i> تنسيق البريد الإلكتروني صحيح</div>';
            } else {
                resultDiv.innerHTML = '<div class="text-danger"><i class="fas fa-times-circle me-1"></i> تنسيق البريد الإلكتروني غير صحيح</div>';
            }
        });
        
        // اختبار قوة كلمة المرور
        document.getElementById('testPassword').addEventListener('input', function() {
            const password = this.value;
            const resultDiv = document.getElementById('passwordStrengthResult');
            
            if (!password) {
                resultDiv.innerHTML = '';
                return;
            }
            
            let score = 0;
            let feedback = [];
            
            if (password.length >= 8) score += 25;
            else feedback.push('8 أحرف على الأقل');
            
            if (/[A-Z]/.test(password)) score += 25;
            else feedback.push('حرف كبير');
            
            if (/[a-z]/.test(password)) score += 25;
            else feedback.push('حرف صغير');
            
            if (/[0-9]/.test(password)) score += 25;
            else feedback.push('رقم');
            
            let strength = 'ضعيفة جداً';
            let color = 'danger';
            
            if (score >= 100) {
                strength = 'قوية جداً';
                color = 'success';
            } else if (score >= 75) {
                strength = 'قوية';
                color = 'success';
            } else if (score >= 50) {
                strength = 'متوسطة';
                color = 'warning';
            } else if (score >= 25) {
                strength = 'ضعيفة';
                color = 'warning';
            }
            
            resultDiv.innerHTML = `
                <div class="text-${color}">
                    <i class="fas fa-shield-alt me-1"></i> 
                    قوة كلمة المرور: ${strength} (${score}%)
                    ${feedback.length > 0 ? '<br><small>مطلوب: ' + feedback.join(', ') + '</small>' : ''}
                </div>
            `;
        });
    </script>
</body>
</html>
