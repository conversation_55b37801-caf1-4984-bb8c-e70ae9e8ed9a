<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Ad;
use App\Models\AdImage;
use App\Models\AdView;
use App\Models\Image;
use App\Services\ImageService;
use App\Services\ViewTrackingService;
use App\Services\AdLimitService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class AdController extends Controller
{

    protected $imageService;
    protected $viewTrackingService;
    protected $adLimitService;

    public function __construct(ImageService $imageService, ViewTrackingService $viewTrackingService, AdLimitService $adLimitService)
    {
        $this->middleware('auth')->except(['index', 'show']);
        $this->imageService = $imageService;
        $this->viewTrackingService = $viewTrackingService;
        $this->adLimitService = $adLimitService;
    }


    // عرض كل الإعلانات
    public function index(Request $request)
    {
        $query = Ad::query();

        // فلترة بالكلمة المفتاحية
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        // فلترة بالفئة والفئة الفرعية
        if ($request->filled('category')) {
            $query->where('category', $request->category);

            // إذا تم تحديد فئة فرعية
            if ($request->filled('subcategory')) {
                $query->where('subcategory', $request->subcategory);
            }
        }

        // فلترة بالموقع
        if ($request->filled('location')) {
            $query->where('location', $request->location);
        }

        // فلترة بالسعر
        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }

        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // ترتيب الإعلانات حسب الاختيار
        switch ($request->input('sort')) {
            case 'newest':
                $query->orderByRaw('
                    CASE
                        WHEN is_featured = 1 AND (featured_until IS NULL OR featured_until > NOW()) THEN 0
                        ELSE 1
                    END
                ')->latest();
                break;
            case 'oldest':
                $query->orderByRaw('
                    CASE
                        WHEN is_featured = 1 AND (featured_until IS NULL OR featured_until > NOW()) THEN 0
                        ELSE 1
                    END
                ')->oldest();
                break;
            case 'price_high':
                $query->orderByRaw('
                    CASE
                        WHEN is_featured = 1 AND (featured_until IS NULL OR featured_until > NOW()) THEN 0
                        ELSE 1
                    END
                ')->orderByDesc('price');
                break;
            case 'price_low':
                $query->orderByRaw('
                    CASE
                        WHEN is_featured = 1 AND (featured_until IS NULL OR featured_until > NOW()) THEN 0
                        ELSE 1
                    END
                ')->orderBy('price');
                break;
            case 'title_asc':
                $query->orderByRaw('
                    CASE
                        WHEN is_featured = 1 AND (featured_until IS NULL OR featured_until > NOW()) THEN 0
                        ELSE 1
                    END
                ')->orderBy('title');
                break;
            default:
                // الترتيب الافتراضي: المميزة النشطة أولاً ثم الأحدث
                $query->orderByRaw('
                    CASE
                        WHEN is_featured = 1 AND (featured_until IS NULL OR featured_until > NOW()) THEN 0
                        ELSE 1
                    END
                ')->latest();
                break;
        }

        $ads = $query->paginate(5)->appends($request->query());

        return view('ads.index', compact('ads'));
    }

    public function create()
    {
        return view('ads.create');
    }

    public function store(Request $request)
    {
        // التحقق من عدد الإعلانات النشطة للمستخدم
        $user = auth()->user();

        if (!$this->adLimitService->canUserAddAd($user)) {
            $this->adLimitService->logLimitExceeded($user, 'create_ad');

            $statusMessage = $this->adLimitService->getUserStatusMessage($user);
            return back()->withErrors([
                'limit_exceeded' => $statusMessage['message']
            ])->withInput();
        }

        // تسجيل بيانات الطلب للتشخيص
        $userStats = $this->adLimitService->getUserStats($user);
        Log::info('🚀 بدء إنشاء إعلان جديد', [
            'user_id' => auth()->id(),
            'title' => $request->title,
            'current_ads_count' => $userStats['current_count'],
            'max_allowed' => $userStats['max_allowed'],
            'remaining' => $userStats['remaining'],
            'has_images' => $request->hasFile('images'),
            'images_count' => $request->hasFile('images') ? count($request->file('images')) : 0,
            'has_single_image' => $request->hasFile('image'),
            'all_files' => array_keys($request->allFiles())
        ]);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|string|max:100',
            'subcategory' => 'nullable|string|max:100',
            'location' => 'nullable|string|max:255',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'price' => 'nullable|numeric',
            'phone' => 'nullable|string|max:50|regex:/^[0-9]+$/',
            'whatsapp' => 'nullable|string|max:50|regex:/^[0-9]+$/',
            'email' => 'nullable|email|max:255',
            'featured_days' => 'nullable|integer|min:1',
            'images' => 'nullable|array|max:5',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
        ]);

        // التحقق من تنسيقات الصور المدعومة
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $index => $imageFile) {
                if (!$this->imageService->isSupportedImageFormat($imageFile->getMimeType())) {
                    return back()->withErrors([
                        'images.' . $index => 'تنسيق الصورة غير مدعوم. الرجاء استخدام تنسيقات: JPEG, PNG, GIF, WebP'
                    ])->withInput();
                }
            }
        }

        if ($request->hasFile('image')) {
            if (!$this->imageService->isSupportedImageFormat($request->file('image')->getMimeType())) {
                return back()->withErrors([
                    'image' => 'تنسيق الصورة غير مدعوم. الرجاء استخدام تنسيقات: JPEG, PNG, GIF, WebP'
                ])->withInput();
            }
        }

        $user = auth()->user();
        $isFeatured = $request->has('is_featured');
        $days = $request->input('featured_days', 0);

        // التحقق من النقاط عند طلب إعلان مميز
        if ($isFeatured && $days > 0) {
            if ($user->points < $days) {
                return back()->withErrors(['featured_days' => 'ليس لديك نقاط كافية لتثبيت الإعلان لهذه المدة.'])->withInput();
            }

            // خصم النقاط
            $user->points -= $days;
            $user->save();

            $validated['is_featured'] = true;
            $validated['featured_until'] = now()->addDays($days);
        } else {
            $validated['is_featured'] = false;
            $validated['featured_until'] = null;
        }

        // إنشاء الإعلان أولاً بدون صور
        $validated['user_id'] = $user->id;
        $ad = Ad::create($validated);

        // معالجة الصور المتعددة إذا تم تحميلها
        if ($request->hasFile('images')) {
            Log::info('📸 بدء معالجة الصور المتعددة', [
                'ad_id' => $ad->id,
                'images_count' => count($request->file('images')),
                'files_info' => array_map(function($file) {
                    return [
                        'name' => $file->getClientOriginalName(),
                        'size' => $file->getSize(),
                        'type' => $file->getMimeType(),
                        'valid' => $file->isValid()
                    ];
                }, $request->file('images'))
            ]);

            try {
                $order = 0;
                $uploadedImages = 0;
                $maxImages = 5;

                foreach ($request->file('images') as $imageFile) {
                    // التحقق من الحد الأقصى للصور
                    if ($uploadedImages >= $maxImages) {
                        Log::warning('Maximum number of images exceeded for ad ID: ' . $ad->id);
                        break;
                    }

                    // التحقق من أن الملف صالح
                    if (!$imageFile->isValid()) {
                        Log::error('Invalid image file uploaded');
                        continue;
                    }

                    // معالجة وضغط الصورة وتخزينها في قاعدة البيانات
                    $imageInfo = $this->imageService->processImageForDatabase($imageFile);

                    if ($imageInfo) {
                        // إنشاء سجل في جدول صور الإعلانات
                        AdImage::create([
                            'ad_id' => $ad->id,
                            'name' => $imageInfo['name'],
                            'mime_type' => $imageInfo['mime_type'],
                            'data' => $imageInfo['data'],
                            'order' => $order++
                        ]);

                        $uploadedImages++;
                        Log::info('Image processed and saved to database for ad ID: ' . $ad->id . ', Size: ' . $imageInfo['size'] . 'KB');
                    } else {
                        Log::error('Failed to process image for ad ID: ' . $ad->id);
                    }
                }

                if ($uploadedImages > 0) {
                    Log::info("Successfully uploaded {$uploadedImages} images for ad ID: " . $ad->id);
                }
            } catch (\Exception $e) {
                Log::error('Error uploading images: ' . $e->getMessage());
                return back()->withErrors(['images' => 'حدث خطأ أثناء رفع الصور: ' . $e->getMessage()])->withInput();
            }
        }

        // للتوافق مع النظام القديم - معالجة الصورة الفردية إذا تم تحميلها
        if ($request->hasFile('image')) {
            try {
                $imageFile = $request->file('image');

                // التحقق من أن الملف صالح
                if (!$imageFile->isValid()) {
                    Log::error('Invalid image file uploaded');
                    return back()->withErrors(['image' => 'الملف غير صالح'])->withInput();
                }

                // معالجة وضغط الصورة وتخزينها في قاعدة البيانات
                $imageInfo = $this->imageService->processImageForDatabase($imageFile);

                if ($imageInfo) {
                    // إنشاء سجل في جدول صور الإعلانات
                    AdImage::create([
                        'ad_id' => $ad->id,
                        'name' => $imageInfo['name'],
                        'mime_type' => $imageInfo['mime_type'],
                        'data' => $imageInfo['data'],
                        'order' => 0
                    ]);

                    Log::info('Single image processed and saved to database for ad ID: ' . $ad->id . ', Size: ' . $imageInfo['size'] . 'KB');
                } else {
                    Log::error('Failed to process single image for ad ID: ' . $ad->id);
                    return back()->withErrors(['image' => 'فشل في معالجة الصورة'])->withInput();
                }
            } catch (\Exception $e) {
                Log::error('Error uploading single image: ' . $e->getMessage());
                return back()->withErrors(['image' => 'حدث خطأ أثناء رفع الصورة: ' . $e->getMessage()])->withInput();
            }
        }

        // تسجيل إنشاء الإعلان بنجاح
        $this->adLimitService->logAdCreated($user, $ad);

        return redirect()->route('ads.index')->with('success', 'تم نشر الإعلان بنجاح');
    }

    public function show(Request $request, $id)
    {
        $ad = Ad::findOrFail($id);

        // تسجيل المشاهدة مع التحقق من عدم التكرار (إلا إذا تم تخطيها بسبب spam)
        if (!$request->attributes->get('skip_view_tracking', false)) {
            $userId = Auth::id();
            $newView = $this->viewTrackingService->trackAdView($ad, $request, $userId);
        }

        // تطبيق إعدادات الخصوصية على معلومات التواصل
        $canViewContact = true;
        $canSendMessage = true;
        $canComment = true;
        $showPhone = true;
        $showEmail = true;
        $showWhatsapp = true;

        if (Auth::check()) {
            $viewerId = Auth::id();
            $adOwnerId = $ad->user_id;

            // إذا لم يكن المستخدم صاحب الإعلان
            if ($viewerId != $adOwnerId) {
                $adOwner = $ad->user;

                // التحقق من الحظر المتبادل
                if ($adOwner->hasBlocked($viewerId) || Auth::user()->hasBlocked($adOwnerId)) {
                    $canViewContact = false;
                    $canSendMessage = false;
                    $canComment = false;
                    $showPhone = false;
                    $showEmail = false;
                    $showWhatsapp = false;
                } else {
                    // تطبيق إعدادات الخصوصية
                    $canSendMessage = $adOwner->canReceiveMessages();
                    $canComment = $adOwner->canReceiveComments();
                    $showPhone = $adOwner->shouldShowPhone();
                    $showEmail = $adOwner->shouldShowEmail();
                    $showWhatsapp = $showPhone; // WhatsApp يعتمد على إظهار الهاتف
                }
            }
        } else {
            // للزوار غير المسجلين، تطبيق إعدادات الخصوصية الأساسية
            $adOwner = $ad->user;
            $showPhone = $adOwner->shouldShowPhone();
            $showEmail = $adOwner->shouldShowEmail();
            $showWhatsapp = $showPhone;
            $canSendMessage = false; // يجب تسجيل الدخول للمراسلة
            $canComment = false; // يجب تسجيل الدخول للتعليق
        }

        // الحصول على إعلانات مشابهة من نفس الفئة والفئة الفرعية
        $query = Ad::where('category', $ad->category)
                  ->where('id', '!=', $ad->id);

        // إذا كان الإعلان له فئة فرعية، نبحث عن إعلانات من نفس الفئة الفرعية أولاً
        if ($ad->subcategory) {
            $query->where(function($q) use ($ad) {
                $q->where('subcategory', $ad->subcategory)
                  ->orWhereNull('subcategory');
            });
        }

        $similarAds = $query->latest()
                           ->take(3)
                           ->get();

        return view('ads.show', compact(
            'ad',
            'similarAds',
            'canViewContact',
            'canSendMessage',
            'canComment',
            'showPhone',
            'showEmail',
            'showWhatsapp'
        ));
    }

    public function edit($id)
    {
        $ad = Ad::findOrFail($id);

        if ($ad->user_id !== auth()->id()) {
            abort(403);
        }

        return view('ads.edit', compact('ad'));
    }

    public function update(Request $request, $id)
    {
        $ad = Ad::findOrFail($id);

        if ($ad->user_id !== auth()->id()) {
            abort(403);
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|string|max:100',
            'subcategory' => 'nullable|string|max:100',
            'location' => 'nullable|string|max:255',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'price' => 'nullable|numeric',
            'phone' => 'nullable|string|max:50|regex:/^[0-9]+$/',
            'whatsapp' => 'nullable|string|max:50|regex:/^[0-9]+$/',
            'email' => 'nullable|email|max:255',
            'featured_days' => 'nullable|integer|min:1',
            'images' => 'nullable|array|max:5',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
        ]);

        // التحقق من تنسيقات الصور المدعومة
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $index => $imageFile) {
                if (!$this->imageService->isSupportedImageFormat($imageFile->getMimeType())) {
                    return back()->withErrors([
                        'images.' . $index => 'تنسيق الصورة غير مدعوم. الرجاء استخدام تنسيقات: JPEG, PNG, GIF, WebP'
                    ])->withInput();
                }
            }
        }

        if ($request->hasFile('image')) {
            if (!$this->imageService->isSupportedImageFormat($request->file('image')->getMimeType())) {
                return back()->withErrors([
                    'image' => 'تنسيق الصورة غير مدعوم. الرجاء استخدام تنسيقات: JPEG, PNG, GIF, WebP'
                ])->withInput();
            }
        }

        $user = auth()->user();
        $isFeatured = $request->has('is_featured');
        $days = $request->input('featured_days', 0);

        // إذا كان الإعلان سيصبح مميزًا ولم يكن مميزًا من قبل أو تم تمديد فترة التثبيت
        if ($isFeatured && $days > 0 && (!$ad->is_featured || $ad->featured_until < now())) {
            // التحقق من وجود نقاط كافية للمستخدم
            if ($user->points < $days) {
                return back()->withErrors(['featured_days' => 'ليس لديك نقاط كافية لتثبيت الإعلان لهذه المدة.'])->withInput();
            }

            // خصم النقاط وتحديث المستخدم
            $user->points -= $days;
            $user->save();

            $validated['is_featured'] = true;
            $validated['featured_until'] = now()->addDays($days);
        } elseif (!$isFeatured) {
            $validated['is_featured'] = false;
            $validated['featured_until'] = null;
        } else {
            // الإعلان مميز بالفعل ولم يتم تغيير حالته
            unset($validated['is_featured']);
            unset($validated['featured_until']);
        }

        // معالجة الصور المتعددة إذا تم تحميلها
        if ($request->hasFile('images')) {
            try {
                $order = $ad->images()->max('order') + 1;
                foreach ($request->file('images') as $imageFile) {
                    // التحقق من أن الملف صالح
                    if (!$imageFile->isValid()) {
                        Log::error('Invalid image file uploaded');
                        continue;
                    }

                    // معالجة وضغط الصورة وتخزينها في قاعدة البيانات
                    $imageInfo = $this->imageService->processImageForDatabase($imageFile);

                    if ($imageInfo) {
                        // إنشاء سجل في جدول صور الإعلانات
                        AdImage::create([
                            'ad_id' => $ad->id,
                            'name' => $imageInfo['name'],
                            'mime_type' => $imageInfo['mime_type'],
                            'data' => $imageInfo['data'],
                            'order' => $order++
                        ]);

                        Log::info('Image processed and saved to database for ad ID: ' . $ad->id . ', Size: ' . $imageInfo['size'] . 'KB');
                    }
                }
            } catch (\Exception $e) {
                Log::error('Error uploading images: ' . $e->getMessage());
                return back()->withErrors(['images' => 'حدث خطأ أثناء رفع الصور'])->withInput();
            }
        }

        // للتوافق مع النظام القديم - معالجة الصورة الفردية إذا تم تحميلها
        if ($request->hasFile('image')) {
            try {
                $imageFile = $request->file('image');

                // التحقق من أن الملف صالح
                if (!$imageFile->isValid()) {
                    Log::error('Invalid image file uploaded during update');
                    return back()->withErrors(['image' => 'الملف غير صالح'])->withInput();
                }

                // معالجة وضغط الصورة وتخزينها في قاعدة البيانات
                $imageInfo = $this->imageService->processImageForDatabase($imageFile);

                if ($imageInfo) {
                    // إنشاء سجل في جدول صور الإعلانات
                    AdImage::create([
                        'ad_id' => $ad->id,
                        'name' => $imageInfo['name'],
                        'mime_type' => $imageInfo['mime_type'],
                        'data' => $imageInfo['data'],
                        'order' => 0
                    ]);

                    Log::info('Single image processed and saved to database for ad ID: ' . $ad->id . ', Size: ' . $imageInfo['size'] . 'KB');
                }
            } catch (\Exception $e) {
                Log::error('Error updating image: ' . $e->getMessage());
                return back()->withErrors(['image' => 'حدث خطأ أثناء تحديث الصورة'])->withInput();
            }
        }

        $ad->update($validated);

        return redirect()->route('ads.index')->with('success', 'تم تعديل الإعلان بنجاح');
    }

    public function destroy($id)
    {
        $ad = Ad::findOrFail($id);

        if ($ad->user_id !== auth()->id()) {
            abort(403);
        }

        // حذف الصور المتعددة
        foreach ($ad->images as $image) {
            // حذف الملف من المجلد إذا كان موجودًا (للتوافق مع النظام القديم)
            if ($image->image) {
                $imagePath = public_path($image->image);
                if (file_exists($imagePath)) {
                    unlink($imagePath);
                    Log::info('Image file deleted: ' . $imagePath);
                }
            }

            // حذف السجل من قاعدة البيانات
            $image->delete();
            Log::info('Image record deleted for ad ID: ' . $ad->id);
        }

        // للتوافق مع النظام القديم - حذف الصورة من قاعدة البيانات إذا كانت موجودة
        if ($ad->image_id) {
            $image = Image::find($ad->image_id);
            if ($image) {
                $image->delete();
                Log::info('Image deleted from database during ad deletion: ' . $ad->image_id);
            } else {
                Log::warning('Image not found in database for deletion during ad deletion: ' . $ad->image_id);
            }
        }

        // تسجيل حذف الإعلان
        $this->adLimitService->logAdDeleted(auth()->user(), $ad);

        $ad->delete();

        return redirect()->route('ads.index')->with('success', 'تم حذف الإعلان بنجاح');
    }

    /**
     * حذف صورة محددة من الإعلان
     */
    public function deleteImage($id)
    {
        try {
            // البحث عن الصورة
            $image = AdImage::findOrFail($id);

            // التحقق من أن المستخدم هو صاحب الإعلان
            $ad = Ad::findOrFail($image->ad_id);
            if ($ad->user_id !== auth()->id()) {
                abort(403, 'غير مصرح لك بحذف هذه الصورة');
            }

            // حذف الملف من المجلد إذا كان موجودًا (للتوافق مع النظام القديم)
            if ($image->image) {
                $imagePath = public_path($image->image);
                if (file_exists($imagePath)) {
                    unlink($imagePath);
                    Log::info('Image file deleted: ' . $imagePath);
                }
            }

            // حذف السجل من قاعدة البيانات
            $image->delete();
            Log::info('Image record deleted for ad ID: ' . $ad->id);

            return response()->json(['success' => true, 'message' => 'تم حذف الصورة بنجاح']);
        } catch (\Exception $e) {
            Log::error('Error deleting image: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'حدث خطأ أثناء حذف الصورة'], 500);
        }
    }

    /**
     * جلب الإعلانات القريبة من موقع المستخدم
     */
    public function nearby(Request $request)
    {
        // التحقق من وجود بيانات الموقع
        if (!$request->filled(['latitude', 'longitude'])) {
            return response()->json([
                'success' => false,
                'message' => 'يجب توفير إحداثيات الموقع'
            ]);
        }

        // الحصول على الإحداثيات
        $userLat = $request->latitude;
        $userLng = $request->longitude;
        $locationName = $request->location_name ?? 'موقعك الحالي';

        try {
            // استعلام لجلب الإعلانات وحساب المسافة
            // ملاحظة: هذا يفترض أن لديك أعمدة latitude و longitude في جدول الإعلانات
            // إذا لم تكن موجودة، ستحتاج إلى إضافتها أولاً

            // نستخدم صيغة Haversine لحساب المسافة بين نقطتين على سطح الكرة الأرضية
            $query = Ad::selectRaw("
                ads.*,
                (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance
            ", [$userLat, $userLng, $userLat])
            ->whereNotNull('latitude')
            ->whereNotNull('longitude')
            ->orderByRaw('
                CASE
                    WHEN is_featured = 1 AND (featured_until IS NULL OR featured_until > NOW()) THEN 0
                    ELSE 1
                END
            ')
            ->orderBy('distance');

            // تحديد نطاق المسافة (افتراضيًا 50 كم)
            $maxDistance = $request->input('max_distance', 50);
            $query->having('distance', '<=', $maxDistance);

            // تحديد عدد النتائج
            $limit = $request->input('limit', 20);
            $ads = $query->take($limit)->get();

            // تحويل البيانات إلى الشكل المطلوب للعرض
            $formattedAds = $ads->map(function($ad) {
                return [
                    'id' => $ad->id,
                    'title' => $ad->title,
                    'description' => $ad->description,
                    'description_short' => Str::limit($ad->description, 100),
                    'price' => $ad->price,
                    'location' => $ad->location,
                    'category' => $ad->category,
                    'subcategory' => $ad->subcategory,
                    'is_featured' => $ad->is_featured,
                    'image_url' => $ad->getImageUrl(),
                    'created_at_human' => $ad->created_at->diffForHumans(),
                    'user_name' => $ad->user->name ?? 'مستخدم',
                    'distance' => round($ad->distance, 1) // تقريب المسافة إلى رقم عشري واحد
                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'تم العثور على ' . count($formattedAds) . ' إعلان قريب من ' . $locationName,
                'ads' => $formattedAds
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching nearby ads: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الإعلانات القريبة: ' . $e->getMessage()
            ]);
        }
    }
}
