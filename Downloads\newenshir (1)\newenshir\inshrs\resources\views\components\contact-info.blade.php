@props([
    'user',
    'showPhone' => true,
    'showEmail' => true,
    'showWhatsapp' => true,
    'canSendMessage' => true,
    'adId' => null,
    'jobId' => null,
    'size' => 'normal' // normal, small, large
])

@php
    $sizeClasses = [
        'small' => 'text-sm',
        'normal' => '',
        'large' => 'text-lg'
    ];
    $textClass = $sizeClasses[$size] ?? '';
@endphp

<div class="contact-info-component">
    <!-- معلومات المستخدم -->
    <div class="user-info mb-3">
        <div class="d-flex align-items-center">
            <div class="user-avatar me-3">
                @if($user->hasProfileImage())
                    <img src="{{ $user->getProfileImageUrl() }}" alt="صورة {{ $user->name }}" class="avatar-img">
                @else
                    <img src="{{ $user->getDefaultAvatar() }}" alt="صورة افتراضية" class="avatar-img">
                @endif
            </div>
            <div>
                <h5 class="mb-1 {{ $textClass }}">{{ $user->name }}</h5>
                @if($user->shouldShowOnlineStatus() && Auth::check())
                    <small class="text-muted">
                        @if($user->isOnline())
                            <i class="fas fa-circle text-success" style="font-size: 8px;"></i>
                            متصل الآن
                        @else
                            <i class="fas fa-circle text-secondary" style="font-size: 8px;"></i>
                            {{ $user->getLastSeenForHumans() }}
                        @endif
                    </small>
                @endif
                @if($user->show_join_date)
                    <div class="text-muted small">
                        <i class="fas fa-calendar-alt"></i>
                        عضو منذ {{ $user->created_at->format('Y') }}
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- معلومات التواصل -->
    <div class="contact-methods">
        @if($showPhone && $user->phone)
            <div class="contact-item mb-2">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="contact-info">
                        <i class="fas fa-phone text-success me-2"></i>
                        <span class="{{ $textClass }}">{{ $user->phone }}</span>
                    </div>
                    <div class="contact-actions">
                        <a href="tel:{{ $user->phone }}" class="btn btn-sm btn-outline-success me-1" title="اتصال">
                            <i class="fas fa-phone"></i>
                        </a>
                        @if($showWhatsapp)
                            <a href="https://wa.me/{{ preg_replace('/[^0-9]/', '', $user->phone) }}"
                               target="_blank"
                               class="btn btn-sm btn-outline-success"
                               title="واتساب">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        @elseif(!$showPhone && $user->phone)
            <div class="contact-item mb-2">
                <div class="text-muted {{ $textClass }}">
                    <i class="fas fa-phone-slash me-2"></i>
                    رقم الهاتف مخفي
                </div>
            </div>
        @endif

        @if($showEmail && $user->email)
            <div class="contact-item mb-2">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="contact-info">
                        <i class="fas fa-envelope text-primary me-2"></i>
                        <span class="{{ $textClass }}">{{ $user->email }}</span>
                    </div>
                    <div class="contact-actions">
                        <a href="mailto:{{ $user->email }}" class="btn btn-sm btn-outline-primary" title="إرسال بريد">
                            <i class="fas fa-envelope"></i>
                        </a>
                    </div>
                </div>
            </div>
        @elseif(!$showEmail && $user->email)
            <div class="contact-item mb-2">
                <div class="text-muted {{ $textClass }}">
                    <i class="fas fa-envelope-open-text me-2"></i>
                    البريد الإلكتروني مخفي
                </div>
            </div>
        @endif

        <!-- زر المراسلة الخاصة -->
        @if(Auth::check())
            @if($canSendMessage && Auth::id() != $user->id)
                <div class="contact-item mb-2">
                    <form action="{{ route('chat.create') }}" method="POST" class="d-inline">
                        @csrf
                        <input type="hidden" name="receiver_id" value="{{ $user->id }}">
                        @if($adId)
                            <input type="hidden" name="ad_id" value="{{ $adId }}">
                        @endif
                        @if($jobId)
                            <input type="hidden" name="job_id" value="{{ $jobId }}">
                        @endif
                        <button type="submit" class="btn btn-primary btn-sm w-100">
                            <i class="fas fa-comments me-2"></i>
                            إرسال رسالة خاصة
                        </button>
                    </form>
                </div>
            @elseif(!$canSendMessage && Auth::id() != $user->id)
                <div class="contact-item mb-2">
                    <div class="alert alert-warning small mb-0">
                        <i class="fas fa-ban me-2"></i>
                        المراسلة غير متاحة
                    </div>
                </div>
            @endif
        @else
            <div class="contact-item mb-2">
                <div class="alert alert-info small mb-0">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    <a href="{{ route('login') }}" class="text-decoration-none">سجل دخولك</a> للمراسلة
                </div>
            </div>
        @endif
    </div>

    <!-- إحصائيات المستخدم -->
    @if($user->show_ads_count || Auth::id() == $user->id)
        <div class="user-stats mt-3 pt-3 border-top">
            <div class="row text-center">
                @if($user->show_ads_count || Auth::id() == $user->id)
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number text-primary fw-bold">
                                {{ $user->ads()->count() ?? 0 }}
                            </div>
                            <div class="stat-label small text-muted">إعلان</div>
                        </div>
                    </div>
                @endif
                @if(method_exists($user, 'jobs'))
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number text-success fw-bold">
                                {{ $user->jobs()->count() ?? 0 }}
                            </div>
                            <div class="stat-label small text-muted">وظيفة</div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    @endif

    <!-- تحذيرات الخصوصية -->
    @if(!$showPhone && !$showEmail)
        <div class="privacy-notice mt-3">
            <div class="alert alert-secondary small mb-0">
                <i class="fas fa-shield-alt me-2"></i>
                هذا المستخدم يحافظ على خصوصية معلومات التواصل
            </div>
        </div>
    @endif
</div>

<style>
.contact-info-component {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.user-avatar {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.contact-item {
    padding: 8px 0;
}

.contact-info {
    flex: 1;
    word-break: break-all;
}

.contact-actions {
    flex-shrink: 0;
}

.stat-item {
    padding: 5px;
}

.stat-number {
    font-size: 1.2rem;
}

.privacy-notice {
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
}

@media (max-width: 768px) {
    .contact-info-component {
        padding: 15px;
    }

    .contact-item {
        padding: 6px 0;
    }

    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 8px;
    }

    .contact-actions {
        align-self: flex-start;
    }
}
</style>
