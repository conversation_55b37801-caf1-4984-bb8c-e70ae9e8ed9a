<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\JobPosting;
use App\Models\Skill;
use App\Models\Course;
use App\Models\Ad;
use Illuminate\Support\Facades\DB;

class DummyDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // إضافة مستخدمين
        $users = [
            [
                'name' => 'محمد أحمد',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'سارة علي',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'أحمد محمود',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'فاطمة حسن',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'خالد عبدالله',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'نورة سعيد',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'عمر فاروق',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'ليلى محمد',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'يوسف إبراهيم',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'هدى عمر',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($users as $user) {
            User::create($user);
        }

        // إضافة مهارات
        $skills = [
            'JavaScript', 'PHP', 'Laravel', 'React', 'Vue.js', 'Node.js', 'Python', 'Java', 'C#', 'SQL',
            'HTML', 'CSS', 'Bootstrap', 'Tailwind CSS', 'Git', 'Docker', 'AWS', 'Azure', 'UI/UX Design', 'Photoshop'
        ];

        // الحصول على معرف المستخدم الأول
        $firstUser = User::first();
        if ($firstUser) {
            foreach ($skills as $skill) {
                Skill::create([
                    'user_id' => $firstUser->id,
                    'name' => $skill,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }

        // إضافة دورات
        $courses = [
            [
                'name' => 'تطوير الويب الشامل',
            ],
            [
                'name' => 'برمجة تطبيقات الموبايل',
            ],
            [
                'name' => 'تعلم Laravel',
            ],
            [
                'name' => 'تصميم واجهات المستخدم',
            ],
            [
                'name' => 'تحليل البيانات',
            ],
        ];

        // الحصول على معرف المستخدم الأول
        $firstUser = User::first();
        if ($firstUser) {
            foreach ($courses as $course) {
                Course::create(array_merge($course, [
                    'user_id' => $firstUser->id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]));
            }
        }

        // إضافة وظائف
        try {
            $jobs = [
                [
                    'job_title' => 'مطور واجهة أمامية',
                    'job_description' => 'نبحث عن مطور واجهة أمامية ذو خبرة في React و Vue.js للعمل على مشاريع مثيرة',
                    'company_name' => 'شركة التقنية المتقدمة',
                    'location' => 'الرياض',
                    'salary' => 15000,
                    'experience_required' => '3-5 سنوات',
                    'whatsapp' => '966500000001',
                    'email' => '<EMAIL>',
                    'phone' => '0500000001',
                ],
                [
                    'job_title' => 'مصمم جرافيك',
                    'job_description' => 'مطلوب مصمم جرافيك مبدع للعمل على تصميم هويات بصرية وواجهات مستخدم',
                    'company_name' => 'إبداع للتصميم',
                    'location' => 'جدة',
                    'salary' => 12000,
                    'experience_required' => '2-4 سنوات',
                    'whatsapp' => '966500000002',
                    'email' => '<EMAIL>',
                    'phone' => '0500000002',
                ],
                [
                    'job_title' => 'مدير مشروع',
                    'job_description' => 'نبحث عن مدير مشروع محترف للإشراف على مشاريع تقنية كبيرة',
                    'company_name' => 'شركة الحلول الرقمية',
                    'location' => 'الدمام',
                    'salary' => 20000,
                    'experience_required' => '5-7 سنوات',
                    'whatsapp' => '966500000003',
                    'email' => '<EMAIL>',
                    'phone' => '0500000003',
                ],
                [
                    'job_title' => 'مطور تطبيقات موبايل',
                    'job_description' => 'مطلوب مطور تطبيقات موبايل للعمل على تطبيقات Android و iOS',
                    'company_name' => 'تك موبايل',
                    'location' => 'الرياض',
                    'salary' => 18000,
                    'experience_required' => '3-5 سنوات',
                    'whatsapp' => '966500000004',
                    'email' => '<EMAIL>',
                    'phone' => '0500000004',
                ],
                [
                    'job_title' => 'محلل بيانات',
                    'job_description' => 'نبحث عن محلل بيانات ذو خبرة في تحليل البيانات الضخمة واستخراج الرؤى منها',
                    'company_name' => 'شركة البيانات الذكية',
                    'location' => 'جدة',
                    'salary' => 16000,
                    'experience_required' => '2-4 سنوات',
                    'whatsapp' => '966500000005',
                    'email' => '<EMAIL>',
                    'phone' => '0500000005',
                ],
            ];

            // الحصول على معرف المستخدم الأول
            $firstUser = User::first();
            if ($firstUser) {
                foreach ($jobs as $job) {
                    JobPosting::create(array_merge($job, [
                        'user_id' => $firstUser->id,
                        'is_featured' => false,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]));
                }
            }
        } catch (\Exception $e) {
            // تجاهل الخطأ إذا كان هناك مشكلة في جدول الوظائف
            echo "Error adding jobs: " . $e->getMessage() . "\n";
        }

        // إضافة إعلانات
        try {
            $ads = [
                [
                    'title' => 'شقة فاخرة للإيجار',
                    'description' => 'شقة فاخرة مكونة من 3 غرف نوم وصالة كبيرة ومطبخ حديث',
                    'category' => 'عقارات',
                    'subcategory' => 'إيجار',
                    'location' => 'الرياض',
                    'price' => 5000.00,
                    'whatsapp' => '966500000001',
                    'email' => '<EMAIL>',
                    'phone' => '0500000001',
                ],
                [
                    'title' => 'سيارة تويوتا كامري 2023 للبيع',
                    'description' => 'سيارة تويوتا كامري 2023 بحالة ممتازة، ماشية 10,000 كم فقط',
                    'category' => 'سيارات',
                    'subcategory' => 'بيع',
                    'location' => 'جدة',
                    'price' => 120000.00,
                    'whatsapp' => '966500000002',
                    'email' => '<EMAIL>',
                    'phone' => '0500000002',
                ],
                [
                    'title' => 'لابتوب ماك بوك برو 2022',
                    'description' => 'لابتوب ماك بوك برو 2022، ذاكرة 16 جيجا، تخزين 512 جيجا، بحالة ممتازة',
                    'category' => 'إلكترونيات',
                    'subcategory' => 'لابتوبات',
                    'location' => 'الدمام',
                    'price' => 8000.00,
                    'whatsapp' => '966500000003',
                    'email' => '<EMAIL>',
                    'phone' => '0500000003',
                ],
                [
                    'title' => 'أريكة جلدية فاخرة',
                    'description' => 'أريكة جلدية فاخرة بحالة ممتازة، لون بني داكن، تتسع لـ 3 أشخاص',
                    'category' => 'أثاث',
                    'subcategory' => 'صالة',
                    'location' => 'الرياض',
                    'price' => 3500.00,
                    'whatsapp' => '966500000004',
                    'email' => '<EMAIL>',
                    'phone' => '0500000004',
                ],
                [
                    'title' => 'آيفون 14 برو ماكس',
                    'description' => 'آيفون 14 برو ماكس، ذاكرة 256 جيجا، لون أسود، بحالة ممتازة مع جميع الملحقات',
                    'category' => 'إلكترونيات',
                    'subcategory' => 'هواتف',
                    'location' => 'جدة',
                    'price' => 4500.00,
                    'whatsapp' => '966500000005',
                    'email' => '<EMAIL>',
                    'phone' => '0500000005',
                ],
            ];

            // الحصول على معرف المستخدم الأول
            $firstUser = User::first();
            if ($firstUser) {
                foreach ($ads as $ad) {
                    Ad::create(array_merge($ad, [
                        'user_id' => $firstUser->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]));
                }
            }
        } catch (\Exception $e) {
            // تجاهل الخطأ إذا كان هناك مشكلة في جدول الإعلانات
            echo "Error adding ads: " . $e->getMessage() . "\n";
        }
    }
}
