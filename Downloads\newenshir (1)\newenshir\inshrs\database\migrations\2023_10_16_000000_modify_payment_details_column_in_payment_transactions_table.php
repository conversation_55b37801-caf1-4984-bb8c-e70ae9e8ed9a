<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // تغيير نوع العمود من text إلى json
        if (Schema::hasTable('payment_transactions')) {
            // إضافة عمود جديد
            Schema::table('payment_transactions', function (Blueprint $table) {
                if (!Schema::hasColumn('payment_transactions', 'payment_details_json')) {
                    $table->json('payment_details_json')->nullable()->after('payment_details');
                }
            });

            // نقل البيانات من العمود القديم إلى الجديد
            $transactions = DB::table('payment_transactions')->get();
            foreach ($transactions as $transaction) {
                if (!empty($transaction->payment_details)) {
                    try {
                        // محاولة تحويل البيانات إلى JSON
                        $jsonData = json_decode($transaction->payment_details, true);
                        if (json_last_error() === JSON_ERROR_NONE) {
                            // البيانات صالحة كـ JSON
                            DB::table('payment_transactions')
                                ->where('id', $transaction->id)
                                ->update(['payment_details_json' => $transaction->payment_details]);
                        } else {
                            // البيانات ليست JSON صالح
                            DB::table('payment_transactions')
                                ->where('id', $transaction->id)
                                ->update(['payment_details_json' => json_encode(['data' => $transaction->payment_details])]);
                        }
                    } catch (\Exception $e) {
                        // في حالة حدوث خطأ
                        DB::table('payment_transactions')
                            ->where('id', $transaction->id)
                            ->update(['payment_details_json' => json_encode(['error' => true])]);
                    }
                }
            }

            // حذف العمود القديم وإعادة تسمية العمود الجديد
            Schema::table('payment_transactions', function (Blueprint $table) {
                $table->dropColumn('payment_details');
            });

            Schema::table('payment_transactions', function (Blueprint $table) {
                $table->renameColumn('payment_details_json', 'payment_details');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('payment_transactions')) {
            // إضافة عمود نصي جديد
            Schema::table('payment_transactions', function (Blueprint $table) {
                if (!Schema::hasColumn('payment_transactions', 'payment_details_text')) {
                    $table->text('payment_details_text')->nullable()->after('payment_details');
                }
            });

            // نقل البيانات من العمود القديم إلى الجديد
            $transactions = DB::table('payment_transactions')->get();
            foreach ($transactions as $transaction) {
                if (!empty($transaction->payment_details)) {
                    try {
                        $jsonData = is_string($transaction->payment_details)
                            ? $transaction->payment_details
                            : json_encode($transaction->payment_details);

                        DB::table('payment_transactions')
                            ->where('id', $transaction->id)
                            ->update(['payment_details_text' => $jsonData]);
                    } catch (\Exception $e) {
                        // في حالة حدوث خطأ
                        DB::table('payment_transactions')
                            ->where('id', $transaction->id)
                            ->update(['payment_details_text' => '{"error": true}']);
                    }
                }
            }

            // حذف العمود القديم وإعادة تسمية العمود الجديد
            Schema::table('payment_transactions', function (Blueprint $table) {
                $table->dropColumn('payment_details');
            });

            Schema::table('payment_transactions', function (Blueprint $table) {
                $table->renameColumn('payment_details_text', 'payment_details');
            });
        }
    }
};
