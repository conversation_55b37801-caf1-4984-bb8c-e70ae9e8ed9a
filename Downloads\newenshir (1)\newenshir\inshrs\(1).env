APP_NAME=Laravel
APP_ENV=production
APP_KEY=base64:OeCpNDzb8TtZ1Y3sc6SJ/5mEDuRQL/wuJ4pwBzOEj2o=
APP_DEBUG=true
APP_URL=https://2f43-2a02-cb80-420a-2a28-6459-888e-c6c2-d99d.ngrok-free.app  # هذا هو رابط ngrok الخاص بك

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel-project-login
DB_USERNAME=root
DB_PASSWORD=

# DB_CONNECTION=pgsql
# DB_HOST=dpg-cvtrapk9c44c738qfqdg-a.oregon-postgres.render.com
# DB_PORT=5432
# DB_DATABASE=laravel_project_login
# DB_USERNAME=laravel_project_login_user
# DB_PASSWORD=d9KkV15JiGqIdO5kXpQN1gLOX78L8djV


BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1




VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# تغيير هذا إلى 'live' عند الانتقال إلى الإنتاج
PAYPAL_MODE=live

 # أدخل بيانات الاعتماد الحقيقية هنا عند الانتقال إلى الإنتاج
PAYPAL_LIVE_CLIENT_ID=AU9c878Prf5y78csx8mpEBIisLEpvY0csJPYI17kGzQe42SoVnSWQvxWWcvV9QpfZpPDtXkrYwF28axQ
PAYPAL_LIVE_CLIENT_SECRET=EBnSMH8n3Y-G-Z4QKTy1IBQvjiBhguqG7Ny63HOfYq4Iqp4wy4Se4YfgTzNJxgYHKgHVJRNupO2LdIkU

# إعدادات Moyasar للدفع عبر STC Pay
MOYASAR_MODE=test
MOYASAR_KEY=pk_test_6VLyR2vpNYdeJjsNW9ammLRYXoXpH8pxA88goNNj
MOYASAR_SECRET=sk_test_ozRkJnnXXSR1ii36rm2ZSVFop3NoGksPz3vE1LUB