@extends('layouts.dashboard')

@section('content')
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-bell me-2"></i> الإشعارات</h5>
                    @if($notifications->where('is_read', false)->count() > 0)
                        <form action="{{ route('notifications.mark-all-as-read') }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-sm btn-light">
                                <i class="fas fa-check-double me-1"></i> تحديد الكل كمقروء
                            </button>
                        </form>
                    @endif
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i> {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i> {{ session('error') }}
                        </div>
                    @endif

                    @forelse($notifications as $notification)
                        <div class="card mb-3 {{ $notification->is_read ? 'border-light' : 'border-primary' }}">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="card-title mb-0">
                                        @if($notification->type == 'success')
                                            <i class="fas fa-check-circle text-success me-2"></i>
                                        @elseif($notification->type == 'error')
                                            <i class="fas fa-times-circle text-danger me-2"></i>
                                        @elseif($notification->type == 'warning')
                                            <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                        @else
                                            <i class="fas fa-info-circle text-info me-2"></i>
                                        @endif
                                        {{ $notification->title }}
                                        @if(!$notification->is_read)
                                            <span class="badge bg-primary ms-2">جديد</span>
                                        @endif
                                    </h6>
                                    <small class="text-muted">{{ $notification->created_at->diffForHumans() }}</small>
                                </div>
                                <p class="card-text">{{ $notification->message }}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        @if($notification->related_to == 'bank_transfer' && $notification->related_id)
                                            <a href="{{ route('points.transactions') }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye me-1"></i> عرض المعاملة
                                            </a>
                                        @endif
                                    </div>
                                    <div class="d-flex">
                                        @if(!$notification->is_read)
                                            <form action="{{ route('notifications.mark-as-read', $notification->id) }}" method="POST" class="me-2">
                                                @csrf
                                                <button type="submit" class="btn btn-sm btn-outline-success">
                                                    <i class="fas fa-check me-1"></i> تحديد كمقروء
                                                </button>
                                            </form>
                                        @endif
                                        <form action="{{ route('notifications.destroy', $notification->id) }}" method="POST">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('هل أنت متأكد من حذف هذا الإشعار؟')">
                                                <i class="fas fa-trash-alt me-1"></i> حذف
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-5">
                            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد إشعارات</h5>
                        </div>
                    @endforelse

                    <div class="d-flex justify-content-center mt-4">
                        {{ $notifications->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
