<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار CSRF Token</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 اختبار CSRF Token</h1>
        
        <div id="results"></div>
        
        <button onclick="testCSRF()">اختبار CSRF Token</button>
        <button onclick="testSaveRoute()">اختبار رابط الحفظ</button>
        <button onclick="clearResults()">مسح النتائج</button>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function testCSRF() {
            addResult('🔍 بدء اختبار CSRF Token...', 'info');
            
            // محاولة الحصول على CSRF token من Laravel
            fetch('/ads')
                .then(response => response.text())
                .then(html => {
                    // البحث عن CSRF token في HTML
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const csrfMeta = doc.querySelector('meta[name="csrf-token"]');
                    
                    if (csrfMeta && csrfMeta.getAttribute('content')) {
                        const token = csrfMeta.getAttribute('content');
                        addResult(`✅ CSRF Token موجود: ${token.substring(0, 20)}...`, 'success');
                        
                        // حفظ التوكن للاختبارات التالية
                        window.csrfToken = token;
                        
                        return token;
                    } else {
                        addResult('❌ CSRF Token غير موجود في صفحة /ads', 'error');
                        return null;
                    }
                })
                .catch(error => {
                    addResult(`❌ خطأ في الحصول على CSRF Token: ${error.message}`, 'error');
                });
        }
        
        function testSaveRoute() {
            addResult('🔍 بدء اختبار رابط الحفظ...', 'info');
            
            if (!window.csrfToken) {
                addResult('⚠️ يجب اختبار CSRF Token أولاً', 'error');
                return;
            }
            
            // اختبار رابط /saved/toggle
            fetch('/saved/toggle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': window.csrfToken,
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    item_type: 'ad',
                    item_id: 1
                })
            })
            .then(response => {
                addResult(`📥 استجابة الخادم: ${response.status} ${response.statusText}`, 'info');
                
                if (response.status === 200) {
                    addResult('✅ رابط /saved/toggle يعمل بشكل صحيح', 'success');
                    return response.json();
                } else if (response.status === 401) {
                    addResult('⚠️ يتطلب تسجيل الدخول', 'error');
                } else if (response.status === 404) {
                    addResult('❌ رابط /saved/toggle غير موجود', 'error');
                } else if (response.status === 419) {
                    addResult('❌ CSRF Token غير صحيح', 'error');
                } else if (response.status === 500) {
                    addResult('❌ خطأ في الخادم', 'error');
                } else {
                    addResult(`⚠️ استجابة غير متوقعة: ${response.status}`, 'error');
                }
                
                return response.text();
            })
            .then(data => {
                if (typeof data === 'object') {
                    addResult(`📊 بيانات الاستجابة: ${JSON.stringify(data)}`, 'success');
                } else {
                    addResult(`📄 محتوى الاستجابة: ${data.substring(0, 100)}...`, 'info');
                }
            })
            .catch(error => {
                addResult(`❌ خطأ في الشبكة: ${error.message}`, 'error');
            });
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 تم تحميل صفحة الاختبار', 'success');
            addResult('💡 اضغط على "اختبار CSRF Token" للبدء', 'info');
        });
    </script>
</body>
</html>
