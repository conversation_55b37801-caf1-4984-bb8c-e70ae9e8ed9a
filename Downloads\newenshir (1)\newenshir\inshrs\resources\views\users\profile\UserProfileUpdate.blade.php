<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <title>تعديل البيانات الشخصية</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2A5C82;
            --secondary-color: #3BAAB5;
            --accent-color: #FF6B6B;
        }

        body {
            background: #f8f9fa;
            font-family: 'Tajawal', sans-serif;
            color: #000;
            line-height: 1.7;
        }

        .header-bg {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            padding: 80px 0;
            color: white;
            border-radius: 0 0 30px 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .profile-img {
            width: 180px;
            height: 180px;
            border: 5px solid white;
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            transition: transform 0.3s;
        }

        .profile-img:hover {
            transform: scale(1.05);
        }

        .section-title {
            position: relative;
            padding-bottom: 15px;
            margin-bottom: 30px;
            color: var(--primary-color);
        }

        .section-title:after {
            content: "";
            position: absolute;
            bottom: 0;
            right: 0;
            width: 50px;
            height: 3px;
            background: var(--secondary-color);
        }

        .skill-badge {
            background: var(--primary-color);
            padding: 10px 20px;
            border-radius: 25px;
            color: white;
            transition: all 0.3s;
        }

        .skill-badge:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .project-card {
            border: none;
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s;
            background: white;
        }

        .project-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }

        .timeline {
            position: relative;
            padding: 40px 0;
        }

        .timeline-item {
            position: relative;
            padding: 20px;
            background: white;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .contact-info {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .social-icon {
            font-size: 24px;
            color: var(--primary-color);
            transition: all 0.3s;
        }

        .social-icon:hover {
            color: var(--secondary-color);
            transform: scale(1.2);
        }

        .editable {
            border: 1px dashed #ddd;
            padding: 5px;
            min-height: 30px;
            cursor: text;
            background-color: #fff;
        }
    </style>
</head>
<body>
    <header class="header-bg">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-4 text-center">
                    <img src="https://source.unsplash.com/random/600x600/?person" class="profile-img rounded-circle" alt="الملف الشخصي">
                    <input type="file" accept="image/*" style="margin-top: 10px;">
                </div>
                <div class="col-md-8">
                    <h1 class="display-4 mb-3 editable" contenteditable="true" style="color: var(--primary-color);">أحمد عبدالله</h1>
                    <h3 class="mb-4 editable" contenteditable="true" style="color: var(--primary-color);">مطور ويب ومصمم واجهات مستخدم</h3>
                    <input type="color" value="#000" onchange="this.parentNode.style.color = this.value;">
                    <div class="d-flex gap-3">
                        <a href="#" class="social-icon"><i class="fab fa-github"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                    </div>
                </div>
            </div>
            <input type="file" accept="image/*" style="margin-top: 10px;">
        </div>
    </header>

        <div class="text-center">
            <button type="button" onclick="window.location='{{ url('/UserProfileupdate') }}'" class="btn btn-lg" style="background-color: var(--secondary-color); border-color: var(--secondary-color); color: white; border-radius: 25px; padding: 12px 30px;">حفظ التعديل</button>
            <button type="button" onclick="window.location='{{ url('/UserProfileShow') }}'" class="btn btn-lg" style="background-color: #ccc; border-color: #ccc; color: #000; border-radius: 25px; padding: 12px 30px;">إلغاء</button>
        </div>

    <main class="container my-5">
        <section class="mb-5">
            <h2 class="section-title" style="color: var(--primary-color);">نبذة عني <input type="color" value="var(--primary-color)" onchange="this.parentNode.style.color = this.value;"></h2>
            <p class="lead editable" contenteditable="true">أحمد عبدالله، مطور ويب متخصص في تطوير تطبيقات الويب الحديثة باستخدام JavaScript وتقنيات الويب الأخرى. لديّ خبرة واسعة في تصميم وتطوير واجهات المستخدم والخوادم، وأسعى دائمًا لتقديم حلول مبتكرة وفعالة.</p>
            <p class="editable" contenteditable="true">أؤمن بأهمية التعلم المستمر ومواكبة أحدث التقنيات في مجال تطوير الويب. لديّ شغف كبير بتحسين تجربة المستخدم وتقديم منتجات عالية الجودة.</p>
        </section>

        <section class="mb-5">
            <h2 class="section-title" style="color: var(--primary-color);">المهارات التقنية <input type="color" value="var(--primary-color)" onchange="this.parentNode.style.color = this.value;"></h2>
            <input type="color" value="#000" onchange="
                const skillBadges = document.querySelectorAll('.skill-badge');
                skillBadges.forEach(badge => {
                    badge.style.color = this.value;
                });
            ">
            <div class="d-flex flex-wrap gap-3">
                <span class="skill-badge editable" contenteditable="true" style="color: #000;"><i class="fab fa-react me-2"></i>React JS</span>
                <span class="skill-badge editable" contenteditable="true" style="color: #000;"><i class="fab fa-node me-2"></i>Node.js</span>
                <span class="skill-badge editable" contenteditable="true" style="color: #000;"><i class="fab fa-js me-2"></i>JavaScript</span>
                <span class="skill-badge editable" contenteditable="true" style="color: #000;"><i class="fab fa-sass me-2"></i>Sass</span>
                <span class="skill-badge editable" contenteditable="true" style="color: #000;"><i class="fab fa-docker me-2"></i>Docker</span>
                <span class="skill-badge editable" contenteditable="true" style="color: #000;"><i class="fab fa-html5 me-2"></i>HTML5</span>
                <span class="skill-badge editable" contenteditable="true" style="color: #000;"><i class="fab fa-css3 me-2"></i>CSS3</span>
                <span class="skill-badge editable" contenteditable="true" style="color: #000;"><i class="fab fa-php me-2"></i>PHP</span>
                <span class="skill-badge editable" contenteditable="true" style="color: #000;"><i class="fab fa-laravel me-2"></i>Laravel</span>
                <span class="skill-badge editable" contenteditable="true" style="color: #000;"><i class="fab fa-git me-2"></i>Git</span>
            </div>
        </section>

        <section class="mb-5">
            <h2 class="section-title" style="color: var(--primary-color);">اللغات <input type="color" value="var(--primary-color)" onchange="this.parentNode.style.color = this.value;"></h2>
            <div class="d-flex flex-wrap gap-3">
                <div class="col-md-6">
                    <div class="project-card">
                        <div class="p-4">
                            <h4 class="editable" contenteditable="true">العربية</h4>
                            <p class="text-muted editable" contenteditable="true">اللغة الأم</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="project-card">
                        <div class="p-4">
                            <h4 class="editable" contenteditable="true">الإنجليزية</h4>
                            <p class="text-muted editable" contenteditable="true">مستوى متقدم</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="mb-5">
            <h2 class="section-title" style="color: var(--primary-color);">الخبرة العملية <input type="color" value="var(--primary-color)" onchange="this.parentNode.style.color = this.value;"></h2>
            <div class="timeline">
                <div class="timeline-item">
                    <h4 class="editable" contenteditable="true">مطور ويب رئيسي</h4>
                    <h5 class="text-muted editable" contenteditable="true">شركة التقنية المتقدمة | 2020 - الآن</h5>
                    <ul class="mt-3">
                        <li class="editable" contenteditable="true">قيادة فريق تطوير مكون من 5 أعضاء</li>
                        <li class="editable" contenteditable="true">تحسين أداء التطبيقات بنسبة 40%</li>
                        <li class="editable" contenteditable="true">تطوير وتنفيذ حلول ويب مبتكرة</li>
                        <li class="editable" contenteditable="true">التعاون مع فرق التصميم والمنتج لتقديم تجارب مستخدم متميزة</li>
                    </ul>
                </div>
                <div class="timeline-item">
                    <h4 class="editable" contenteditable="true">مطور واجهات أمامية</h4>
                    <h5 class="text-muted editable" contenteditable="true">شركة الويب الإبداعي | 2018 - 2020</h5>
                    <ul class="mt-3">
                        <li class="editable" contenteditable="true">تطوير 15+ واجهة مستخدم تفاعلية</li>
                        <li class="editable" contenteditable="true">تحسين تجربة المستخدم بنسبة 60%</li>
                        <li class="editable" contenteditable="true">استخدام أحدث التقنيات مثل React و Vue.js</li>
                        <li class="editable" contenteditable="true">العمل على مشاريع متنوعة في مجالات مختلفة</li>
                    </ul>
                </div>
            </div>
        </section>

        <section class="mb-5">
            <h2 class="section-title" style="color: var(--primary-color);">التعليم <input type="color" value="var(--primary-color)" onchange="this.parentNode.style.color = this.value;"></h2>
            <div class="timeline">
                <div class="timeline-item">
                    <h4 class="editable" contenteditable="true">بكالوريوس علوم الحاسب</h4>
                    <h5 class="text-muted editable" contenteditable="true">جامعة الملك سعود | 2014 - 2018</h5>
                </div>
            </div>
        </section>

        <section class="mb-5">
            <h2 class="section-title" style="color: var(--primary-color);">الدورات التدريبية <input type="color" value="var(--primary-color)" onchange="this.parentNode.style.color = this.value;"></h2>
            <div class="d-flex flex-wrap gap-3">
                <span class="skill-badge editable" contenteditable="true" style="color: #000;">دورة تطوير الويب الكامل</span>
                <span class="skill-badge editable" contenteditable="true" style="color: #000;">دورة تصميم واجهات المستخدم</span>
            </div>
        </section>
    </main>

    <footer class="bg-dark  py-4">
        <div class="container text-center">
            <div class="contact-info mb-4 text-dark">
                <h4 class="mb-3 text-dark">تواصل معي</h4>
                <p class="mb-1 text-dark editable" contenteditable="true"><i class="fas fa-phone me-2"></i>+966 55 123 4567</p>
                <p class="mb-1 text-dark editable" contenteditable="true"><i class="fas fa-envelope me-2"></i><EMAIL></p>
            </div>
            <p class="mb-0 text-dark">&copy; 2024 أحمد عبدالله. جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <script>
        // تغيير الألوان الديناميكية
        const primaryColorPickers = document.querySelectorAll('input[type="color"]');
        primaryColorPickers.forEach(picker => {
            picker.addEventListener('input', function(e) {
                this.parentNode.style.color = e.target.value;
            });
        });
    </script>
    <script>
        const skillColorPicker = document.querySelector('section[style="color: var(--primary-color);"] + div input[type="color"]');

        skillColorPicker.addEventListener('input', function(e) {
            const skillBadges = document.querySelectorAll('.skill-badge');
            skillBadges.forEach(badge => {
                badge.style.color = e.target.value;
            });
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>