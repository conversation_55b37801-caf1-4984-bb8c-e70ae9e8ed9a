<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>استعادة كلمة المرور - {{ config('app.name') }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            text-align: right;
        }
        
        .reset-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }
        
        .reset-card {
            background: white;
            border-radius: 1.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            animation: slideUp 0.6s ease;
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .reset-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .reset-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .reset-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }
        
        .reset-body {
            padding: 2rem;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .step {
            display: flex;
            align-items: center;
            margin: 0 1rem;
        }
        
        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-left: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .step.active .step-circle {
            background: #667eea;
            color: white;
        }
        
        .step.completed .step-circle {
            background: #28a745;
            color: white;
        }
        
        .step-text {
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 500;
        }
        
        .step.active .step-text {
            color: #667eea;
            font-weight: 600;
        }
        
        .step.completed .step-text {
            color: #28a745;
            font-weight: 600;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }
        
        .form-label i {
            margin-left: 0.5rem;
            color: #667eea;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-reset {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 0.75rem;
            padding: 0.75rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-reset:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-reset:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-reset .spinner {
            display: none;
        }
        
        .btn-reset.loading .spinner {
            display: inline-block;
        }
        
        .btn-reset.loading .btn-text {
            display: none;
        }
        
        .reset-instructions {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196f3;
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .instructions-title {
            font-weight: 600;
            color: #1976d2;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }
        
        .instructions-title i {
            margin-left: 0.5rem;
        }
        
        .instructions-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .instructions-list li {
            padding: 0.5rem 0;
            color: #1565c0;
            display: flex;
            align-items: flex-start;
        }
        
        .instructions-list li::before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-left: 0.5rem;
            margin-top: 0.1rem;
        }
        
        .email-status {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 1px solid #28a745;
            border-radius: 0.75rem;
            padding: 1rem;
            margin-top: 1rem;
            display: none;
        }
        
        .email-status.show {
            display: block;
            animation: slideDown 0.3s ease;
        }
        
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .status-text {
            color: #155724;
            margin: 0;
            display: flex;
            align-items: center;
        }
        
        .status-text i {
            margin-left: 0.5rem;
        }
        
        .resend-timer {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffc107;
            border-radius: 0.75rem;
            padding: 1rem;
            margin-top: 1rem;
            text-align: center;
            display: none;
        }
        
        .resend-timer.show {
            display: block;
        }
        
        .timer-text {
            color: #856404;
            margin: 0;
            font-weight: 500;
        }
        
        .resend-button {
            background: none;
            border: none;
            color: #667eea;
            font-weight: 600;
            cursor: pointer;
            text-decoration: underline;
        }
        
        .resend-button:hover {
            color: #5a6fd8;
        }
        
        .resend-button:disabled {
            color: #6c757d;
            cursor: not-allowed;
            text-decoration: none;
        }
        
        .reset-footer {
            text-align: center;
            padding: 1rem 2rem 2rem;
            border-top: 1px solid #f8f9fa;
        }
        
        .reset-footer a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .reset-footer a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .reset-container {
                padding: 1rem;
            }
            
            .reset-card {
                margin: 0;
            }
            
            .reset-header,
            .reset-body {
                padding: 1.5rem;
            }
            
            .step {
                margin: 0 0.5rem;
            }
            
            .step-text {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-card">
            <!-- Header -->
            <div class="reset-header">
                <h1>
                    <i class="fas fa-key me-2"></i>
                    استعادة كلمة المرور
                </h1>
                <p>سنساعدك في استعادة الوصول إلى حسابك</p>
            </div>
            
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step active" id="step-1">
                    <div class="step-circle">1</div>
                    <div class="step-text">إدخال البريد</div>
                </div>
                <div class="step" id="step-2">
                    <div class="step-circle">2</div>
                    <div class="step-text">التحقق</div>
                </div>
            </div>
            
            <!-- Body -->
            <div class="reset-body">
                <!-- Instructions -->
                <div class="reset-instructions">
                    <div class="instructions-title">
                        <i class="fas fa-info-circle"></i>
                        كيفية استعادة كلمة المرور
                    </div>
                    <ul class="instructions-list">
                        <li>أدخل البريد الإلكتروني المرتبط بحسابك</li>
                        <li>تحقق من صندوق الوارد لرسالة استعادة كلمة المرور</li>
                        <li>اتبع الرابط في الرسالة لإنشاء كلمة مرور جديدة</li>
                        <li>سجل الدخول بكلمة المرور الجديدة</li>
                    </ul>
                </div>
                
                <form id="resetForm" method="POST" action="{{ route('password.email') }}">
                    @csrf
                    
                    <div class="form-group">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope"></i>
                            البريد الإلكتروني
                        </label>
                        <input type="email" 
                               class="form-control" 
                               id="email" 
                               name="email" 
                               value="{{ old('email') }}" 
                               required 
                               autocomplete="email"
                               placeholder="أدخل بريدك الإلكتروني">
                        
                        <!-- Email Validator Component -->
                        @include('components.email-validator', [
                            'inputId' => 'email',
                            'checkAvailability' => false,
                            'realTimeValidation' => true,
                            'showSuggestions' => true
                        ])
                    </div>
                    
                    <button type="submit" class="btn btn-reset" id="resetBtn">
                        <span class="spinner">
                            <i class="fas fa-spinner fa-spin"></i>
                            جاري الإرسال...
                        </span>
                        <span class="btn-text">
                            <i class="fas fa-paper-plane me-2"></i>
                            إرسال رابط الاستعادة
                        </span>
                    </button>
                </form>
                
                <!-- Email Status -->
                <div class="email-status" id="emailStatus">
                    <p class="status-text">
                        <i class="fas fa-check-circle"></i>
                        <span id="statusMessage">تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني</span>
                    </p>
                </div>
                
                <!-- Resend Timer -->
                <div class="resend-timer" id="resendTimer">
                    <p class="timer-text">
                        يمكنك طلب إرسال رابط جديد خلال <span id="countdown">60</span> ثانية
                    </p>
                    <button type="button" class="resend-button" id="resendBtn" onclick="resendEmail()" disabled>
                        إعادة الإرسال
                    </button>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="reset-footer">
                <p class="mb-2">
                    تذكرت كلمة المرور؟ 
                    <a href="{{ route('login') }}">تسجيل الدخول</a>
                </p>
                <p class="mb-0">
                    ليس لديك حساب؟ 
                    <a href="{{ route('register') }}">إنشاء حساب جديد</a>
                </p>
            </div>
        </div>
    </div>

    <!-- Notification System -->
    @if(session('status'))
        @include('components.notification-system', [
            'type' => 'success',
            'title' => 'تم الإرسال!',
            'message' => session('status'),
            'position' => 'top-right',
            'autoHide' => true,
            'duration' => 5000,
            'id' => 'status-notification'
        ])
    @endif

    @if($errors->any())
        @include('components.notification-system', [
            'type' => 'error',
            'title' => 'خطأ',
            'message' => $errors->first(),
            'position' => 'top-right',
            'autoHide' => true,
            'duration' => 5000,
            'id' => 'error-notification'
        ])
    @endif

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let resendCountdown = 60;
        let countdownInterval;
        
        // إظهار الإشعارات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            @if(session('status'))
                showNotification('status-notification');
                showEmailSentStatus();
            @endif
            
            @if($errors->any())
                showNotification('error-notification');
            @endif
        });
        
        function validateForm() {
            const email = document.getElementById('email').value.trim();
            
            if (!email) {
                showErrorNotification('يرجى إدخال البريد الإلكتروني');
                return false;
            }
            
            if (!validateEmailFormat(email)) {
                showErrorNotification('تنسيق البريد الإلكتروني غير صحيح');
                return false;
            }
            
            return true;
        }
        
        function validateEmailFormat(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        function showEmailSentStatus() {
            // تحديث المؤشر
            document.getElementById('step-1').classList.remove('active');
            document.getElementById('step-1').classList.add('completed');
            document.getElementById('step-2').classList.add('active');
            
            // إظهار حالة الإرسال
            document.getElementById('emailStatus').classList.add('show');
            
            // بدء العد التنازلي
            startResendCountdown();
        }
        
        function startResendCountdown() {
            document.getElementById('resendTimer').classList.add('show');
            
            countdownInterval = setInterval(() => {
                resendCountdown--;
                document.getElementById('countdown').textContent = resendCountdown;
                
                if (resendCountdown <= 0) {
                    clearInterval(countdownInterval);
                    document.getElementById('resendBtn').disabled = false;
                    document.querySelector('.timer-text').textContent = 'يمكنك الآن طلب إرسال رابط جديد';
                }
            }, 1000);
        }
        
        function resendEmail() {
            const email = document.getElementById('email').value.trim();
            
            if (!validateForm()) {
                return;
            }
            
            const resendBtn = document.getElementById('resendBtn');
            resendBtn.disabled = true;
            resendBtn.textContent = 'جاري الإرسال...';
            
            // إرسال طلب إعادة الإرسال
            fetch('{{ route("password.email") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ email: email })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccessNotification('تم إعادة إرسال رابط الاستعادة بنجاح');
                    
                    // إعادة تعيين العد التنازلي
                    resendCountdown = 60;
                    resendBtn.textContent = 'إعادة الإرسال';
                    startResendCountdown();
                } else {
                    showErrorNotification(data.message || 'حدث خطأ أثناء إعادة الإرسال');
                    resendBtn.disabled = false;
                    resendBtn.textContent = 'إعادة الإرسال';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorNotification('حدث خطأ في الشبكة. يرجى المحاولة مرة أخرى.');
                resendBtn.disabled = false;
                resendBtn.textContent = 'إعادة الإرسال';
            });
        }
        
        function showErrorNotification(message) {
            const notificationId = 'error-' + Date.now();
            const notificationHtml = `
                <div id="${notificationId}" class="notification-alert notification-error notification-top-right" style="display: none;">
                    <div class="notification-content">
                        <div class="notification-header">
                            <i class="notification-icon fas fa-times-circle"></i>
                            <h6 class="notification-title">خطأ</h6>
                            <button type="button" class="notification-close" onclick="hideNotification('${notificationId}')">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <p class="notification-message">${message}</p>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', notificationHtml);
            showNotification(notificationId);
            
            setTimeout(() => {
                const element = document.getElementById(notificationId);
                if (element) {
                    element.remove();
                }
            }, 5000);
        }
        
        function showSuccessNotification(message) {
            const notificationId = 'success-' + Date.now();
            const notificationHtml = `
                <div id="${notificationId}" class="notification-alert notification-success notification-top-right" style="display: none;">
                    <div class="notification-content">
                        <div class="notification-header">
                            <i class="notification-icon fas fa-check-circle"></i>
                            <h6 class="notification-title">نجح!</h6>
                            <button type="button" class="notification-close" onclick="hideNotification('${notificationId}')">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <p class="notification-message">${message}</p>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', notificationHtml);
            showNotification(notificationId);
        }
        
        // معالجة إرسال النموذج
        document.getElementById('resetForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (!validateForm()) {
                return;
            }
            
            const resetBtn = document.getElementById('resetBtn');
            resetBtn.classList.add('loading');
            resetBtn.disabled = true;
            
            // إرسال النموذج
            const formData = new FormData(this);
            
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccessNotification('تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني');
                    showEmailSentStatus();
                } else {
                    showErrorNotification(data.message || 'حدث خطأ أثناء إرسال رابط الاستعادة');
                }
                
                resetBtn.classList.remove('loading');
                resetBtn.disabled = false;
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorNotification('حدث خطأ في الشبكة. يرجى المحاولة مرة أخرى.');
                
                resetBtn.classList.remove('loading');
                resetBtn.disabled = false;
            });
        });
        
        // تحسين تجربة المستخدم
        document.getElementById('email').addEventListener('input', function() {
            this.classList.remove('is-invalid');
            if (validateEmailFormat(this.value.trim())) {
                this.classList.add('is-valid');
            }
        });
    </script>
</body>
</html>
