<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>تسجيل الدخول - {{ config('app.name') }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            text-align: right;
        }
        
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }
        
        .login-card {
            background: white;
            border-radius: 1.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
            animation: slideUp 0.6s ease;
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .login-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }
        
        .login-body {
            padding: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }
        
        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }
        
        .form-label i {
            margin-left: 0.5rem;
            color: #667eea;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            padding-left: 3rem;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .form-control.is-valid {
            border-color: #28a745;
        }
        
        .form-control.is-invalid {
            border-color: #dc3545;
        }
        
        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            z-index: 2;
        }
        
        .password-toggle {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            z-index: 3;
            padding: 0.25rem;
        }
        
        .password-toggle:hover {
            color: #667eea;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 0.75rem;
            padding: 0.75rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-login:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-login .spinner {
            display: none;
        }
        
        .btn-login.loading .spinner {
            display: inline-block;
        }
        
        .btn-login.loading .btn-text {
            display: none;
        }
        
        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 1rem 0;
        }
        
        .form-check {
            margin: 0;
        }
        
        .form-check-input {
            margin-left: 0.5rem;
        }
        
        .forgot-password {
            color: #667eea;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .forgot-password:hover {
            text-decoration: underline;
        }
        
        .login-footer {
            text-align: center;
            padding: 1rem 2rem 2rem;
            border-top: 1px solid #f8f9fa;
        }
        
        .login-footer a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .login-footer a:hover {
            text-decoration: underline;
        }
        
        .social-login {
            margin: 1.5rem 0;
        }
        
        .social-divider {
            text-align: center;
            margin: 1.5rem 0;
            position: relative;
        }
        
        .social-divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
        }
        
        .social-divider span {
            background: white;
            padding: 0 1rem;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .btn-social {
            border: 2px solid #e9ecef;
            border-radius: 0.75rem;
            padding: 0.75rem;
            width: 100%;
            background: white;
            color: #333;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-bottom: 0.5rem;
        }
        
        .btn-social:hover {
            border-color: #667eea;
            background: #f8f9fa;
            transform: translateY(-1px);
        }
        
        .btn-social i {
            margin-left: 0.5rem;
        }
        
        .login-attempts {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffc107;
            border-radius: 0.75rem;
            padding: 1rem;
            margin-bottom: 1rem;
            display: none;
        }
        
        .login-attempts.show {
            display: block;
            animation: slideDown 0.3s ease;
        }
        
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .attempts-text {
            color: #856404;
            font-size: 0.9rem;
            margin: 0;
            display: flex;
            align-items: center;
        }
        
        .attempts-text i {
            margin-left: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .login-container {
                padding: 1rem;
            }
            
            .login-card {
                margin: 0;
            }
            
            .login-header,
            .login-body {
                padding: 1.5rem;
            }
            
            .remember-forgot {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <!-- Header -->
            <div class="login-header">
                <h1>
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </h1>
                <p>مرحباً بعودتك إلى منصة انشر</p>
            </div>
            
            <!-- Body -->
            <div class="login-body">
                <!-- Login Attempts Warning -->
                <div class="login-attempts" id="loginAttempts">
                    <p class="attempts-text">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span id="attemptsText">تبقى لديك محاولتان لتسجيل الدخول</span>
                    </p>
                </div>
                
                <form id="loginForm" method="POST" action="{{ route('login') }}">
                    @csrf
                    
                    <div class="form-group">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope"></i>
                            البريد الإلكتروني
                        </label>
                        <div class="position-relative">
                            <input type="email" 
                                   class="form-control" 
                                   id="email" 
                                   name="email" 
                                   value="{{ old('email') }}" 
                                   required 
                                   autocomplete="email"
                                   placeholder="أدخل بريدك الإلكتروني">
                            <i class="input-icon fas fa-envelope"></i>
                        </div>
                        
                        <!-- Email Validator Component -->
                        @include('components.email-validator', [
                            'inputId' => 'email',
                            'checkAvailability' => false,
                            'realTimeValidation' => true,
                            'showSuggestions' => false
                        ])
                    </div>
                    
                    <div class="form-group">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock"></i>
                            كلمة المرور
                        </label>
                        <div class="position-relative">
                            <input type="password" 
                                   class="form-control" 
                                   id="password" 
                                   name="password" 
                                   required 
                                   autocomplete="current-password"
                                   placeholder="أدخل كلمة المرور">
                            <button type="button" class="password-toggle" onclick="togglePassword()">
                                <i class="fas fa-eye" id="passwordToggleIcon"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="remember-forgot">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="remember" name="remember">
                            <label class="form-check-label" for="remember">
                                تذكرني
                            </label>
                        </div>
                        <a href="{{ route('password.request') }}" class="forgot-password">
                            نسيت كلمة المرور؟
                        </a>
                    </div>
                    
                    <button type="submit" class="btn btn-login" id="loginBtn">
                        <span class="spinner">
                            <i class="fas fa-spinner fa-spin"></i>
                            جاري تسجيل الدخول...
                        </span>
                        <span class="btn-text">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </span>
                    </button>
                </form>
                
                <!-- Social Login -->
                <div class="social-login">
                    <div class="social-divider">
                        <span>أو سجل الدخول باستخدام</span>
                    </div>
                    
                    <a href="{{ route('auth.google') }}" class="btn btn-social">
                        <i class="fab fa-google" style="color: #db4437;"></i>
                        تسجيل الدخول بـ Google
                    </a>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="login-footer">
                <p class="mb-0">
                    ليس لديك حساب؟ 
                    <a href="{{ route('register') }}">إنشاء حساب جديد</a>
                </p>
            </div>
        </div>
    </div>

    <!-- Notification System -->
    @if(session('status'))
        @include('components.notification-system', [
            'type' => 'success',
            'title' => 'تم بنجاح!',
            'message' => session('status'),
            'position' => 'top-right',
            'autoHide' => true,
            'duration' => 5000,
            'id' => 'status-notification'
        ])
    @endif

    @if($errors->any())
        @include('components.notification-system', [
            'type' => 'error',
            'title' => 'خطأ في تسجيل الدخول',
            'message' => $errors->first(),
            'position' => 'top-right',
            'autoHide' => true,
            'duration' => 5000,
            'id' => 'error-notification'
        ])
    @endif

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let loginAttempts = 0;
        const maxAttempts = 3;
        
        // إظهار الإشعارات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            @if(session('status'))
                showNotification('status-notification');
            @endif
            
            @if($errors->any())
                showNotification('error-notification');
            @endif
        });
        
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
        
        function showLoginAttempts() {
            const attemptsDiv = document.getElementById('loginAttempts');
            const attemptsText = document.getElementById('attemptsText');
            const remaining = maxAttempts - loginAttempts;
            
            if (remaining > 0) {
                attemptsText.textContent = `تبقى لديك ${remaining} محاولة لتسجيل الدخول`;
                attemptsDiv.classList.add('show');
            } else {
                attemptsText.textContent = 'تم استنفاد جميع المحاولات. يرجى المحاولة لاحقاً';
                attemptsDiv.classList.add('show');
                
                // تعطيل النموذج
                document.getElementById('loginBtn').disabled = true;
                document.getElementById('email').disabled = true;
                document.getElementById('password').disabled = true;
            }
        }
        
        function validateForm() {
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;
            
            if (!email) {
                showErrorNotification('يرجى إدخال البريد الإلكتروني');
                return false;
            }
            
            if (!validateEmailFormat(email)) {
                showErrorNotification('تنسيق البريد الإلكتروني غير صحيح');
                return false;
            }
            
            if (!password) {
                showErrorNotification('يرجى إدخال كلمة المرور');
                return false;
            }
            
            return true;
        }
        
        function validateEmailFormat(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        function showErrorNotification(message) {
            const notificationId = 'error-' + Date.now();
            const notificationHtml = `
                <div id="${notificationId}" class="notification-alert notification-error notification-top-right" style="display: none;">
                    <div class="notification-content">
                        <div class="notification-header">
                            <i class="notification-icon fas fa-times-circle"></i>
                            <h6 class="notification-title">خطأ</h6>
                            <button type="button" class="notification-close" onclick="hideNotification('${notificationId}')">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <p class="notification-message">${message}</p>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', notificationHtml);
            showNotification(notificationId);
            
            setTimeout(() => {
                const element = document.getElementById(notificationId);
                if (element) {
                    element.remove();
                }
            }, 5000);
        }
        
        function showSuccessNotification(message) {
            const notificationId = 'success-' + Date.now();
            const notificationHtml = `
                <div id="${notificationId}" class="notification-alert notification-success notification-center" style="display: none;">
                    <div class="notification-content">
                        <div class="notification-header">
                            <i class="notification-icon fas fa-check-circle"></i>
                            <h6 class="notification-title">نجح!</h6>
                        </div>
                        <p class="notification-message">${message}</p>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', notificationHtml);
            showNotification(notificationId);
        }
        
        // معالجة إرسال النموذج
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (!validateForm()) {
                return;
            }
            
            const loginBtn = document.getElementById('loginBtn');
            loginBtn.classList.add('loading');
            loginBtn.disabled = true;
            
            // إرسال النموذج
            const formData = new FormData(this);
            
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccessNotification('تم تسجيل الدخول بنجاح! جاري التوجيه...');
                    setTimeout(() => {
                        window.location.href = data.redirect || '/dashboard';
                    }, 2000);
                } else {
                    loginAttempts++;
                    showErrorNotification(data.message || 'بيانات تسجيل الدخول غير صحيحة');
                    showLoginAttempts();
                    
                    loginBtn.classList.remove('loading');
                    loginBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                loginAttempts++;
                showErrorNotification('حدث خطأ في الشبكة. يرجى المحاولة مرة أخرى.');
                showLoginAttempts();
                
                loginBtn.classList.remove('loading');
                loginBtn.disabled = false;
            });
        });
        
        // تحسين تجربة المستخدم
        document.getElementById('email').addEventListener('input', function() {
            this.classList.remove('is-invalid');
            if (validateEmailFormat(this.value.trim())) {
                this.classList.add('is-valid');
            }
        });
        
        document.getElementById('password').addEventListener('input', function() {
            this.classList.remove('is-invalid');
            if (this.value.length > 0) {
                this.classList.add('is-valid');
            }
        });
    </script>
</body>
</html>
