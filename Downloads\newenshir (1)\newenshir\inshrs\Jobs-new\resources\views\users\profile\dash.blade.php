

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم التوظيف الذكي</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4a90e2;
            --secondary-color: #6c5ce7;
            --background-light: #f8f9fa;
            --background-dark: #2d3436;
            --text-light: #2d3436;
            --text-dark: #f8f9fa;
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Cairo', sans-serif;
        }

        body {
            background-color: var(--background-light);
            color: var(--text-light);
            transition: background-color 0.3s, color 0.3s;
        }

        body.dark-mode {
            background-color: var(--background-dark);
            color: var(--text-dark);
        }

        
        
        
        /* النافبار العلوي */
        .navbar {
            position: fixed;
            top: 0;
            right: 0;
            left: 0;
            height: 60px;
            background-color: white;
            box-shadow: var(--card-shadow);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            z-index: 1000;
        }

        .dark-mode .navbar {
            background-color: #1a1a1a;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .navbar-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .nav-button {
            background: none;
            border: none;
            color: var(--text-light);
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
        }

        .dark-mode .nav-button {
            color: var(--text-dark);
        }

        .nav-button:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }

        /* الشريط الجانبي */
        .sidebar {
            position: fixed;
            right: 0;
            top: 60px;
            bottom: 0;
            width: 250px;
            background-color: white;
            box-shadow: var(--card-shadow);
            padding: 20px;
            transition: transform 0.3s;
        }

        .dark-mode .sidebar {
            background-color: #1a1a1a;
        }

        .sidebar.collapsed {
            transform: translateX(250px);
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 12px;
            color: var(--text-light);
            text-decoration: none;
            border-radius: 8px;
            margin-bottom: 8px;
        }

        .dark-mode .menu-item {
            color: var(--text-dark);
        }

        .menu-item:hover {
            background-color: rgba(74, 144, 226, 0.1);
        }

        .menu-item i {
            margin-left: 12px;
        }

        /* المحتوى الرئيسي */
        .main-content {
            margin-right: 250px;
            margin-top: 60px;
            padding: 20px;
            transition: margin-right 0.3s;
        }

        .main-content.expanded {
            margin-right: 0;
        }

        /* البطاقات الإحصائية */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background-color: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: var(--card-shadow);
        }

        .dark-mode .stat-card {
            background-color: #1a1a1a;
        }

        .stat-card h3 {
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
        }

        /* الرسم البياني */
        .chart-container {
            background-color: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: var(--card-shadow);
            margin-bottom: 20px;
        }

        .dark-mode .chart-container {
            background-color: #1a1a1a;
        }

        #emailChart {
            width: 100%;
            height: 300px;
        }

        /* وضع الظلام */
        .dark-mode-toggle {
            background: none;
            border: none;
            color: var(--text-light);
            cursor: pointer;
            padding: 8px;
        }

        .dark-mode .dark-mode-toggle {
            color: var(--text-dark);
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(250px);
            }
            
            .main-content {
                margin-right: 0;
            }
            
            .sidebar.active {
                transform: translateX(0);
            }
        }
    </style>
</head>
<body>
   
@extends('layouts.app1')


@section('content')

    <!-- المحتوى الرئيسي -->
    <a href="{{ url('/basic-info') }}">المعلومات الشخصية الأساسية</a>
    <main class="main-content">
        <div class="stats-grid">
            <div class="stat-card">
                <h3>إجمالي الرسائل</h3>
                <div class="stat-value">2,451</div>
            </div>
            <div class="stat-card">
                <h3>الشركات</h3>
                <div class="stat-value">9845</div>
            </div>
            <div class="stat-card">
                <h3>الطلبات المستلمه</h3>
                <div class="stat-value">1,250</div>
            </div>

            <div class="stat-card">
                <h3>الطلبات غير المستلمه</h3>
                <div class="stat-value">1,015</div>
            </div>


            <div class="stat-card">
                <h3>معدل الاستجابة</h3>
                <div class="stat-value">87%</div>
            </div>
        </div>

        <div class="chart-container">
            <h2>تحليل الطلبات الوظيفة </h2>
            <canvas id="emailChart"></canvas>
        </div>
    </main>





    @endsection


    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.0/chart.min.js"></script>
   
</body>
</html>