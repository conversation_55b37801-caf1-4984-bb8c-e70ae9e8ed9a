<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الباحثين عن عمل</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700&display=swap');
        body {
            font-family: 'Almarai', sans-serif;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center text-gray-800 mb-8">الباحثين عن عمل</h1>

        @if($jobSeekers->isEmpty())
            <div class="text-center bg-white shadow-md rounded-lg p-8">
                <svg class="mx-auto h-16 w-16 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p class="mt-4 text-xl text-gray-500">لا يوجد باحثين عن عمل حاليًا</p>
            </div>
        @else
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($jobSeekers as $seeker)
                    <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 space-y-4">
                        <div class="flex items-center space-x-4 reverse-x">
                            <div class="flex-shrink-0">
                                <img src="{{ $seeker->user->avatar ?? '/default-avatar.png' }}" 
                                     alt="{{ $seeker->user->name ?? 'باحث عن عمل' }}" 
                                     class="w-16 h-16 rounded-full object-cover">
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-blue-600">{{ $seeker->user->name ?? 'غير معروف' }}</h3>
                                <p class="text-sm text-gray-500">{{ $seeker->specialization ?? 'التخصص غير محدد' }}</p>
                            </div>
                        </div>

                        <div class="space-y-2">
                            <div class="flex items-center space-x-2 reverse-x">
                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                <span class="text-gray-600">الخبرة: {{ $seeker->experience ? $seeker->experience . ' سنوات' : 'بدون خبرة' }}</span>
                            </div>

                            <div class="flex items-center space-x-2 reverse-x">
                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                </svg>
                                <span class="text-gray-600">المهارات: {{ $seeker->skills ?? 'غير محدد' }}</span>
                            </div>

                            <div class="flex items-center space-x-2 reverse-x">
                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                <span class="text-gray-600">{{ $seeker->location ?? 'الموقع غير محدد' }}</span>
                            </div>
                        </div>

                        <div class="bg-gray-100 p-3 rounded-lg">
                            <p class="text-sm text-gray-700">{{ $seeker->description ?? 'لم يتم إضافة وصف' }}</p>
                        </div>

                        <div class="pt-4 border-t border-gray-200">
                            <a href="#" class="block w-full text-center bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition-colors">
                                عرض التفاصيل
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        @endif
    </div>
</body>
</html>