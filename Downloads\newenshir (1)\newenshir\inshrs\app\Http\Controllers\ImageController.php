<?php

namespace App\Http\Controllers;

use App\Models\Image;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;

class ImageController extends Controller
{
    /**
     * Display the image from the database
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $image = Image::findOrFail($id);

        // Decode the base64 data
        $imageData = base64_decode($image->data);

        // Create a response with the appropriate content type
        $response = Response::make($imageData, 200);
        $response->header('Content-Type', $image->type);

        return $response;
    }
}
