@extends('layouts.appdata')

@section('content')
<div class="container mt-5 text-end" dir="rtl">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-lg border-0 rounded-4">
                <div class="card-header bg-gradient  text-center py-3 rounded-top-4">
                    <h2 class="fw-bold ">إضافة اعلان عمل جديدة</h2>
                    
                </div>
                <div class="card-body">
                    
                    <!-- عرض رسالة النجاح -->
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if (session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <strong>تنبيه!</strong> {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

                    
                    <form action="{{ route('job_seekers.store') }}" method="POST">
                        @csrf

                        <div class="mb-3 input-group">
                            <span class="input-group-text"><i class="fas fa-briefcase"></i></span>
                            <input type="text" name="job_title" class="form-control" placeholder="عنوان الوظيفة" value="{{ old('job_title') }}" required>
                        </div>

                        <div class="mb-3 input-group">
                            <span class="input-group-text"><i class="fas fa-align-left"></i></span>
                            <textarea name="description" class="form-control" rows="4" placeholder="وصف الوظيفة">{{ old('description') }}</textarea>
                        </div>

                        <div class="mb-3 input-group">
                            <span class="input-group-text"><i class="fas fa-user-graduate"></i></span>
                            <input type="text" name="specialization" class="form-control" placeholder="التخصص" value="{{ old('specialization') }}">
                        </div>

                        <div class="mb-3 input-group">
                            <span class="input-group-text"><i class="fas fa-clock"></i></span>
                            <input type="number" name="experience" class="form-control" placeholder="سنوات الخبرة" value="{{ old('experience') }}">
                        </div>

                        <div class="mb-3 input-group">
                            <span class="input-group-text"><i class="fas fa-lightbulb"></i></span>
                            <textarea name="skills" class="form-control" rows="3" placeholder="المهارات">{{ old('skills') }}</textarea>
                        </div>

                        <div class="mb-3 input-group">
                            <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                            <input type="text" name="location" class="form-control" placeholder="الموقع" value="{{ old('location') }}">
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3 input-group">
                                <span class="input-group-text"><i class="fab fa-whatsapp"></i></span>
                                <input type="text" name="whatsapp" class="form-control" placeholder="رقم الواتساب" value="{{ old('whatsapp') }}">
                            </div>
                            <div class="col-md-6 mb-3 input-group">
                                <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                <input type="text" name="phone" class="form-control" placeholder="رقم الهاتف" value="{{ old('phone') }}">
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg rounded-pill shadow-sm">
                                <i class="fas fa-save"></i> حفظ الوظيفة
                            </button>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .bg-gradient {
        background: linear-gradient(to right, #3b82f6, #10b981);
    }
</style>
@endsection
