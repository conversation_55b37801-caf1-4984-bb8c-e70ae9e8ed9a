<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تعديل الإعلان</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <!-- إضافة مكتبة Leaflet للخرائط -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    <!-- إضافة ملف الألوان المخصص -->
    <link href="{{ asset('css/colors.css') }}" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Tajawal', sans-serif;
            direction: rtl;
            text-align: right;
        }

        .card {
            border-radius: 1rem;
        }

        label {
            font-weight: 600;
        }

        .form-control:focus {
            box-shadow: 0 0 0 0.15rem rgba(13,110,253,.25);
        }

        button[type="submit"]:hover {
            transform: translateY(-2px);
            transition: all 0.3s ease-in-out;
        }

        /* أنماط الخريطة */
        #map-container {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        #map-modal {
            width: 90%;
            max-width: 800px;
            height: 80%;
            max-height: 600px;
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        #map-header {
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #eee;
        }

        #map-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
        }

        #map {
            flex: 1;
            width: 100%;
        }

        #map-footer {
            padding: 15px;
            text-align: center;
            border-top: 1px solid #eee;
        }

        #map-confirm {
            padding: 8px 20px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        #map-confirm:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
        }

        .map-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 8px 15px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
        }

        .map-button:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm p-4 bg-white">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <a href="{{ url()->previous() }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right ml-1"></i> رجوع
                    </a>
                    <h2 class="text-center text-primary fw-bold mb-0">
                        <i class="fas fa-edit ms-2"></i>تعديل الإعلان
                    </h2>
                    <a href="{{ url('/dashboard') }}" class="btn btn-outline-primary">
                        <i class="fas fa-tachometer-alt ml-1"></i> لوحة التحكم
                    </a>
                </div>

                <form action="{{ route('ads.update', $ad->id) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')

                    <div class="mb-3">
                        <label for="title" class="form-label">العنوان</label>
                        <input type="text" name="title" id="title" class="form-control" value="{{ old('title', $ad->title) }}" required>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea name="description" id="description" class="form-control" rows="4" required>{{ old('description', $ad->description) }}</textarea>
                    </div>

                    <div class="mb-3">
                        <label for="images" class="form-label">صور الإعلان (يمكنك اختيار حتى 5 صور)</label>

                        <!-- عرض الصور الحالية -->
                        @if($ad->images->count() > 0)
                            <div class="mb-3">
                                <p class="form-text">الصور الحالية:</p>
                                <div class="d-flex flex-wrap gap-2">
                                    @foreach($ad->images as $image)
                                        <div class="position-relative image-container">
                                            <img src="{{ $image->getImageUrl() }}" alt="{{ $ad->title }}" class="img-thumbnail" style="max-height: 150px; max-width: 150px;">
                                            <button type="button" class="btn btn-danger btn-sm delete-image" data-image-id="{{ $image->id }}" style="position: absolute; top: 5px; right: 5px;">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @elseif($ad->image)
                            <div class="mb-3">
                                <p class="form-text">الصورة الحالية:</p>
                                <div class="position-relative">
                                    <img src="{{ $ad->getImageUrl() }}" alt="{{ $ad->title }}" class="img-thumbnail" style="max-height: 150px; max-width: 150px;">
                                </div>
                            </div>
                        @endif

                        <!-- إضافة صور جديدة -->
                        <input type="file" name="images[]" id="images" class="form-control" accept="image/jpeg,image/jpg,image/png,image/gif,image/webp" multiple>
                        <div class="form-text">يمكنك رفع حتى 5 صور بحجم أقصى 2 ميجابايت للصورة الواحدة. سيتم ضغط الصور الكبيرة تلقائياً.</div>
                        <div class="mt-2" id="images-preview-container" class="d-flex flex-wrap gap-2"></div>
                    </div>

                    <!-- <div class="mb-3">
                        <label for="image" class="form-label">صورة الإعلان (الطريقة القديمة)</label>
                        <input type="file" name="image" id="image" class="form-control" accept="image/jpeg,image/jpg,image/png,image/gif,image/webp">
                        <div class="form-text">يمكنك رفع صورة جديدة بحجم أقصى 2 ميجابايت</div>
                        <div class="mt-2" id="image-preview-container" style="display: none;">
                            <img id="image-preview" src="#" alt="معاينة الصورة" style="max-width: 100%; max-height: 200px;">
                        </div>
                    </div> -->

                    @php
                        // التصنيفات الرئيسية والفرعية
                        $mainCategories = [
                            'vehicles' => [
                                'name' => 'مركبات',
                                'icon' => '🚗',
                                'subcategories' => [
                                    'new_cars' => 'سيارات جديدة',
                                    'used_cars' => 'سيارات مستعملة',
                                    'motorcycles' => 'دراجات نارية',
                                    'car_parts' => 'قطع غيار وإكسسوارات'
                                ]
                            ],
                            'realestate' => [
                                'name' => 'عقارات',
                                'icon' => '🏠',
                                'subcategories' => [
                                    'apartments_sale' => 'شقق للبيع',
                                    'apartments_rent' => 'شقق للإيجار',
                                    'villas' => 'فلل ومنازل',
                                    'lands' => 'أراضي للبيع'
                                ]
                            ],
                            'animals' => [
                                'name' => 'مواشي وحيوانات',
                                'icon' => '🐾',
                                'subcategories' => [
                                    'sheep' => 'أغنام',
                                    'cows' => 'أبقار',
                                    'camels' => 'إبل',
                                    'birds' => 'طيور زينة',
                                    'pets' => 'كلاب وقطط'
                                ]
                            ],
                            'electronics' => [
                                'name' => 'الكترونيات وتقنية',
                                'icon' => '💻',
                                'subcategories' => [
                                    'phones' => 'جوالات',
                                    'computers' => 'كمبيوترات',
                                    'tvs' => 'شاشات وتلفزيونات',
                                    'cameras' => 'كاميرات مراقبة'
                                ]
                            ],
                            'jobs' => [
                                'name' => 'وظائف وأعمال',
                                'icon' => '💼',
                                'subcategories' => [
                                    'admin_jobs' => 'وظائف إدارية',
                                    'tech_jobs' => 'وظائف فنية وهندسية',
                                    'sales_jobs' => 'وظائف مبيعات وتسويق',
                                    'freelance' => 'أعمال حرة ومقاولات'
                                ]
                            ],
                            'services' => [
                                'name' => 'خدمات عامة',
                                'icon' => '🛠',
                                'subcategories' => [
                                    'moving' => 'خدمات نقل عفش',
                                    'car_maintenance' => 'صيانة سيارات',
                                    'cleaning' => 'تنظيف منازل',
                                    'construction' => 'خدمات بناء وصيانة مباني'
                                ]
                            ],
                            'furniture' => [
                                'name' => 'أثاث ومستلزمات المنزل',
                                'icon' => '🛋',
                                'subcategories' => [
                                    'bedrooms' => 'غرف نوم',
                                    'kitchens' => 'مطابخ',
                                    'office_furniture' => 'أثاث مكتبي',
                                    'home_appliances' => 'أدوات منزلية وكهربائية'
                                ]
                            ],
                            'hobbies' => [
                                'name' => 'هوايات وترفيه',
                                'icon' => '🎮',
                                'subcategories' => [
                                    'sports' => 'أدوات رياضية',
                                    'games' => 'ألعاب إلكترونية',
                                    'books' => 'كتب ومجلات',
                                    'music' => 'آلات موسيقية'
                                ]
                            ],
                            'fashion' => [
                                'name' => 'ملابس وأزياء',
                                'icon' => '👕',
                                'subcategories' => [
                                    'men_clothes' => 'ملابس رجالية',
                                    'women_clothes' => 'ملابس نسائية',
                                    'shoes_bags' => 'أحذية وحقائب',
                                    'watches' => 'ساعات واكسسوارات'
                                ]
                            ],
                            'other' => [
                                'name' => 'أخرى',
                                'icon' => '📦',
                                'subcategories' => [
                                    'medical' => 'مستلزمات طبية',
                                    'gifts' => 'تحف وهدايا',
                                    'events' => 'مستلزمات مناسبات'
                                ]
                            ]
                        ];
                    @endphp

                    <div class="mb-3">
                        <label for="category" class="form-label">الفئة الرئيسية</label>
                        <select name="category" id="category" class="form-select" required onchange="updateSubcategories()">
                            <option value="" disabled>اختر الفئة الرئيسية</option>
                            @foreach($mainCategories as $key => $category)
                                <option value="{{ $key }}" {{ old('category', $ad->category) == $key ? 'selected' : '' }}>
                                    {{ $category['icon'] }} {{ $category['name'] }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="subcategory" class="form-label">الفئة الفرعية</label>
                        <select name="subcategory" id="subcategory" class="form-select">
                            <option value="" disabled selected>اختر الفئة الفرعية</option>
                            <!-- سيتم تحديث الخيارات بواسطة JavaScript -->
                        </select>
                    </div>

                    <script>
                        // تعريف التصنيفات الفرعية كمتغير JavaScript
                        const subcategories = @json($mainCategories);

                        // تحديث التصنيفات الفرعية عند تغيير التصنيف الرئيسي
                        function updateSubcategories() {
                            const categorySelect = document.getElementById('category');
                            const subcategorySelect = document.getElementById('subcategory');
                            const selectedCategory = categorySelect.value;

                            // إفراغ قائمة التصنيفات الفرعية
                            subcategorySelect.innerHTML = '<option value="" disabled selected>اختر الفئة الفرعية</option>';

                            // إذا تم اختيار تصنيف رئيسي
                            if (selectedCategory && subcategories[selectedCategory]) {
                                // إضافة التصنيفات الفرعية للتصنيف المختار
                                const subCats = subcategories[selectedCategory].subcategories;
                                for (const [key, value] of Object.entries(subCats)) {
                                    const option = document.createElement('option');
                                    option.value = key;
                                    option.textContent = value;

                                    // تحديد القيمة المحفوظة مسبقًا إن وجدت
                                    if (key === '{{ old('subcategory', $ad->subcategory) }}') {
                                        option.selected = true;
                                    }

                                    subcategorySelect.appendChild(option);
                                }
                            }
                        }

                        // تنفيذ الدالة عند تحميل الصفحة لتحديث التصنيفات الفرعية بناءً على القيمة المحفوظة
                        document.addEventListener('DOMContentLoaded', updateSubcategories);
                    </script>

                    <div class="mb-3">
                        <label for="location" class="form-label">الموقع</label>
                        <div class="input-group">
                            <input type="text" name="location" id="location" class="form-control" value="{{ old('location', $ad->location) }}">
                            <button type="button" class="btn btn-primary" id="get-location-btn">
                                <i class="fas fa-map-marker-alt"></i> تحديد موقعي
                            </button>
                            <button type="button" class="btn btn-outline-primary" id="open-map-btn">
                                <i class="fas fa-map"></i> اختيار من الخريطة
                            </button>
                        </div>
                        <div class="form-text" id="location-status"></div>
                        <!-- حقول مخفية للإحداثيات -->
                        <input type="hidden" name="latitude" id="latitude" value="{{ old('latitude', $ad->latitude) }}">
                        <input type="hidden" name="longitude" id="longitude" value="{{ old('longitude', $ad->longitude) }}">
                    </div>

                    <div class="mb-3">
                        <label for="price" class="form-label">السعر</label>
                        <input type="number" name="price" id="price" class="form-control" value="{{ old('price', $ad->price) }}">
                    </div>

                    <div class="mb-3">
                        <label for="phone" class="form-label">رقم الهاتف</label>
                        <input type="text" name="phone" id="phone" class="form-control" value="{{ old('phone', $ad->phone) }}">
                    </div>

                    <div class="mb-3">
                        <label for="whatsapp" class="form-label">رقم الواتساب</label>
                        <input type="text" name="whatsapp" id="whatsapp" class="form-control" value="{{ old('whatsapp', $ad->whatsapp) }}">
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" name="email" id="email" class="form-control" value="{{ old('email', $ad->email) }}">
                    </div>

                    <div class="mb-4 p-3 border rounded bg-light">
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" name="is_featured" id="is_featured" {{ $ad->is_featured ? 'checked' : '' }}>
                            <label class="form-check-label fw-bold" for="is_featured">
                                <i class="fas fa-star text-warning me-1"></i> تمييز الإعلان (إعلان خارجي)
                            </label>
                        </div>
                        <div id="featured-options" class="{{ $ad->is_featured ? '' : 'd-none' }}">
                            <div class="mb-2">
                                <label for="featured_days" class="form-label">عدد أيام التمييز:</label>
                                <input type="number" name="featured_days" id="featured_days" class="form-control" min="1" max="30" value="1">
                            </div>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-1"></i> سيتم خصم نقطة واحدة مقابل كل يوم تمييز. رصيدك الحالي: <strong>{{ auth()->user()->points ?? 0 }}</strong> نقطة.
                            </div>
                            @if($ad->is_featured && $ad->featured_until)
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-1"></i> هذا الإعلان مميز حتى تاريخ: <strong>{{ $ad->featured_until->format('Y-m-d') }}</strong>
                                </div>
                            @endif

                            @php
                                // حساب عدد الإعلانات المميزة النشطة للمستخدم
                                $activeFeaturedAdsCount = \App\Models\Ad::where('user_id', auth()->id())
                                    ->where('is_featured', true)
                                    ->where(function($query) {
                                        $query->whereNull('featured_until')
                                            ->orWhere('featured_until', '>', now());
                                    })
                                    ->count();

                                // الحد الأقصى للإعلانات المميزة (يمكن تعديله حسب الحاجة)
                                $maxFeaturedAds = 3;

                                // التحقق مما إذا كان هذا الإعلان مميزًا بالفعل
                                $isCurrentAdFeatured = $ad->is_featured && ($ad->featured_until === null || $ad->featured_until > now());

                                // إذا كان الإعلان الحالي مميزًا، نقلل العدد بواحد لاستثناء هذا الإعلان من العد
                                if ($isCurrentAdFeatured) {
                                    $activeFeaturedAdsCount--;
                                }
                            @endphp

                            @if($activeFeaturedAdsCount >= $maxFeaturedAds && !$isCurrentAdFeatured)
                                <div class="alert alert-warning mt-2">
                                    <i class="fas fa-exclamation-triangle me-1"></i> لقد وصلت للحد الأقصى من الإعلانات المميزة ({{ $maxFeaturedAds }}). يرجى إلغاء تمييز أحد إعلاناتك الأخرى أولاً أو الانتظار حتى انتهاء فترة التمييز.
                                </div>
                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        const featuredCheckbox = document.getElementById('is_featured');
                                        const maxReachedWarning = document.querySelector('.alert-warning');

                                        if (featuredCheckbox && maxReachedWarning) {
                                            featuredCheckbox.addEventListener('change', function() {
                                                if (this.checked) {
                                                    alert('لقد وصلت للحد الأقصى من الإعلانات المميزة ({{ $maxFeaturedAds }}). يرجى إلغاء تمييز أحد إعلاناتك الأخرى أولاً أو الانتظار حتى انتهاء فترة التمييز.');
                                                    this.checked = false;
                                                    document.getElementById('featured-options').classList.add('d-none');
                                                }
                                            });
                                        }
                                    });
                                </script>
                            @endif
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="fas fa-check ms-2"></i>تحديث الإعلان
                        </button>
                    </div>

                    <script>
                        // إظهار/إخفاء خيارات التمييز عند تغيير حالة الاختيار
                        document.addEventListener('DOMContentLoaded', function() {
                            const featuredCheckbox = document.getElementById('is_featured');
                            const featuredOptions = document.getElementById('featured-options');

                            featuredCheckbox.addEventListener('change', function() {
                                if (this.checked) {
                                    featuredOptions.classList.remove('d-none');
                                } else {
                                    featuredOptions.classList.add('d-none');
                                }
                            });
                        });
                    </script>

                </form>
            </div>
        </div>
    </div>
</div>

<script>
    // معاينة الصور قبل الرفع وحذف الصور
    document.addEventListener('DOMContentLoaded', function() {
        // معاينة الصور المتعددة
        const imagesInput = document.getElementById('images');
        const imagesPreviewContainer = document.getElementById('images-preview-container');

        if (imagesInput) {
            imagesInput.addEventListener('change', function() {
                // إفراغ حاوية المعاينة
                imagesPreviewContainer.innerHTML = '';

                // التحقق من عدد الصور
                if (this.files.length > 5) {
                    alert('يمكنك اختيار 5 صور كحد أقصى');
                    this.value = ''; // إفراغ حقل الإدخال
                    return;
                }

                // قائمة تنسيقات الصور المدعومة
                const supportedFormats = [
                    'image/jpeg',
                    'image/jpg',
                    'image/png',
                    'image/gif',
                    'image/webp'
                    // ملاحظة: تم استبعاد image/webm لأنه غير مدعوم في معالجة الصور
                ];

                // التحقق من كل ملف جديد
                for (let i = 0; i < this.files.length; i++) {
                    const file = this.files[i];

                    // التحقق من نوع الملف
                    if (!supportedFormats.includes(file.type.toLowerCase())) {
                        alert('تنسيق الصورة غير مدعوم. الرجاء استخدام تنسيقات: JPEG, PNG, GIF, WebP فقط');
                        this.value = '';
                        return;
                    }

                    // التحقق من حجم الملف (2MB)
                    if (file.size > 2 * 1024 * 1024) {
                        alert('حجم الصورة يجب أن يكون أقل من 2 ميجابايت');
                        this.value = '';
                        return;
                    }
                }

                // إنشاء معاينة لكل صورة
                for (let i = 0; i < this.files.length; i++) {
                    const file = this.files[i];
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        const imgContainer = document.createElement('div');
                        imgContainer.className = 'position-relative d-inline-block m-2';

                        const img = document.createElement('img');
                        img.src = e.target.result;
                        img.className = 'img-thumbnail';
                        img.style.maxHeight = '150px';
                        img.style.maxWidth = '150px';

                        imgContainer.appendChild(img);
                        imagesPreviewContainer.appendChild(imgContainer);
                    }

                    reader.readAsDataURL(file);
                }
            });
        }

        // معاينة الصورة الفردية (للتوافق مع النظام القديم)
        const imageInput = document.getElementById('image');
        const imagePreview = document.getElementById('image-preview');
        const previewContainer = document.getElementById('image-preview-container');

        if (imageInput) {
            imageInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        imagePreview.src = e.target.result;
                        previewContainer.style.display = 'block';
                    }

                    reader.readAsDataURL(this.files[0]);
                } else {
                    previewContainer.style.display = 'none';
                }
            });
        }

        // حذف الصور
        const deleteButtons = document.querySelectorAll('.delete-image');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                if (confirm('هل أنت متأكد من حذف هذه الصورة؟')) {
                    const imageId = this.getAttribute('data-image-id');
                    const imageContainer = this.closest('.image-container');

                    // إرسال طلب حذف الصورة
                    fetch(`/ad-images/${imageId}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}',
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // إزالة الصورة من العرض
                            imageContainer.remove();
                            alert(data.message);
                        } else {
                            alert('حدث خطأ أثناء حذف الصورة');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('حدث خطأ أثناء حذف الصورة');
                    });
                }
            });
        });
    });

    // تحديد الموقع الجغرافي
    document.addEventListener('DOMContentLoaded', function() {
        const getLocationBtn = document.getElementById('get-location-btn');
        const locationStatus = document.getElementById('location-status');
        const locationInput = document.getElementById('location');
        const latitudeInput = document.getElementById('latitude');
        const longitudeInput = document.getElementById('longitude');

        if (getLocationBtn) {
            getLocationBtn.addEventListener('click', function() {
                if (navigator.geolocation) {
                    locationStatus.textContent = 'جاري تحديد الموقع...';
                    locationStatus.style.color = 'blue';

                    navigator.geolocation.getCurrentPosition(
                        // نجاح
                        function(position) {
                            const latitude = position.coords.latitude;
                            const longitude = position.coords.longitude;

                            // تخزين الإحداثيات في الحقول المخفية
                            latitudeInput.value = latitude;
                            longitudeInput.value = longitude;

                            // الحصول على اسم الموقع باستخدام Reverse Geocoding
                            reverseGeocode(latitude, longitude);
                        },
                        // فشل
                        function(error) {
                            let errorMessage = '';
                            switch(error.code) {
                                case error.PERMISSION_DENIED:
                                    errorMessage = 'تم رفض الوصول إلى الموقع الجغرافي.';
                                    break;
                                case error.POSITION_UNAVAILABLE:
                                    errorMessage = 'معلومات الموقع غير متاحة.';
                                    break;
                                case error.TIMEOUT:
                                    errorMessage = 'انتهت مهلة طلب الموقع.';
                                    break;
                                case error.UNKNOWN_ERROR:
                                    errorMessage = 'حدث خطأ غير معروف.';
                                    break;
                            }
                            locationStatus.textContent = errorMessage;
                            locationStatus.style.color = 'red';
                        },
                        // خيارات
                        {
                            enableHighAccuracy: true,
                            timeout: 10000,
                            maximumAge: 0
                        }
                    );
                } else {
                    locationStatus.textContent = 'المتصفح لا يدعم تحديد الموقع الجغرافي.';
                    locationStatus.style.color = 'red';
                }
            });
        }

        // دالة للحصول على اسم الموقع من الإحداثيات
        function reverseGeocode(lat, lng) {
            // استخدام خدمة Nominatim OpenStreetMap للحصول على اسم الموقع
            fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&accept-language=ar`)
                .then(response => response.json())
                .then(data => {
                    if (data && data.address) {
                        // محاولة الحصول على اسم المدينة أو المنطقة
                        const city = data.address.city || data.address.town || data.address.village || data.address.county || '';
                        const country = data.address.country || '';

                        if (city && country) {
                            locationInput.value = `${country} - ${city}`;
                            locationStatus.textContent = 'تم تحديد الموقع بنجاح!';
                            locationStatus.style.color = 'green';
                        } else {
                            locationStatus.textContent = 'تم تحديد الإحداثيات ولكن لم يتم العثور على اسم المكان.';
                            locationStatus.style.color = 'orange';
                        }
                    } else {
                        locationStatus.textContent = 'تم تحديد الإحداثيات ولكن لم يتم العثور على معلومات الموقع.';
                        locationStatus.style.color = 'orange';
                    }
                })
                .catch(error => {
                    console.error('Error in reverse geocoding:', error);
                    locationStatus.textContent = 'تم تحديد الإحداثيات ولكن حدث خطأ في الحصول على اسم الموقع.';
                    locationStatus.style.color = 'orange';
                });
        }

        // تهيئة الخريطة
        let map = null;
        let marker = null;
        let selectedLat = null;
        let selectedLng = null;

        // عناصر نافذة الخريطة
        const mapContainer = document.getElementById('map-container');
        const mapCloseBtn = document.getElementById('map-close');
        const mapConfirmBtn = document.getElementById('map-confirm');
        const openMapBtn = document.getElementById('open-map-btn');
        const selectedLatSpan = document.getElementById('selected-lat');
        const selectedLngSpan = document.getElementById('selected-lng');

        // فتح نافذة الخريطة
        if (openMapBtn) {
            openMapBtn.addEventListener('click', function() {
                mapContainer.style.display = 'flex';

                // تهيئة الخريطة إذا لم تكن موجودة بالفعل
                if (!map) {
                    // تحديد مركز الخريطة (الرياض كمركز افتراضي)
                    const defaultLat = 24.7136;
                    const defaultLng = 46.6753;

                    // استخدام الإحداثيات المخزنة إذا كانت موجودة
                    const initialLat = latitudeInput.value ? parseFloat(latitudeInput.value) : defaultLat;
                    const initialLng = longitudeInput.value ? parseFloat(longitudeInput.value) : defaultLng;

                    // إنشاء الخريطة
                    map = L.map('map').setView([initialLat, initialLng], 13);

                    // إضافة طبقة الخريطة
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    }).addTo(map);

                    // إضافة علامة إذا كانت الإحداثيات موجودة
                    if (latitudeInput.value && longitudeInput.value) {
                        marker = L.marker([initialLat, initialLng], {draggable: true}).addTo(map);
                        selectedLat = initialLat;
                        selectedLng = initialLng;
                        selectedLatSpan.textContent = initialLat.toFixed(6);
                        selectedLngSpan.textContent = initialLng.toFixed(6);

                        // تحديث الإحداثيات عند سحب العلامة
                        marker.on('dragend', function(event) {
                            const position = marker.getLatLng();
                            selectedLat = position.lat;
                            selectedLng = position.lng;
                            selectedLatSpan.textContent = selectedLat.toFixed(6);
                            selectedLngSpan.textContent = selectedLng.toFixed(6);
                        });
                    }

                    // إضافة حدث النقر على الخريطة
                    map.on('click', function(e) {
                        // إزالة العلامة السابقة إذا وجدت
                        if (marker) {
                            map.removeLayer(marker);
                        }

                        // إضافة علامة جديدة
                        marker = L.marker(e.latlng, {draggable: true}).addTo(map);
                        selectedLat = e.latlng.lat;
                        selectedLng = e.latlng.lng;
                        selectedLatSpan.textContent = selectedLat.toFixed(6);
                        selectedLngSpan.textContent = selectedLng.toFixed(6);

                        // تحديث الإحداثيات عند سحب العلامة
                        marker.on('dragend', function(event) {
                            const position = marker.getLatLng();
                            selectedLat = position.lat;
                            selectedLng = position.lng;
                            selectedLatSpan.textContent = selectedLat.toFixed(6);
                            selectedLngSpan.textContent = selectedLng.toFixed(6);
                        });
                    });
                } else {
                    // تحديث حجم الخريطة عند إعادة فتحها
                    setTimeout(() => {
                        map.invalidateSize();
                    }, 100);
                }
            });
        }

        // إغلاق نافذة الخريطة
        if (mapCloseBtn) {
            mapCloseBtn.addEventListener('click', function() {
                mapContainer.style.display = 'none';
            });
        }

        // تأكيد اختيار الموقع
        if (mapConfirmBtn) {
            mapConfirmBtn.addEventListener('click', function() {
                if (selectedLat && selectedLng) {
                    // تخزين الإحداثيات في الحقول المخفية
                    latitudeInput.value = selectedLat;
                    longitudeInput.value = selectedLng;

                    // الحصول على اسم الموقع
                    reverseGeocode(selectedLat, selectedLng);

                    // إغلاق نافذة الخريطة
                    mapContainer.style.display = 'none';

                    // تحديث حالة الموقع
                    locationStatus.textContent = 'تم اختيار الموقع من الخريطة!';
                    locationStatus.style.color = 'green';
                } else {
                    alert('الرجاء اختيار موقع على الخريطة أولاً');
                }
            });
        }

        // إغلاق نافذة الخريطة عند النقر خارجها
        mapContainer.addEventListener('click', function(e) {
            if (e.target === mapContainer) {
                mapContainer.style.display = 'none';
            }
        });
    });

    // Smart Contact Script - التواصل الذكي
    const phoneInput = document.getElementById('phone');
    const whatsappInput = document.getElementById('whatsapp');

    // دالة للتحقق من الرقم وإظهار تنبيه واتساب
    function checkForWhatsAppRedirect(input, inputType) {
        if (!input) return;

        input.addEventListener('input', function() {
            const value = this.value.trim();

            // التحقق من بداية الرقم بـ 05 أو 966
            if (value.startsWith('05') || value.startsWith('966')) {
                // إنشاء تنبيه ديناميكي
                showWhatsAppAlert(value, inputType, this);
            }
        });
    }

    // دالة إظهار تنبيه واتساب
    function showWhatsAppAlert(phoneNumber, inputType, inputElement) {
        // إزالة أي تنبيه سابق
        const existingAlert = document.querySelector('.whatsapp-alert');
        if (existingAlert) {
            existingAlert.remove();
        }

        // تنسيق الرقم للواتساب
        let whatsappNumber = phoneNumber;
        if (whatsappNumber.startsWith('05')) {
            whatsappNumber = '966' + whatsappNumber.substring(1);
        }

        // إنشاء عنصر التنبيه
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-info whatsapp-alert mt-2';
        alertDiv.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fab fa-whatsapp text-success me-2" style="font-size: 1.2rem;"></i>
                <div class="flex-grow-1">
                    <strong>تم اكتشاف رقم سعودي!</strong>
                    <br>
                    <small>هل تريد فتح واتساب مباشرة؟</small>
                </div>
                <div>
                    <button type="button" class="btn btn-success btn-sm me-2" onclick="openWhatsApp('${whatsappNumber}')">
                        <i class="fab fa-whatsapp me-1"></i>
                        فتح واتساب
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="dismissAlert(this)">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;

        // إدراج التنبيه بعد حقل الإدخال
        inputElement.parentNode.insertBefore(alertDiv, inputElement.nextSibling);

        // إخفاء التنبيه تلقائياً بعد 10 ثوان
        setTimeout(() => {
            if (alertDiv && alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 10000);
    }

    // تطبيق التحقق على حقول الهاتف والواتساب
    checkForWhatsAppRedirect(phoneInput, 'phone');
    checkForWhatsAppRedirect(whatsappInput, 'whatsapp');

    // دالة فتح واتساب
    window.openWhatsApp = function(phoneNumber) {
        const whatsappUrl = `https://wa.me/${phoneNumber}`;
        window.open(whatsappUrl, '_blank');
    };

    // دالة إغلاق التنبيه
    window.dismissAlert = function(button) {
        const alert = button.closest('.whatsapp-alert');
        if (alert) {
            alert.remove();
        }
    };
</script>

<!-- نافذة الخريطة -->
<div id="map-container">
    <div id="map-modal">
        <div id="map-header">
            <h3>اختر موقعًا على الخريطة</h3>
            <button id="map-close">&times;</button>
        </div>
        <div id="map"></div>
        <div id="map-footer">
            <p id="map-coordinates">الإحداثيات: <span id="selected-lat">-</span>, <span id="selected-lng">-</span></p>
            <button id="map-confirm">تأكيد الموقع</button>
        </div>
    </div>
</div>

</body>
</html>
