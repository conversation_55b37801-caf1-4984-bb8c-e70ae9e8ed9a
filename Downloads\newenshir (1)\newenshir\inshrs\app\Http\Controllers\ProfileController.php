<?php

namespace App\Http\Controllers;

use App\Http\Requests\ProfileUpdateRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Illuminate\View\View;
use Exception;

class ProfileController extends Controller
{
    /**
     * Display the user's profile form.
     *
     * @param Request $request
     * @return View
     */
    public function edit(Request $request): View
    {
        return view('profile.edit', [
            'user' => $request->user(),
        ]);
    }

    /**
     * Update the user's profile information and experiences.
     *
     * @param ProfileUpdateRequest $request
     * @return RedirectResponse
     */
    public function update(ProfileUpdateRequest $request): RedirectResponse
    {
        try {
            DB::beginTransaction();
            
            $user = $request->user();
            $user->fill($request->validated());

            if ($user->isDirty('email')) {
                $user->email_verified_at = null;
            }

            // Update experiences
            if ($request->has('company')) {
                $user->experiences()->delete(); // Remove old experiences
                
                foreach ($request->company as $index => $company) {
                    if (!empty($company)) {
                        $user->experiences()->create([
                            'company_name' => $company,
                            'job_title' => $request->position[$index] ?? '',
                            'start_date' => $request->start_date[$index] ?? null,
                            'end_date' => $request->end_date[$index] ?? null,
                            'description' => $request->description[$index] ?? null,
                        ]);
                    }
                }
            }

            $user->save();
            DB::commit();

            return Redirect::route('profile.edit')
                ->with('status', 'profile-updated')
                ->with('message', 'تم تحديث الملف الشخصي بنجاح');

        } catch (Exception $e) {
            DB::rollBack();
            \Log::error('Profile update failed: ' . $e->getMessage());
            
            return Redirect::route('profile.edit')
                ->with('error', 'حدث خطأ أثناء تحديث الملف الشخصي');
        }
    }

    /**
     * Delete the user's account.
     *
     * @param Request $request
     * @return RedirectResponse
     */
    public function destroy(Request $request): RedirectResponse
    {
        try {
            $request->validateWithBag('userDeletion', [
                'password' => ['required', 'current-password'],
            ]);

            $user = $request->user();
            
            Auth::logout();
            $user->delete();
            
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return Redirect::to('/')
                ->with('message', 'تم حذف الحساب بنجاح');

        } catch (Exception $e) {
            \Log::error('Account deletion failed: ' . $e->getMessage());
            
            return Redirect::back()
                ->with('error', 'حدث خطأ أثناء حذف الحساب');
        }
    }



 
    public function showProfile()
    {
        $user = Auth::user(); // جلب المستخدم المصادق عليه
        $languages = $user->languages; // جلب اللغات المرتبطة بالمستخدم
        return view('profile', compact('languages')); // تمرير اللغات إلى العرض
   
    }

}