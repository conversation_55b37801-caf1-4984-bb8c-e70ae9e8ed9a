<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعلاناتي والوظائف التي قدمت عليها</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --light-bg: #f3f4f6;
            --border-color: #e5e7eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
        }

        body {
            background-color: var(--light-bg);
            padding: 10px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background-color: var(--primary-color);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 1.5rem;
        }

        .section-title {
            padding: 15px;
            font-size: 1.2rem;
            color: var(--secondary-color);
            border-bottom: 1px solid var(--border-color);
        }

        .jobs-grid,
        .ads-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            padding: 20px;
        }

        .job-card,
        .ad-card {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
        }

        .card-header {
            padding: 15px;
            border-bottom: 1px solid var(--border-color);
        }

        .card-title {
            font-size: 1.1rem;
            color: var(--secondary-color);
            margin-bottom: 5px;
        }

        .card-meta {
            font-size: 0.9rem;
            color: #666;
        }

        .card-content {
            padding: 15px;
        }

        .card-description {
            color: #444;
            margin-bottom: 15px;
            font-size: 0.95rem;
        }

        .tag-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 10px 0;
        }

        .tag {
            background: var(--light-bg);
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 0.85rem;
        }

        .card-actions {
            padding: 15px;
            border-top: 1px solid var(--border-color);
            display: flex;
            gap: 10px;
        }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }

        .primary-btn {
            background-color: var(--primary-color);
            color: white;
        }

        .secondary-btn {
            background-color: var(--light-bg);
            color: var(--secondary-color);
        }

        @media (max-width: 768px) {
            .jobs-grid,
            .ads-grid {
                grid-template-columns: 1fr;
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>إعلاناتي والوظائف التي قدمت عليها</h1>
        </header>

        <section class="applied-jobs">
            <h2 class="section-title">الوظائف التي قدمت عليها</h2>
            <div class="jobs-grid">
                <div class="job-card">
                    <div class="card-header">
                        <h3 class="card-title">مطور واجهات أمامية</h3>
                        <div class="card-meta">شركة التقنية المتقدمة</div>
                    </div>
                    <div class="card-content">
                        <p class="card-description">نبحث عن مطور واجهات أمامية ذو خبرة في React و Vue.js للعمل على مشاريعنا المتنوعة.</p>
                        <div class="tag-container">
                            <span class="tag">دوام كامل</span>
                            <span class="tag">الرياض</span>
                            <span class="tag">3-5 سنوات خبرة</span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="action-btn primary-btn">عرض التفاصيل</button>
                        <button class="action-btn secondary-btn">إلغاء التقديم</button>
                    </div>
                </div>

                <div class="job-card">
                    <div class="card-header">
                        <h3 class="card-title">مدير تسويق</h3>
                        <div class="card-meta">شركة التسويق الحديثة</div>
                    </div>
                    <div class="card-content">
                        <p class="card-description">نبحث عن مدير تسويق مبدع لقيادة فريق التسويق وتنفيذ استراتيجيات تسويقية مبتكرة.</p>
                        <div class="tag-container">
                            <span class="tag">دوام كامل</span>
                            <span class="tag">جدة</span>
                            <span class="tag">5+ سنوات خبرة</span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="action-btn primary-btn">عرض التفاصيل</button>
                        <button class="action-btn secondary-btn">إلغاء التقديم</button>
                    </div>
                </div>
            </div>
        </section>

        <section class="my-ads">
            <h2 class="section-title">إعلاناتي</h2>
            <div class="ads-grid">
                <div class="ad-card">
                    <div class="card-header">
                        <h3 class="card-title">مطور واجهات أمامية متاح للعمل الحر</h3>
                        <div class="card-meta">أحمد محمد</div>
                    </div>
                    <div class="card-content">
                        <p class="card-description">مطور واجهات أمامية ذو خبرة في React و Vue.js متاح للعمل الحر على مشاريع تطوير الويب.</p>
                        <div class="tag-container">
                            <span class="tag">React</span>
                            <span class="tag">Vue.js</span>
                            <span class="tag">عمل حر</span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="action-btn primary-btn">عرض الملف الشخصي</button>
                        <button class="action-btn secondary-btn">تعديل الإعلان</button>
                        <button class="action-btn secondary-btn">حذف الإعلان</button>
                    </div>
                </div>

                <div class="ad-card">
                    <div class="card-header">
                        <h3 class="card-title">مصمم جرافيك متاح للعمل عن بعد</h3>
                        <div class="card-meta">سارة علي</div>
                    </div>
                    <div class="card-content">
                        <p class="card-description">مصممة جرافيك مبدعة ومتاحة للعمل عن بعد على مشاريع تصميم الجرافيك المختلفة.</p>
                        <div class="tag-container">
                            <span class="tag">تصميم جرافيك</span>
                            <span class="tag">عمل عن بعد</span>
                            <span class="tag">Illustrator</span>
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="action-btn primary-btn">عرض الملف الشخصي</button>
                        <button class="action-btn secondary-btn">تعديل الإعلان</button>
                        <button class="action-btn secondary-btn">حذف الإعلان</button>
                    </div>
                </div>
            </div>
        </section>
    </div>
</body>
</html>