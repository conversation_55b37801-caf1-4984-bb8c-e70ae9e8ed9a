<?php

use Illuminate\Support\Facades\File;
use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\JobController;
use App\Http\Controllers\JobSeekerController;
use App\Http\Controllers\BasicInfoController;
use App\Http\Controllers\PostController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\CommentController;
use App\Http\Controllers\ResumeController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/




 

use App\Http\Controllers\PointController;
use App\Http\Controllers\MoyasarController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\Admin\BankTransferController;

Route::middleware(['auth'])->group(function () {
    // مسارات النقاط والدفع
    Route::get('/points/buy', [PointController::class, 'buy'])->name('points.buy');
    Route::post('/points/buy', [PointController::class, 'processBuy'])->name('points.buy.process');
    Route::get('/points/paypal/success', [PointController::class, 'paypalSuccess'])->name('points.paypal.success');
    Route::get('/points/paypal/cancel', [PointController::class, 'paypalCancel'])->name('points.paypal.cancel');
    Route::post('/points/paypal/process', [PointController::class, 'processPayPalPayment'])->name('points.paypal.process');
    Route::get('/points/transactions', [PointController::class, 'transactions'])->name('points.transactions');

    // مسارات Moyasar للدفع عبر STC Pay
    Route::post('/points/stcpay', [MoyasarController::class, 'createPayment'])->name('moyasar.create');
    Route::get('/points/stcpay/callback', [MoyasarController::class, 'callback'])->name('moyasar.callback');

    // مسارات الإشعارات
    Route::get('/notifications', [NotificationController::class, 'index'])->name('notifications.index');
    Route::post('/notifications/{id}/mark-as-read', [NotificationController::class, 'markAsRead'])->name('notifications.mark-as-read');
    Route::post('/notifications/mark-all-as-read', [NotificationController::class, 'markAllAsRead'])->name('notifications.mark-all-as-read');
    Route::delete('/notifications/{id}', [NotificationController::class, 'destroy'])->name('notifications.destroy');

    // مسارات إعدادات المستخدم
    Route::prefix('user/settings')->name('user.settings.')->group(function () {
        Route::get('/', [App\Http\Controllers\UserSettingsController::class, 'index'])->name('index');
        Route::get('/test', function() {
            return view('user.settings.test');
        })->name('test');
        Route::put('/profile', [App\Http\Controllers\UserSettingsController::class, 'updateProfile'])->name('profile');
        Route::put('/privacy', [App\Http\Controllers\UserSettingsController::class, 'updatePrivacy'])->name('privacy');
      
        Route::put('/notifications', [App\Http\Controllers\UserSettingsController::class, 'updateNotifications'])->name('notifications');
        Route::put('/preferences', [App\Http\Controllers\UserSettingsController::class, 'updatePreferences'])->name('preferences');
        Route::put('/password', [App\Http\Controllers\UserSettingsController::class, 'updatePassword'])->name('password');
        Route::post('/two-factor', [App\Http\Controllers\UserSettingsController::class, 'toggleTwoFactor'])->name('two-factor');
        Route::post('/block', [App\Http\Controllers\UserSettingsController::class, 'blockUser'])->name('block');
        Route::post('/unblock', [App\Http\Controllers\UserSettingsController::class, 'unblockUser'])->name('unblock');
        Route::delete('/delete', [App\Http\Controllers\UserSettingsController::class, 'deleteAccount'])->name('delete');
    });

    // مسارات الصورة الشخصية
    Route::prefix('user/profile-image')->name('user.profile-image.')->group(function () {
        Route::post('/upload', [App\Http\Controllers\ProfileImageController::class, 'upload'])->name('upload');
        Route::delete('/delete', [App\Http\Controllers\ProfileImageController::class, 'delete'])->name('delete');
        Route::get('/show/{userId?}', [App\Http\Controllers\ProfileImageController::class, 'show'])->name('show');
        Route::get('/info', [App\Http\Controllers\ProfileImageController::class, 'getImageInfo'])->name('info');
    });

    // تحديث آخر ظهور
    Route::post('/user/update-last-seen', function () {
        auth()->user()->update(['last_seen' => now()]);
        return response()->json(['success' => true]);
    });

    // صفحة اختبار نظام الصورة الشخصية
    Route::get('/test-profile-image', function () {
        return view('test-profile-image');
    })->name('test.profile-image');

    // صفحة الملف الشخصي المحدثة
    Route::get('/profile', function () {
        return view('profile.index');
    })->name('profile.index');

    // صفحة اختبار رفع الصورة البسيطة
    Route::get('/simple-upload-test', function () {
        return view('simple-upload-test');
    })->name('simple-upload-test');
});


Route::get('/terms-and-conditions', function () {
    return view('terms-and-conditions');
})->name('terms-and-conditions');



// مسارات لوحة تحكم المسؤول
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\PermissionController;
use App\Http\Controllers\Admin\UserRoleController;

Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // مسارات لوحة التحكم الرئيسية
    Route::get('/', [AdminController::class, 'dashboard'])->name('dashboard');
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard'); // إضافة مسار إضافي للوحة التحكم
    Route::get('/users', [AdminController::class, 'users'])->name('users');
    Route::get('/ads', [AdminController::class, 'ads'])->name('ads');
    Route::get('/jobs', [AdminController::class, 'jobs'])->name('jobs');
    Route::get('/job-seekers', [AdminController::class, 'jobSeekers'])->name('job-seekers');

    // مسارات إدارة المستخدمين
    Route::post('/users/{id}/make-admin', [AdminController::class, 'makeAdmin'])->name('users.make-admin');
    Route::post('/users/{id}/remove-admin', [AdminController::class, 'removeAdmin'])->name('users.remove-admin');
    Route::delete('/users/{id}', [AdminController::class, 'deleteUser'])->name('users.delete');

    // مسارات إدارة المحتوى
    Route::delete('/ads/{id}', [AdminController::class, 'deleteAd'])->name('ads.delete');
    Route::delete('/jobs/{id}', [AdminController::class, 'deleteJob'])->name('jobs.delete');

    // مسارات الإشعارات
    Route::post('/notifications/send', [AdminController::class, 'sendNotification'])->name('notifications.send');

    // مسارات إدارة طلبات التحويل البنكي
    Route::get('/bank-transfers', [BankTransferController::class, 'index'])->name('bank-transfers.index');
    Route::get('/bank-transfers/{id}', [BankTransferController::class, 'show'])->name('bank-transfers.show');
    Route::post('/bank-transfers/{id}/approve', [BankTransferController::class, 'approve'])->name('bank-transfers.approve');
    Route::post('/bank-transfers/{id}/reject', [BankTransferController::class, 'reject'])->name('bank-transfers.reject');

    // مسارات إدارة الأدوار والصلاحيات
    Route::resource('roles', RoleController::class);
    Route::resource('permissions', PermissionController::class);

    // مسارات إدارة أدوار المستخدمين
    Route::get('/user-roles', [UserRoleController::class, 'index'])->name('user-roles.index');
    Route::get('/user-roles/create', [UserRoleController::class, 'create'])->name('user-roles.create');
    Route::post('/user-roles', [UserRoleController::class, 'store'])->name('user-roles.store');
    Route::get('/user-roles/{id}/edit', [UserRoleController::class, 'edit'])->name('user-roles.edit');
    Route::put('/user-roles/{id}', [UserRoleController::class, 'update'])->name('user-roles.update');
    Route::delete('/user-roles/{id}', [UserRoleController::class, 'destroy'])->name('user-roles.destroy');
    // مسارات إدارة نقاط المستخدمين
    Route::get('/points/add', [PointController::class, 'create'])->name('points.add');
    Route::post('/points/add', [PointController::class, 'addPoints'])->name('points.store');

    // مسارات إعدادات الموقع (للمدير فقط)
    Route::get('/site-settings', [App\Http\Controllers\Admin\SiteSettingsController::class, 'index'])->name('site-settings.index');
    Route::put('/site-settings', [App\Http\Controllers\Admin\SiteSettingsController::class, 'update'])->name('site-settings.update');
    Route::get('/site-settings/reset', [App\Http\Controllers\Admin\SiteSettingsController::class, 'reset'])->name('site-settings.reset');
    Route::get('/site-settings/export', [App\Http\Controllers\Admin\SiteSettingsController::class, 'export'])->name('site-settings.export');
    Route::post('/site-settings/import', [App\Http\Controllers\Admin\SiteSettingsController::class, 'import'])->name('site-settings.import');
    Route::get('/site-settings/clear-cache', [App\Http\Controllers\Admin\SiteSettingsController::class, 'clearCache'])->name('site-settings.clear-cache');
});




use App\Http\Controllers\AdController;
use App\Http\Controllers\AdImageController;
use App\Http\Controllers\ImageController;
use App\Http\Controllers\SavedItemController;

Route::resource('ads', AdController::class);
Route::post('/ads/nearby', [AdController::class, 'nearby'])->name('ads.nearby');

// مسار لعرض الصور من قاعدة البيانات (النظام القديم)
Route::get('/images/{id}', [ImageController::class, 'show'])->name('images.show');

// مسار لعرض صور الإعلانات من قاعدة البيانات (النظام الجديد)
Route::get('/ad-images/{id}', [AdImageController::class, 'show'])->name('ad-images.show');

// مسار لحذف صور الإعلانات
Route::delete('/ad-images/{id}', [AdController::class, 'deleteImage'])->name('ad-images.destroy')->middleware('auth');






// مسارات البلاغات
Route::middleware(['auth'])->group(function () {
    Route::post('/ads/{id}/report', [App\Http\Controllers\ReportController::class, 'reportAd'])->name('ads.report');
    Route::post('/jobs/{id}/report', [App\Http\Controllers\ReportController::class, 'reportJob'])->name('jobs.report');
    Route::post('/job-seekers/{id}/report', [App\Http\Controllers\ReportController::class, 'reportJobSeeker'])->name('job-seekers.report');
});

// مسارات العناصر المحفوظة
Route::middleware(['auth'])->prefix('saved')->name('saved.')->group(function () {
    Route::get('/', [SavedItemController::class, 'index'])->name('index');
    Route::get('/ads', [SavedItemController::class, 'ads'])->name('ads');
    Route::get('/jobs', [SavedItemController::class, 'jobs'])->name('jobs');
    Route::get('/job-seekers', [SavedItemController::class, 'jobSeekers'])->name('job-seekers');
    Route::post('/toggle', [SavedItemController::class, 'store'])->name('toggle');
    Route::post('/check', [SavedItemController::class, 'checkSaved'])->name('check');
    Route::delete('/remove', [SavedItemController::class, 'destroy'])->name('remove');
    Route::delete('/clear-all', [SavedItemController::class, 'clearAll'])->name('clear-all');
    Route::get('/stats', [SavedItemController::class, 'stats'])->name('stats');
    Route::get('/export', [SavedItemController::class, 'export'])->name('export');
});



// routes/web.php
Route::get('/jobs/{id}/edit', [JobController::class, 'edit'])->name('jobs.edit');
Route::post('/jobs/{id}/update', [JobController::class, 'update'])->name('jobs.update');
Route::delete('/jobs/{id}', [JobController::class, 'destroy'])->name('jobs.destroy');


Route::get('/job-seekers/{id}/edit', [JobSeekerController::class, 'edit'])->name('jobSeeker.edit');
Route::put('/job-seekers/{id}/update', [JobSeekerController::class, 'update'])->name('jobSeeker.update');
Route::delete('my-job-seekers/{id}', [JobSeekerController::class, 'destroy'])->name('jobSeeker.destroy');


use App\Http\Controllers\DashboardController;


Route::get('/my-job-seekers', [DashboardController::class, 'myJobSeekers'])->name('myJobSeekers');


Route::get('/my-jobs', [JobController::class, 'myJobs'])->name('jobs.myJobs')->middleware('auth');



// Route::get('/users/profile/UserProfileShow', [BasicInfoController::class, 'show'])->name('UserProfileShow.show');
Route::get('/users/profile/basic_info', [BasicInfoController::class, 'edit'])->name('basic_info.edit');
Route::put('/users/profile/basic_info', [BasicInfoController::class, 'update'])->name('basic_info.update');

Route::get('jobs', [JobController::class, 'index'])->name('jobs.index');

Route::get('/jobs/show_job_company/{id}', [JobController::class, 'show'])->name('jobs.show');

// تم دمج هذا الراوت مع /job-seekers/{id}

Route::middleware(['auth'])->group(function () {
    Route::get('/jobs/post_job_company', [JobController::class, 'create'])->name('jobs.create');
    Route::post('/jobs/store', [JobController::class, 'store'])->name('jobs.store');
});


 Route::get('/show-job-company', function () {
     return view('/jobs/show_job_company');
  })->name('show-job-company');


Route::get('/Officials', function () {
    return view('admin/Officials/Officials_Dash');
})->name('Officials');


Route::get('/admin', function () {
    return redirect('/admin/dashboard');
})->name('admin');

// Custom login route is commented out to use Laravel's built-in auth routes
// Route::get('/login', function () {
//     return view('users/auth/login/login-singup');
// })->name('login');

// Google Authentication Routes
use App\Http\Controllers\Auth\GoogleAuthController;

Route::get('/auth/google', [GoogleAuthController::class, 'redirect'])->name('auth.google');
Route::get('/auth/google/callback', [GoogleAuthController::class, 'callback']);


Route::get('/dash', function () {
    return view('users/profile/dash');
})->name('dash');


Route::get('/companydash', function () {
    return view('company/companydash');
})->name('companydash');





Route::get('/form-page', function () {
    return view('specialsend/form_page');
})->name('form-page');



Route::get('/show-job-user', function () {
    return view('Jobs/show_job_user');
})->name('show-job-user');



Route::get('/edit_job_user', function () {
    return view('Jobs/edit_job_user');
})->name('edit_job_user');



Route::get('/show-job-application', function () {
    return view('Jobs/show_job_application');
})->name('show-job-application');



Route::get('/main', function () {
    return view('main/home');
})->name('main.home');



Route::get('/UserjobsManagement', function () {
    return view('Jobs/UserjobsManagement');
})->name('UserjobsManagement');



Route::get('/price', function () {
    return view('pricing/price');
})->name('price');



Route::get('/post-job-user', function () {
    return view('Jobs/post_job_user');
})->name('post-job-user');


// Route::get('/post-job-company', function () {
//     return view('jobs/post_job_company');
// })->name('post-job-company');




Route::get('/Apply-job-company', function () {
    return view('Jobs/Apply_job_company');
})->name('Apply-job-company');




Route::get('/jobsSeekers', function () {
    return view('Jobs/jobsSeekers');
})->name('jobsSeekers');





Route::get('/UserProfileShow', function () {
    return view('users/profile/UserProfileShow');
})->name('/UserProfileShow');





Route::get('/UserProfileupdate', function () {
    return view('users/profile/UserProfileUpdate');
})->name('/UserProfile');


Route::get('/userNotificationAllShow', function () {
    return view('Notification/userNotificationAllShow');
})->name('userNotificationAllShow');



Route::get('/settings-User', function () {
    return view('Jobs/setting_User');
})->name('settings-User');


Route::get('/cv-show', function () {
    return view('cv/show');
});


Route::get('/pricingC', function () {
    return view('pricing/priceCompany');
});





// Route::get('/User-Profile-Show', function () {
//     return view('users/profile/User_Profile_Show');
// });



Route::get('/User_Profile_updateblade', function () {
    return view('profile/User_Profile_updateblade');
});



Route::get('/jobsSearch', function () {
    return view('Jobs/jobsSearch');
});



Route::get('/NotificationAllShow', function () {
    return view('Notification/NotificationAllShow');
});


Route::get('/CompanyProfileShow', function () {
    return view('company/CompanyProfileShow');
});

Route::get('/company/basic_info', function () {
    return view('company/basic_info');
});

Route::get('/company/CompanyProfileShow', function () {
    return view('company/CompanyProfileShow');
})->name('CompanyProfileShow');

Route::get('/company/CompanyProfileUpdate', function () {
    return view('company/CompanyProfileUpdate');
})->name('CompanyProfileUpdate');



Route::get('/UserProfileUpdate', function () {
    return view('profile/UserProfileUpdate');
});



Route::get('/Permissions ', function () {
    return view('admin/Permissions ');
});


Route::get('/Officials-Dash', function () {
    return view('admin/Officials/Officials_Dash');
});


Route::get('/supp_manag', function () {
    return view('admin/Officials/supp_manag');
});


Route::get('/ads-admin', function () {
    return view('ads/ads_admin');
});





Route::get('/ads-user', function () {
    return view('ads/ads_user');
});





Route::get('/jobsManagement', function () {
    return view('Jobs/jobsManagement');
});

Route::get('/jobs-seekers', function () {
    return view('Jobs/jobsSeekers');
})->name('jobs-seekers');



Route::get('/pricingU', function () {
    return view('pricing/price');
});


Route::get('/', function () {
    return view('main/home');
}) ;










Route::get('/specialsend', [PostController::class, 'send'])->name('specialsend.send');

Route::get('profile/dash', [PostController::class, 'dash'])->name('profile.dash');



  Route::get('/edit-job', function () {
    return view('Jobs/edit_job');
});

Route::get('/job-applications', function () {
    return view('Jobs/job_applications');
});

Route::get('/basic-info', function () {
    return view('users/profile/basic_info');
})->name('basic-info');

Route::get('/company-basic-info', function () {
    return view('company/basic_info');
})->middleware('auth');

// مسارات التقارير الشاملة
Route::middleware(['auth'])->group(function () {
    Route::get('/reports/comprehensive', [App\Http\Controllers\ComprehensiveReportsController::class, 'index'])->name('reports.comprehensive');
});

// مسارات البلاغات للمشرفين
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // استخدام الطريقة القديمة مؤقتًا للتحقق من المشكلة
    Route::get('/reports', function () {
        return view('admin.reports_bootstrap')->with([
            'stats' => [
                'adReports' => [
                    'total' => 0,
                    'pending' => 0,
                    'reviewed' => 0,
                    'rejected' => 0,
                ],
                'jobReports' => [
                    'total' => 0,
                    'pending' => 0,
                    'reviewed' => 0,
                    'rejected' => 0,
                ],
                'jobSeekerReports' => [
                    'total' => 0,
                    'pending' => 0,
                    'reviewed' => 0,
                    'rejected' => 0,
                ],
            ],
            'totals' => [
                'total' => 0,
                'pending' => 0,
                'reviewed' => 0,
                'rejected' => 0,
            ],
            'pendingAdReports' => [],
            'pendingJobReports' => [],
            'pendingJobSeekerReports' => []
        ]);
    })->name('reports');

    // المسارات الأخرى للبلاغات
    Route::get('/reports/ads', [App\Http\Controllers\Admin\ReportController::class, 'adReports'])->name('reports.ads');
    Route::get('/reports/jobs', [App\Http\Controllers\Admin\ReportController::class, 'jobReports'])->name('reports.jobs');
    Route::get('/reports/job-seekers', [App\Http\Controllers\Admin\ReportController::class, 'jobSeekerReports'])->name('reports.job-seekers');

    Route::post('/reports/ads/{id}/status', [App\Http\Controllers\Admin\ReportController::class, 'updateAdReportStatus'])->name('reports.ads.status');
    Route::post('/reports/jobs/{id}/status', [App\Http\Controllers\Admin\ReportController::class, 'updateJobReportStatus'])->name('reports.jobs.status');
    Route::post('/reports/job-seekers/{id}/status', [App\Http\Controllers\Admin\ReportController::class, 'updateJobSeekerReportStatus'])->name('reports.job-seekers.status');
});
Route::get('/CompanyProfileShow', function () {
    return view('company/CompanyProfileShow');
})->name('/CompanyProfileShow');

Route::get('/sections', function () {
    return view('main/Sections');
})->name('sections');










Route::get('/jobs/create' ,function(){
Route::get('/upload', function() {
    $images = File::files(public_path('images'));
    return view('upload', compact('images'));
});
});













// Route::get('/index', function () {
//     return view('index');
// });



Route::get('/users/profile/UserProfileShow', [ProfileController::class, 'showProfile'])->middleware('auth');


Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // مسارات الدردشة
    Route::get('/chat', [ChatController::class, 'index'])->name('chat.index');
    Route::get('/chat/{id}', [ChatController::class, 'show'])->name('chat.show');
    Route::post('/chat/create', [ChatController::class, 'create'])->name('chat.create');
    Route::post('/chat/{id}/store', [ChatController::class, 'store'])->name('chat.store');

    // مسارات التعليقات
    Route::post('/ads/{adId}/comments', [CommentController::class, 'store'])->name('comments.store');
    Route::put('/comments/{id}', [CommentController::class, 'update'])->name('comments.update');
    Route::delete('/comments/{id}', [CommentController::class, 'destroy'])->name('comments.destroy');

    // مسارات السيرة الذاتية
    Route::get('/resumes', [ResumeController::class, 'index'])->name('resumes.index');
    Route::get('/resumes/create', [ResumeController::class, 'create'])->name('resumes.create');
    Route::post('/resumes', [ResumeController::class, 'store'])->name('resumes.store');
    Route::get('/resumes/{id}', [ResumeController::class, 'show'])->name('resumes.show');
    Route::get('/resumes/{id}/edit', [ResumeController::class, 'edit'])->name('resumes.edit');
    Route::put('/resumes/{id}', [ResumeController::class, 'update'])->name('resumes.update');
    Route::delete('/resumes/{id}', [ResumeController::class, 'destroy'])->name('resumes.destroy');
    Route::get('/resumes/{id}/pdf', [ResumeController::class, 'downloadPdf'])->name('resumes.pdf');
    Route::post('/resumes/{id}/customize', [ResumeController::class, 'customize'])->name('resumes.customize');
});



Route::get('/jobSeekers', [JobSeekerController::class, 'index'])->name('job_seekers.index');



Route::get('/job-seekers', [JobSeekerController::class, 'create'])->name('job_seekers.create');
Route::post('/job-seekers/store', [JobSeekerController::class, 'store'])->name('job_seekers.store');
Route::get('/job-seekers/{id}', [JobSeekerController::class, 'show'])->name('job-seekers.show');


Route::get('/privacy', function () {
    return view('privacy');
})->name('privacy');
require __DIR__.'/auth.php';


Route::get('/upload', function() {
    $images = App\Models\Image::latest()->get();
    return view('upload', compact('images'));
});

Route::post('/upload', function(Illuminate\Http\Request $request) {
    $request->validate([
        'image' => 'required|image'
    ]);

    $imageName = time().'.'.$request->image->extension();

    // قراءة محتوى الصورة وتحويلها إلى base64
    $imageData = base64_encode(file_get_contents($request->file('image')->getRealPath()));

    // حفظ الصورة في قاعدة البيانات
    $dbImage = App\Models\Image::create([
        'name' => $imageName,
        'type' => $request->file('image')->getMimeType(),
        'data' => $imageData
    ]);

    return redirect('/upload')->with('success', 'تم رفع الصورة بنجاح');
});


