<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SpecialAd extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'image_id',
        'url',
        'position',
        'is_active',
        'start_date',
        'end_date',
        'clicks',
        'views',
        'advertiser_name',
        'advertiser_phone',
        'advertiser_email',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    /**
     * العلاقة مع نموذج الصورة
     */
    public function image()
    {
        return $this->belongsTo(Image::class);
    }

    /**
     * التحقق مما إذا كان الإعلان نشط حاليًا
     */
    public function isActive()
    {
        return $this->is_active && 
               $this->start_date <= now() && 
               ($this->end_date === null || $this->end_date >= now());
    }

    /**
     * الحصول على مسار الصورة
     */
    public function getImageUrl()
    {
        if ($this->image_id && $this->image) {
            return route('images.show', $this->image_id) . '?v=' . time();
        }

        // إرجاع صورة افتراضية إذا لم تكن هناك صورة
        return 'https://via.placeholder.com/800x400?text=إعلان+مميز';
    }

    /**
     * زيادة عدد النقرات
     */
    public function incrementClicks()
    {
        $this->increment('clicks');
    }

    /**
     * زيادة عدد المشاهدات
     */
    public function incrementViews()
    {
        $this->increment('views');
    }
}
