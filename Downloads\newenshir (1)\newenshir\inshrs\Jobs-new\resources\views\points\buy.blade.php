<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شراء النقاط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .points-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-top: 30px;
        }
        .points-badge {
            background-color: #f0f8ff;
            color: #0d6efd;
            padding: 10px 15px;
            border-radius: 50px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 20px;
        }
        .payment-options {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        .paypal-button {
            background-color: #0070ba;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            width: 100%;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .paypal-button:hover {
            background-color: #003087;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        .alert {
            border-radius: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="points-card">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div class="d-flex align-items-center">
                            <a href="{{ url()->previous() }}" class="btn btn-outline-secondary btn-sm me-2">
                                <i class="fas fa-arrow-right me-1"></i> رجوع
                            </a>
                            <h2 class="mb-0">شراء النقاط</h2>
                        </div>
                        <a href="{{ route('points.transactions') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-history me-1"></i> سجل المعاملات
                        </a>
                    </div>

                    @if(session('success'))
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i> {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i> {{ session('error') }}
                        </div>
                    @endif

                    @if(session('info'))
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> {{ session('info') }}
                        </div>
                    @endif

                    <div class="text-center mb-4">
                        <div class="points-badge">
                            <i class="fas fa-coins me-2"></i> رصيدك الحالي: {{ auth()->user()->points ?? 0 }} نقطة
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i> اختر باقة النقاط</h5>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('points.buy.process') }}" method="POST" id="points-form">
                                @csrf
                                <div class="form-group mb-4">
                                    <label for="amount" class="form-label fw-bold">عدد النقاط:</label>
                                    <select name="amount" id="amount" class="form-select form-select-lg">
                                        <option value="5">5 نقاط - 0.05 دولار أمريكي</option>
                                        <option value="10">10 نقاط - 0.10 دولار أمريكي</option>
                                        <option value="20">20 نقطة - 0.20 دولار أمريكي</option>
                                        <option value="50">50 نقطة - 0.50 دولار أمريكي</option>
                                        <option value="100">100 نقطة - 1.00 دولار أمريكي</option>
                                    </select>
                                </div>
                                <input type="hidden" name="payment_method" value="paypal">

                                <div class="card bg-light mb-4">
                                    <div class="card-body">
                                        <h6 class="mb-3"><i class="fas fa-lightbulb text-warning me-2"></i> ماذا يمكنك أن تفعل بالنقاط؟</h6>
                                        <ul class="mb-0">
                                            <li>تثبيت إعلاناتك في أعلى القائمة (نقطة واحدة = يوم واحد)</li>
                                            <li>الوصول إلى ميزات حصرية للمستخدمين المميزين</li>
                                            <li>زيادة فرص ظهور إعلاناتك للمستخدمين</li>
                                            <li>سعر النقطة الواحدة: 0.01 دولار أمريكي فقط!</li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="card mt-4">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0"><i class="fas fa-credit-card me-2"></i> اختر طريقة الدفع</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="text-center mb-4">
                                            <h6 class="text-muted mb-3">المبلغ الإجمالي: <span id="total-amount">0.05</span> دولار أمريكي</h6>
                                        </div>

                                        <!-- خيارات طرق الدفع -->
                                        <ul class="nav nav-tabs mb-4" id="paymentTabs" role="tablist">
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link active" id="paypal-tab" data-bs-toggle="tab" data-bs-target="#paypal-content" type="button" role="tab" aria-controls="paypal-content" aria-selected="true">
                                                    <i class="fab fa-paypal me-1"></i> PayPal
                                                </button>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="stcpay-tab" data-bs-toggle="tab" data-bs-target="#stcpay-content" type="button" role="tab" aria-controls="stcpay-content" aria-selected="false">
                                                    <i class="fas fa-mobile-alt me-1"></i> STC Pay
                                                </button>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="bank-tab" data-bs-toggle="tab" data-bs-target="#bank-content" type="button" role="tab" aria-controls="bank-content" aria-selected="false">
                                                    <i class="fas fa-university me-1"></i> تحويل بنكي
                                                </button>
                                            </li>
                                        </ul>

                                        <div class="tab-content" id="paymentTabsContent">
                                            <!-- محتوى PayPal -->
                                            <div class="tab-pane fade show active" id="paypal-content" role="tabpanel" aria-labelledby="paypal-tab">
                                                <!-- حاوية أزرار PayPal -->
                                                <div id="paypal-button-container"></div>

                                                <div class="text-center mt-3">
                                                    <small class="text-muted">
                                                        <i class="fas fa-lock me-1"></i> جميع المعاملات آمنة ومشفرة
                                                    </small>
                                                    <div class="mt-2">
                                                        <img src="https://www.paypalobjects.com/webstatic/en_US/i/buttons/cc-badges-ppmcvdam.png" alt="طرق الدفع المقبولة" class="img-fluid" style="max-width: 250px;">
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- محتوى STC Pay -->
                                            <div class="tab-pane fade" id="stcpay-content" role="tabpanel" aria-labelledby="stcpay-tab">
                                                <div class="text-center mb-4">
                                                    <img src="https://stcpay.com.sa/wp-content/uploads/2022/03/stc-pay-logo.png" alt="STC Pay" class="img-fluid" style="max-width: 150px;">
                                                </div>

                                                <div class="alert alert-info">
                                                    <i class="fas fa-info-circle me-2"></i> سيتم تحويلك إلى تطبيق STC Pay لإكمال عملية الدفع.
                                                </div>

                                                <form action="{{ route('points.buy.process') }}" method="POST">
                                                    @csrf
                                                    <input type="hidden" name="amount" value="{{ $amount ?? request('amount', 5) }}">
                                                    <input type="hidden" name="payment_method" value="stcpay">
                                                    <button type="submit" class="btn btn-success btn-lg w-100">
                                                        <i class="fas fa-mobile-alt me-2"></i> الدفع عبر STC Pay
                                                    </button>
                                                </form>

                                                <div class="text-center mt-3">
                                                    <small class="text-muted">
                                                        <i class="fas fa-lock me-1"></i> جميع المعاملات آمنة ومشفرة
                                                    </small>
                                                </div>
                                            </div>

                                            <!-- محتوى التحويل البنكي -->
                                            <div class="tab-pane fade" id="bank-content" role="tabpanel" aria-labelledby="bank-tab">
                                                <div class="text-center mb-4">
                                                    <img src="https://www.alrajhibank.com.sa/content/dam/alrajhi-bank/images/alrajhi-logo.svg" alt="مصرف الراجحي" class="img-fluid" style="max-width: 150px;">
                                                </div>

                                                <div class="alert alert-info">
                                                    <i class="fas fa-info-circle me-2"></i> يرجى تحويل المبلغ إلى الحساب البنكي التالي ثم إرسال إشعار التحويل.
                                                </div>

                                                <div class="card mb-4">
                                                    <div class="card-body">
                                                        <h6 class="mb-3 fw-bold">تفاصيل الحساب البنكي:</h6>
                                                        <ul class="list-group list-group-flush">
                                                            <li class="list-group-item d-flex justify-content-between">
                                                                <span>اسم البنك:</span>
                                                                <span class="fw-bold">مصرف الراجحي</span>
                                                            </li>
                                                            <li class="list-group-item d-flex justify-content-between">
                                                                <span>اسم المستفيد:</span>
                                                                <span class="fw-bold">شركة سوق الإعلانات</span>
                                                            </li>
                                                            <li class="list-group-item d-flex justify-content-between">
                                                                <span>رقم الحساب:</span>
                                                                <span class="fw-bold">966511591846</span>
                                                            </li>
                                                            <li class="list-group-item d-flex justify-content-between">
                                                                <span>رقم الآيبان:</span>
                                                                <span class="fw-bold">SA0380000000966511591846</span>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>

                                                <div class="mb-4">
                                                    <form action="{{ route('points.buy.process') }}" method="POST" enctype="multipart/form-data">
                                                        @csrf
                                                        <input type="hidden" name="amount" value="{{ $amount ?? request('amount', 5) }}">
                                                        <input type="hidden" name="payment_method" value="bank_transfer">

                                                        <div class="mb-3">
                                                            <label for="transfer_receipt" class="form-label fw-bold">إيصال التحويل:</label>
                                                            <input type="file" class="form-control" id="transfer_receipt" name="transfer_receipt" required>
                                                            <div class="form-text">يرجى إرفاق صورة لإيصال التحويل البنكي.</div>
                                                        </div>

                                                        <div class="mb-3">
                                                            <label for="transfer_notes" class="form-label fw-bold">ملاحظات إضافية:</label>
                                                            <textarea class="form-control" id="transfer_notes" name="transfer_notes" rows="3" placeholder="أي معلومات إضافية عن التحويل..."></textarea>
                                                        </div>

                                                        <button type="submit" class="btn btn-primary btn-lg w-100">
                                                            <i class="fas fa-paper-plane me-2"></i> إرسال إشعار التحويل
                                                        </button>
                                                    </form>
                                                </div>

                                                <div class="alert alert-warning">
                                                    <i class="fas fa-exclamation-triangle me-2"></i> ملاحظة: سيتم إضافة النقاط إلى حسابك بعد التحقق من التحويل البنكي. قد تستغرق العملية من 1-2 يوم عمل.
                                                </div>
                                            </div>
                                        </div>

                                        <!-- زر الرجوع -->
                                        <div class="text-center mt-3">
                                            <button type="button" class="btn btn-outline-secondary" onclick="window.history.back();">
                                                <i class="fas fa-arrow-right me-1"></i> الرجوع إلى الصفحة السابقة
                                            </button>
                                        </div>

                                        <!-- رسالة معالجة الدفع -->
                                        <div id="payment-processing" class="alert alert-info text-center mt-3 d-none">
                                            <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                                                <span class="visually-hidden">جاري المعالجة...</span>
                                            </div>
                                            جاري معالجة الدفع، يرجى الانتظار...
                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-4 bg-light border-0">
                                    <div class="card-body">
                                        <h6 class="mb-3"><i class="fas fa-shield-alt text-success me-2"></i> مزايا الدفع عبر PayPal</h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <ul class="small mb-0">
                                                    <li>دفع آمن ومشفر بالكامل</li>
                                                    <li>حماية المشتري</li>
                                                </ul>

157382

                                            </div>
                                            <div class="col-md-6">
                                                <ul class="small mb-0">
                                                    <li>لا حاجة لإنشاء حساب PayPal</li>
                                                    <li>دعم للبطاقات الائتمانية والمدفوعات البنكية</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="text-center text-muted small">
                        <p>جميع المعاملات آمنة ومشفرة. بالضغط على زر الدفع، أنت توافق على <a href="#">شروط الخدمة</a>.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- PayPal JavaScript SDK -->
    <script src="https://www.paypal.com/sdk/js?client-id={{ config('services.paypal.' . config('services.paypal.mode') . '.client_id') }}&currency=USD&intent=capture&debug=true"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const amountSelect = document.getElementById('amount');

            // تحديث المبلغ عند تغيير الاختيار
            function updateAmount() {
                const points = parseInt(amountSelect.value);
                const rate = 0.01; // 0.01 ريال لكل نقطة
                const amount = (points * rate).toFixed(2);
                document.getElementById('total-amount').textContent = amount;
                renderPayPalButtons(amount);
            }

            amountSelect.addEventListener('change', updateAmount);

            // إنشاء أزرار PayPal
            function renderPayPalButtons(amount) {
                // إزالة الأزرار الحالية إن وجدت
                const container = document.getElementById('paypal-button-container');
                container.innerHTML = '';

                paypal.Buttons({
                    style: {
                        layout: 'vertical',
                        color: 'blue',
                        shape: 'rect',
                        label: 'pay'
                    },

                    // إعداد المعاملة
                    createOrder: function(data, actions) {
                        // طباعة المبلغ للتصحيح
                        console.log('Creating order with amount:', amount);

                        return actions.order.create({
                            purchase_units: [{
                                description: "شراء " + amountSelect.value + " نقطة",
                                amount: {
                                    currency_code: "USD",
                                    value: amount
                                }
                            }],
                            application_context: {
                                shipping_preference: 'NO_SHIPPING',
                                user_action: 'PAY_NOW',
                                cancel_url: '{{ route("points.buy") }}',
                                return_url: '{{ route("points.buy") }}'
                            }
                        });
                    },

                    // التعامل مع الموافقة
                    onApprove: function(data, actions) {
                        // عرض رسالة انتظار
                        document.getElementById('payment-processing').classList.remove('d-none');

                        // طباعة بيانات الطلب للتصحيح
                        console.log('Order approved:', data);

                        // التقاط الدفع
                        return actions.order.capture()
                            .then(function(orderData) {
                                // طباعة بيانات الطلب بعد التقاط للتصحيح
                                console.log('Order captured:', orderData);

                                // إرسال البيانات إلى الخادم
                                const formData = new FormData();
                                formData.append('_token', document.querySelector('input[name="_token"]').value);
                                formData.append('amount', amountSelect.value); // عدد النقاط
                                formData.append('transaction_id', orderData.id);
                                formData.append('payment_status', orderData.status);

                                // إضافة المزيد من البيانات للتصحيح
                                if (orderData.purchase_units && orderData.purchase_units[0] && orderData.purchase_units[0].amount) {
                                    formData.append('payment_amount', orderData.purchase_units[0].amount.value);
                                    formData.append('payment_currency', orderData.purchase_units[0].amount.currency_code);
                                }

                                return fetch('{{ route("points.paypal.process") }}', {
                                    method: 'POST',
                                    body: formData
                                });
                            })
                            .then(response => {
                                if (!response.ok) {
                                    throw new Error('Network response was not ok');
                                }
                                return response.json();
                            })
                            .then(data => {
                                if (data.success) {
                                    // إعادة تحميل الصفحة مع رسالة نجاح
                                    window.location.href = '{{ route("points.buy") }}?success=' + encodeURIComponent(data.message);
                                } else {
                                    // عرض رسالة خطأ
                                    alert(data.message || 'حدث خطأ أثناء معالجة الدفع');
                                    document.getElementById('payment-processing').classList.add('d-none');
                                }
                            })
                            .catch(error => {
                                console.error('Error processing payment:', error);
                                alert('حدث خطأ أثناء معالجة الدفع: ' + error.message);
                                document.getElementById('payment-processing').classList.add('d-none');
                            });
                    },

                    // التعامل مع الأخطاء
                    onError: function(err) {
                        console.error('PayPal Error:', err);

                        // عرض تفاصيل الخطأ في وحدة التحكم للتصحيح
                        if (err.details) {
                            console.error('Error Details:', JSON.stringify(err.details, null, 2));
                        }

                        // إظهار رسالة خطأ أكثر تفصيلاً للمستخدم
                        let errorMessage = 'حدث خطأ أثناء عملية الدفع';

                        if (err.message) {
                            errorMessage += ': ' + err.message;
                        }

                        alert(errorMessage);
                    }
                }).render('#paypal-button-container');
            }

            // تهيئة أزرار PayPal عند تحميل الصفحة
            updateAmount();
        });
    </script>
</body>
</html>







7095 الى 900








