<?php

// اختبار نظام الحفظ
// تشغيل: php test_save_system.php

require_once 'vendor/autoload.php';

// تحميل إعدادات Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\SavedItem;
use App\Models\Ad;
use App\Models\JobPosting;
use App\Models\JobSeeker;
use Illuminate\Support\Facades\Schema;

echo "🧪 اختبار نظام الحفظ الشامل\n";
echo "=============================\n\n";

try {
    // 1. فحص جدول saved_items
    echo "1️⃣ فحص جدول العناصر المحفوظة:\n";
    
    if (Schema::hasTable('saved_items')) {
        echo "   ✅ جدول 'saved_items' موجود\n";
        
        // فحص الأعمدة المطلوبة
        $requiredColumns = ['user_id', 'item_type', 'item_id', 'saved_at'];
        $missingColumns = [];
        
        foreach ($requiredColumns as $column) {
            if (!Schema::hasColumn('saved_items', $column)) {
                $missingColumns[] = $column;
            }
        }
        
        if (empty($missingColumns)) {
            echo "   ✅ جميع الأعمدة المطلوبة موجودة\n";
        } else {
            echo "   ❌ أعمدة مفقودة: " . implode(', ', $missingColumns) . "\n";
        }
        
        // إحصائيات الجدول
        $totalSaved = SavedItem::count();
        echo "   📊 إجمالي العناصر المحفوظة: {$totalSaved}\n";
        
        if ($totalSaved > 0) {
            $savedAds = SavedItem::where('item_type', 'ad')->count();
            $savedJobs = SavedItem::where('item_type', 'job')->count();
            $savedJobSeekers = SavedItem::where('item_type', 'job_seeker')->count();
            
            echo "   📢 إعلانات محفوظة: {$savedAds}\n";
            echo "   💼 وظائف محفوظة: {$savedJobs}\n";
            echo "   👤 باحثين محفوظين: {$savedJobSeekers}\n";
        }
        
    } else {
        echo "   ❌ جدول 'saved_items' غير موجود\n";
        echo "   🔧 تشغيل: php artisan migrate\n";
    }

    // 2. فحص نموذج SavedItem
    echo "\n2️⃣ فحص نموذج SavedItem:\n";
    
    if (class_exists('App\Models\SavedItem')) {
        echo "   ✅ نموذج SavedItem موجود\n";
        
        // فحص الدوال المطلوبة
        $requiredMethods = ['isSaved', 'saveItem', 'unsaveItem', 'toggleSave', 'getSavedStats'];
        $missingMethods = [];
        
        foreach ($requiredMethods as $method) {
            if (!method_exists(SavedItem::class, $method)) {
                $missingMethods[] = $method;
            }
        }
        
        if (empty($missingMethods)) {
            echo "   ✅ جميع الدوال المطلوبة موجودة\n";
        } else {
            echo "   ❌ دوال مفقودة: " . implode(', ', $missingMethods) . "\n";
        }
        
    } else {
        echo "   ❌ نموذج SavedItem غير موجود\n";
    }

    // 3. فحص Controller
    echo "\n3️⃣ فحص SavedItemController:\n";
    
    if (class_exists('App\Http\Controllers\SavedItemController')) {
        echo "   ✅ SavedItemController موجود\n";
        
        // فحص الدوال المطلوبة
        $requiredMethods = ['index', 'store', 'destroy', 'checkSaved', 'clearAll'];
        $missingMethods = [];
        
        foreach ($requiredMethods as $method) {
            if (!method_exists('App\Http\Controllers\SavedItemController', $method)) {
                $missingMethods[] = $method;
            }
        }
        
        if (empty($missingMethods)) {
            echo "   ✅ جميع دوال Controller موجودة\n";
        } else {
            echo "   ❌ دوال مفقودة: " . implode(', ', $missingMethods) . "\n";
        }
        
    } else {
        echo "   ❌ SavedItemController غير موجود\n";
    }

    // 4. فحص Views
    echo "\n4️⃣ فحص ملفات Views:\n";
    
    $viewFiles = [
        'resources/views/saved/index.blade.php' => 'الصفحة الرئيسية',
        'resources/views/components/save-button.blade.php' => 'زر الحفظ'
    ];
    
    foreach ($viewFiles as $file => $description) {
        if (file_exists($file)) {
            echo "   ✅ {$description} موجود\n";
        } else {
            echo "   ❌ {$description} غير موجود ({$file})\n";
        }
    }

    // 5. فحص الروابط
    echo "\n5️⃣ فحص الروابط:\n";
    
    $routeFile = 'routes/web.php';
    if (file_exists($routeFile)) {
        $content = file_get_contents($routeFile);
        
        $requiredRoutes = [
            'saved.index',
            'saved.toggle', 
            'saved.check',
            'saved.clear-all'
        ];
        
        $missingRoutes = [];
        foreach ($requiredRoutes as $route) {
            if (strpos($content, $route) === false) {
                $missingRoutes[] = $route;
            }
        }
        
        if (empty($missingRoutes)) {
            echo "   ✅ جميع الروابط المطلوبة موجودة\n";
        } else {
            echo "   ❌ روابط مفقودة: " . implode(', ', $missingRoutes) . "\n";
        }
        
    } else {
        echo "   ❌ ملف الروابط غير موجود\n";
    }

    // 6. اختبار وظائف النظام
    echo "\n6️⃣ اختبار وظائف النظام:\n";
    
    if (Schema::hasTable('saved_items') && class_exists('App\Models\SavedItem')) {
        
        // اختبار دالة getSavedStats
        try {
            $testUserId = 1; // افتراض وجود مستخدم بهذا المعرف
            $stats = SavedItem::getSavedStats($testUserId);
            
            if (is_array($stats) && isset($stats['total'], $stats['ads'], $stats['jobs'], $stats['job_seekers'])) {
                echo "   ✅ دالة getSavedStats تعمل بشكل صحيح\n";
                echo "   📊 إحصائيات المستخدم {$testUserId}: إجمالي {$stats['total']}, إعلانات {$stats['ads']}, وظائف {$stats['jobs']}, باحثين {$stats['job_seekers']}\n";
            } else {
                echo "   ❌ مشكلة في دالة getSavedStats\n";
            }
            
        } catch (Exception $e) {
            echo "   ⚠️ لا يمكن اختبار getSavedStats: " . $e->getMessage() . "\n";
        }
        
        // اختبار دالة isSaved
        try {
            $testUserId = 1;
            $testAdId = 1;
            $isSaved = SavedItem::isSaved($testUserId, 'ad', $testAdId);
            
            echo "   ✅ دالة isSaved تعمل بشكل صحيح\n";
            echo "   🔍 الإعلان {$testAdId} " . ($isSaved ? 'محفوظ' : 'غير محفوظ') . " للمستخدم {$testUserId}\n";
            
        } catch (Exception $e) {
            echo "   ⚠️ لا يمكن اختبار isSaved: " . $e->getMessage() . "\n";
        }
    }

    // 7. فحص التكامل مع الصفحات
    echo "\n7️⃣ فحص التكامل مع الصفحات:\n";
    
    $integrationFiles = [
        'resources/views/ads/show.blade.php' => 'save-button',
        'resources/views/Jobs/show_job_company.blade.php' => 'save-button',
        'resources/views/Jobs/show_job_user.blade.php' => 'save-button'
    ];
    
    foreach ($integrationFiles as $file => $component) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            if (strpos($content, $component) !== false) {
                echo "   ✅ {$file} يحتوي على {$component}\n";
            } else {
                echo "   ❌ {$file} لا يحتوي على {$component}\n";
            }
        } else {
            echo "   ❌ {$file} غير موجود\n";
        }
    }

    // 8. إحصائيات العناصر المتاحة
    echo "\n8️⃣ إحصائيات العناصر المتاحة:\n";
    
    try {
        $totalAds = Ad::count();
        echo "   📢 إجمالي الإعلانات: {$totalAds}\n";
    } catch (Exception $e) {
        echo "   ⚠️ لا يمكن الحصول على عدد الإعلانات\n";
    }
    
    try {
        $totalJobs = JobPosting::count();
        echo "   💼 إجمالي الوظائف: {$totalJobs}\n";
    } catch (Exception $e) {
        echo "   ⚠️ لا يمكن الحصول على عدد الوظائف\n";
    }
    
    try {
        $totalJobSeekers = JobSeeker::count();
        echo "   👤 إجمالي الباحثين عن عمل: {$totalJobSeekers}\n";
    } catch (Exception $e) {
        echo "   ⚠️ لا يمكن الحصول على عدد الباحثين عن عمل\n";
    }

    echo "\n🎯 خلاصة الاختبار:\n";
    echo "==================\n";
    
    $allGood = true;
    $issues = [];
    
    // فحص المتطلبات الأساسية
    if (!Schema::hasTable('saved_items')) {
        $issues[] = "جدول saved_items غير موجود";
        $allGood = false;
    }
    
    if (!class_exists('App\Models\SavedItem')) {
        $issues[] = "نموذج SavedItem غير موجود";
        $allGood = false;
    }
    
    if (!class_exists('App\Http\Controllers\SavedItemController')) {
        $issues[] = "SavedItemController غير موجود";
        $allGood = false;
    }
    
    if (!file_exists('resources/views/components/save-button.blade.php')) {
        $issues[] = "component زر الحفظ غير موجود";
        $allGood = false;
    }
    
    if ($allGood) {
        echo "🎉 نظام الحفظ جاهز للاستخدام!\n";
        echo "✅ جدول قاعدة البيانات موجود ومكتمل\n";
        echo "✅ جميع النماذج والـ Controllers متاحة\n";
        echo "✅ ملفات Views موجودة\n";
        echo "✅ التكامل مع الصفحات مكتمل\n\n";
        
        echo "📋 خطوات الاستخدام:\n";
        echo "=================\n";
        echo "1. php artisan migrate (إذا لم يتم تنفيذه)\n";
        echo "2. زيارة /saved لعرض العناصر المحفوظة\n";
        echo "3. استخدام أزرار الحفظ في صفحات العرض\n";
        echo "4. تجربة الفلترة والتصدير\n";
        
    } else {
        echo "⚠️ هناك مشاكل تحتاج إلى حل:\n";
        foreach ($issues as $issue) {
            echo "❌ {$issue}\n";
        }
        
        echo "\n🔧 حلول مقترحة:\n";
        echo "1. php artisan migrate\n";
        echo "2. التأكد من وجود جميع الملفات المطلوبة\n";
        echo "3. php artisan config:clear\n";
        echo "4. composer dump-autoload\n";
    }

} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
    echo "\nتفاصيل الخطأ:\n";
    echo $e->getTraceAsString() . "\n";
}

echo "\n📚 للمزيد من المعلومات:\n";
echo "========================\n";
echo "- راجع ملف SAVE_SYSTEM_DOCUMENTATION.md\n";
echo "- تأكد من تشغيل: php artisan migrate\n";
echo "- زر الصفحة: /saved\n";

?>
