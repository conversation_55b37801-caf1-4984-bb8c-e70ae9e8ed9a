@if(Auth::check() && Auth::id() != $userId)
<div class="chat-button-compact-container">
    <form action="{{ route('chat.create') }}" method="POST" class="chat-button-compact-form">
        @csrf
        <input type="hidden" name="receiver_id" value="{{ $userId }}">
        @if(isset($adId))
        <input type="hidden" name="ad_id" value="{{ $adId }}">
        @endif
        @if(isset($jobId))
        <input type="hidden" name="job_id" value="{{ $jobId }}">
        @endif
        <button type="submit" class="chat-button-compact" title="بدء محادثة">
            <i class="fas fa-comments"></i>
            <span class="chat-compact-text">محادثة</span>
        </button>
    </form>
</div>

<style>
    .chat-button-compact-container {
        display: inline-block;
    }
    
    .chat-button-compact-form {
        margin: 0;
    }
    
    .chat-button-compact {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #E67E22 0%, #D35400 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.5rem 0.75rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.875rem;
        box-shadow: 0 2px 6px rgba(230, 126, 34, 0.3);
        position: relative;
        overflow: hidden;
        font-family: 'Tajawal', sans-serif;
        text-decoration: none;
    }
    
    .chat-button-compact::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .chat-button-compact:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(230, 126, 34, 0.4);
        background: linear-gradient(135deg, #D35400 0%, #B7471D 100%);
    }
    
    .chat-button-compact:hover::before {
        opacity: 1;
    }
    
    .chat-button-compact:active {
        transform: translateY(0);
        transition: transform 0.1s ease;
    }
    
    .chat-button-compact i {
        font-size: 1rem;
        margin-left: 0.375rem;
    }
    
    .chat-compact-text {
        font-size: 0.875rem;
        font-weight: 600;
    }
    
    /* تنسيق للشاشات الصغيرة */
    @media (max-width: 768px) {
        .chat-button-compact {
            padding: 0.375rem 0.625rem;
            font-size: 0.8rem;
        }
        
        .chat-button-compact i {
            font-size: 0.9rem;
            margin-left: 0.25rem;
        }
        
        .chat-compact-text {
            font-size: 0.8rem;
        }
    }
    
    /* إخفاء النص في الشاشات الصغيرة جداً */
    @media (max-width: 480px) {
        .chat-compact-text {
            display: none;
        }
        
        .chat-button-compact {
            padding: 0.5rem;
            border-radius: 50%;
            width: 36px;
            height: 36px;
        }
        
        .chat-button-compact i {
            margin-left: 0;
            font-size: 1rem;
        }
    }
    
    /* تأثير التحميل */
    .chat-button-compact.loading {
        pointer-events: none;
        opacity: 0.8;
    }
    
    .chat-button-compact.loading i {
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
    
    /* تأثير النبضة الخفيف */
    .chat-button-compact:hover {
        animation: pulse-light 2s infinite;
    }
    
    @keyframes pulse-light {
        0% {
            box-shadow: 0 2px 6px rgba(230, 126, 34, 0.3);
        }
        50% {
            box-shadow: 0 4px 12px rgba(230, 126, 34, 0.5);
        }
        100% {
            box-shadow: 0 2px 6px rgba(230, 126, 34, 0.3);
        }
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثير التحميل عند الضغط
    const chatCompactButtons = document.querySelectorAll('.chat-button-compact');
    
    chatCompactButtons.forEach(button => {
        button.addEventListener('click', function() {
            this.classList.add('loading');
            
            // إزالة تأثير التحميل بعد 3 ثوان
            setTimeout(() => {
                this.classList.remove('loading');
            }, 3000);
        });
    });
});
</script>
@endif
