@extends('layouts.main')

@section('title', 'اختبار إعدادات المستخدم')

@section('content')
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4>اختبار إعدادات المستخدم</h4>
                </div>
                <div class="card-body">
                    <h5>معلومات المستخدم:</h5>
                    <ul>
                        <li><strong>الاسم:</strong> {{ Auth::user()->name }}</li>
                        <li><strong>البريد الإلكتروني:</strong> {{ Auth::user()->email }}</li>
                        <li><strong>الهاتف:</strong> {{ Auth::user()->phone ?? 'غير محدد' }}</li>
                    </ul>

                    <hr>

                    <h5>إعدادات الخصوصية الحالية:</h5>
                    <ul>
                        <li><strong>السماح بالمراسلة:</strong> 
                            <span class="badge {{ Auth::user()->allow_messages ? 'bg-success' : 'bg-danger' }}">
                                {{ Auth::user()->allow_messages ? 'مفعل' : 'معطل' }}
                            </span>
                        </li>
                        <li><strong>السماح بالتعليقات:</strong> 
                            <span class="badge {{ Auth::user()->allow_comments ? 'bg-success' : 'bg-danger' }}">
                                {{ Auth::user()->allow_comments ? 'مفعل' : 'معطل' }}
                            </span>
                        </li>
                        <li><strong>إظهار رقم الهاتف:</strong> 
                            <span class="badge {{ Auth::user()->show_phone ? 'bg-success' : 'bg-danger' }}">
                                {{ Auth::user()->show_phone ? 'مفعل' : 'معطل' }}
                            </span>
                        </li>
                        <li><strong>إظهار البريد الإلكتروني:</strong> 
                            <span class="badge {{ Auth::user()->show_email ? 'bg-success' : 'bg-danger' }}">
                                {{ Auth::user()->show_email ? 'مفعل' : 'معطل' }}
                            </span>
                        </li>
                        <li><strong>الملف الشخصي عام:</strong> 
                            <span class="badge {{ Auth::user()->profile_public ? 'bg-success' : 'bg-danger' }}">
                                {{ Auth::user()->profile_public ? 'مفعل' : 'معطل' }}
                            </span>
                        </li>
                    </ul>

                    <hr>

                    <h5>المستخدمون المحظورون:</h5>
                    @php
                        $blockedUserIds = Auth::user()->blocked_users ? json_decode(Auth::user()->blocked_users, true) : [];
                    @endphp
                    
                    @if(empty($blockedUserIds))
                        <p class="text-muted">لا توجد مستخدمون محظورون</p>
                    @else
                        <p>عدد المستخدمين المحظورين: {{ count($blockedUserIds) }}</p>
                        <ul>
                            @foreach($blockedUserIds as $blockedId)
                                @php
                                    $blockedUser = \App\Models\User::find($blockedId);
                                @endphp
                                @if($blockedUser)
                                    <li>{{ $blockedUser->name }} ({{ $blockedUser->email }})</li>
                                @endif
                            @endforeach
                        </ul>
                    @endif

                    <hr>

                    <div class="text-center">
                        <a href="{{ route('user.settings.index') }}" class="btn btn-primary">
                            <i class="fas fa-cog"></i> الذهاب لصفحة الإعدادات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
