<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use App\Models\PaymentTransaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class BankTransferController extends Controller
{
    /**
     * عرض قائمة طلبات التحويل البنكي
     */
    public function index()
    {
        $pendingTransfers = PaymentTransaction::where('payment_method', 'bank_transfer')
            ->where('status', 'PENDING')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('admin.bank_transfers.index', compact('pendingTransfers'));
    }

    /**
     * عرض تفاصيل طلب تحويل بنكي
     */
    public function show($id)
    {
        $transfer = PaymentTransaction::findOrFail($id);

        // التحقق من أن المعاملة هي تحويل بنكي
        if ($transfer->payment_method !== 'bank_transfer') {
            return redirect()->route('admin.bank-transfers.index')
                ->with('error', 'هذه المعاملة ليست تحويلاً بنكياً.');
        }

        return view('admin.bank_transfers.show', compact('transfer'));
    }

    /**
     * الموافقة على طلب تحويل بنكي
     */
    public function approve($id)
    {
        $transfer = PaymentTransaction::findOrFail($id);

        // التحقق من أن المعاملة هي تحويل بنكي وفي حالة معلقة
        if ($transfer->payment_method !== 'bank_transfer' || $transfer->status !== 'PENDING') {
            return redirect()->route('admin.bank-transfers.index')
                ->with('error', 'لا يمكن الموافقة على هذه المعاملة.');
        }

        try {
            // تحديث حالة المعاملة
            $transfer->status = 'COMPLETED';
            $transfer->completed_at = now();
            $transfer->save();

            // إضافة النقاط للمستخدم
            $user = User::find($transfer->user_id);
            if ($user) {
                $user->points = ($user->points ?? 0) + $transfer->points_amount;
                $user->save();

                // إنشاء إشعار للمستخدم
                Notification::create([
                    'user_id' => $user->id,
                    'title' => 'تمت الموافقة على طلب التحويل البنكي',
                    'message' => "تمت الموافقة على طلب التحويل البنكي الخاص بك وإضافة {$transfer->points_amount} نقطة إلى حسابك.",
                    'type' => 'success',
                    'related_to' => 'bank_transfer',
                    'related_id' => $transfer->id,
                ]);

                Log::info('Bank transfer approved', [
                    'transfer_id' => $transfer->id,
                    'user_id' => $user->id,
                    'points_added' => $transfer->points_amount,
                    'admin_id' => Auth::id()
                ]);
            }

            return redirect()->route('admin.bank-transfers.index')
                ->with('success', 'تمت الموافقة على طلب التحويل البنكي بنجاح.');
        } catch (\Exception $e) {
            Log::error('Error approving bank transfer', [
                'transfer_id' => $transfer->id,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('admin.bank-transfers.index')
                ->with('error', 'حدث خطأ أثناء الموافقة على طلب التحويل البنكي.');
        }
    }

    /**
     * رفض طلب تحويل بنكي
     */
    public function reject(Request $request, $id)
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:500',
        ]);

        $transfer = PaymentTransaction::findOrFail($id);

        // التحقق من أن المعاملة هي تحويل بنكي وفي حالة معلقة
        if ($transfer->payment_method !== 'bank_transfer' || $transfer->status !== 'PENDING') {
            return redirect()->route('admin.bank-transfers.index')
                ->with('error', 'لا يمكن رفض هذه المعاملة.');
        }

        try {
            // تحديث حالة المعاملة
            $transfer->status = 'REJECTED';

            // إضافة سبب الرفض إلى تفاصيل الدفع
            $paymentDetails = json_decode($transfer->payment_details, true) ?: [];
            $paymentDetails['rejection_reason'] = $request->rejection_reason;
            $paymentDetails['rejected_at'] = now()->toDateTimeString();
            $paymentDetails['rejected_by'] = Auth::id();

            $transfer->payment_details = json_encode($paymentDetails);
            $transfer->save();

            // إنشاء إشعار للمستخدم
            Notification::create([
                'user_id' => $transfer->user_id,
                'title' => 'تم رفض طلب التحويل البنكي',
                'message' => "تم رفض طلب التحويل البنكي الخاص بك. السبب: {$request->rejection_reason}",
                'type' => 'error',
                'related_to' => 'bank_transfer',
                'related_id' => $transfer->id,
            ]);

            Log::info('Bank transfer rejected', [
                'transfer_id' => $transfer->id,
                'user_id' => $transfer->user_id,
                'reason' => $request->rejection_reason,
                'admin_id' => Auth::id()
            ]);

            return redirect()->route('admin.bank-transfers.index')
                ->with('success', 'تم رفض طلب التحويل البنكي بنجاح.');
        } catch (\Exception $e) {
            Log::error('Error rejecting bank transfer', [
                'transfer_id' => $transfer->id,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('admin.bank-transfers.index')
                ->with('error', 'حدث خطأ أثناء رفض طلب التحويل البنكي.');
        }
    }
}
