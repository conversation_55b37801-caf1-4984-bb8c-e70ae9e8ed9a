<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>تعديل إعلان الباحث عن عمل</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 font-sans">
    <div class="container mx-auto px-4 py-8 max-w-3xl">
        <div class="bg-white rounded-lg shadow p-6">
            <h1 class="text-2xl font-bold mb-6 text-center">تعديل إعلان الباحث عن عمل</h1>

            @if ($errors->any())
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                    <ul class="list-disc ps-5">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form action="{{ route('jobSeeker.update', $jobSeeker->id) }}" method="POST">
                @csrf
                @method('PUT')

                <div class="mb-4">
                    <label for="job_title" class="block font-semibold mb-1">المسمى الوظيفي:</label>
                    <input type="text" name="job_title" id="job_title" value="{{ old('job_title', $jobSeeker->job_title) }}"
                        class="w-full border border-gray-300 rounded px-3 py-2" required>
                </div>

                <div class="mb-4">
                    <label for="description" class="block font-semibold mb-1">الوصف:</label>
                    <textarea name="description" id="description" rows="4"
                        class="w-full border border-gray-300 rounded px-3 py-2" required>{{ old('description', $jobSeeker->description) }}</textarea>
                </div>

                <div class="mb-4">
                    <label for="specialization" class="block font-semibold mb-1">التخصص:</label>
                    <input type="text" name="specialization" id="specialization"
                        value="{{ old('specialization', $jobSeeker->specialization) }}"
                        class="w-full border border-gray-300 rounded px-3 py-2" required>
                </div>

                <div class="mb-4">
                    <label for="experience" class="block font-semibold mb-1">الخبرة (بالسنوات):</label>
                    <input type="number" name="experience" id="experience"
                        value="{{ old('experience', $jobSeeker->experience) }}"
                        class="w-full border border-gray-300 rounded px-3 py-2">
                </div>

                <div class="mb-4">
                    <label for="skills" class="block font-semibold mb-1">المهارات:</label>
                    <input type="text" name="skills" id="skills" value="{{ old('skills', $jobSeeker->skills) }}"
                        class="w-full border border-gray-300 rounded px-3 py-2">
                </div>

                <div class="mb-4">
                    <label for="location" class="block font-semibold mb-1">الموقع:</label>
                    <input type="text" name="location" id="location"
                        value="{{ old('location', $jobSeeker->location) }}"
                        class="w-full border border-gray-300 rounded px-3 py-2" required>
                </div>

                <div class="mb-4">
                    <label for="whatsapp" class="block font-semibold mb-1">رقم الواتساب:</label>
                    <input type="text" name="whatsapp" id="whatsapp"
                        value="{{ old('whatsapp', $jobSeeker->whatsapp) }}"
                        class="w-full border border-gray-300 rounded px-3 py-2">
                </div>

                <div class="mb-4">
                    <label for="phone" class="block font-semibold mb-1">رقم الهاتف:</label>
                    <input type="text" name="phone" id="phone" value="{{ old('phone', $jobSeeker->phone) }}"
                        class="w-full border border-gray-300 rounded px-3 py-2">
                </div>

                <div class="text-center mt-6">
                    <button type="submit"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded text-lg">
                        تحديث البيانات
                    </button>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
