@props([
    'inputId' => 'email',
    'checkAvailability' => true,
    'realTimeValidation' => true,
    'showSuggestions' => true,
    'debounceDelay' => 500
])

<div class="email-validator-container" data-input-id="{{ $inputId }}">
    <style>
        .email-validator-container {
            position: relative;
        }
        
        .email-status {
            display: flex;
            align-items: center;
            margin-top: 0.5rem;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }
        
        .email-status.valid {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 1px solid #28a745;
            color: #155724;
        }
        
        .email-status.invalid {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border: 1px solid #dc3545;
            color: #721c24;
        }
        
        .email-status.checking {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border: 1px solid #17a2b8;
            color: #0c5460;
        }
        
        .email-status.taken {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffc107;
            color: #856404;
        }
        
        .email-status-icon {
            margin-left: 0.5rem;
            font-size: 1rem;
        }
        
        .email-status-text {
            flex: 1;
        }
        
        .email-suggestions {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-top: 0.5rem;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }
        
        .email-suggestions.show {
            display: block;
            animation: slideDown 0.3s ease;
        }
        
        .suggestion-item {
            padding: 0.75rem;
            cursor: pointer;
            border-bottom: 1px solid #f8f9fa;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }
        
        .suggestion-item:last-child {
            border-bottom: none;
        }
        
        .suggestion-item:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        
        .suggestion-icon {
            margin-left: 0.5rem;
            color: #007bff;
        }
        
        .suggestion-text {
            flex: 1;
        }
        
        .suggestion-domain {
            font-size: 0.8rem;
            color: #6c757d;
        }
        
        .email-format-help {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196f3;
            border-radius: 0.5rem;
            padding: 0.75rem;
            margin-top: 0.5rem;
            font-size: 0.875rem;
            color: #1565c0;
            display: none;
        }
        
        .email-format-help.show {
            display: block;
            animation: slideDown 0.3s ease;
        }
        
        .format-help-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }
        
        .format-examples {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .format-examples li {
            padding: 0.25rem 0;
            display: flex;
            align-items: center;
        }
        
        .format-examples li::before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-left: 0.5rem;
        }
        
        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
    
    <div class="email-status" id="{{ $inputId }}-status" style="display: none;">
        <span class="email-status-icon">
            <i class="fas fa-envelope"></i>
        </span>
        <span class="email-status-text"></span>
    </div>
    
    @if($showSuggestions)
        <div class="email-suggestions" id="{{ $inputId }}-suggestions">
            <!-- سيتم ملؤها ديناميكياً -->
        </div>
    @endif
    
    <div class="email-format-help" id="{{ $inputId }}-help">
        <div class="format-help-title">
            <i class="fas fa-info-circle me-2"></i>
            تنسيق البريد الإلكتروني الصحيح
        </div>
        <ul class="format-examples">
            <li><EMAIL></li>
            <li><EMAIL></li>
            <li><EMAIL></li>
        </ul>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const inputId = '{{ $inputId }}';
    const emailInput = document.getElementById(inputId);
    const container = document.querySelector(`[data-input-id="${inputId}"]`);
    
    if (!emailInput || !container) return;
    
    const config = {
        checkAvailability: {{ $checkAvailability ? 'true' : 'false' }},
        realTimeValidation: {{ $realTimeValidation ? 'true' : 'false' }},
        showSuggestions: {{ $showSuggestions ? 'true' : 'false' }},
        debounceDelay: {{ $debounceDelay }}
    };
    
    const elements = {
        status: document.getElementById(`${inputId}-status`),
        suggestions: document.getElementById(`${inputId}-suggestions`),
        help: document.getElementById(`${inputId}-help`)
    };
    
    let debounceTimer;
    let currentValidation = null;
    
    // مزودي البريد الشائعين
    const popularDomains = [
        'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
        'icloud.com', 'live.com', 'msn.com', 'aol.com'
    ];
    
    function validateEmailFormat(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    function showStatus(type, message, icon = null) {
        if (!elements.status) return;
        
        const icons = {
            valid: 'fas fa-check-circle',
            invalid: 'fas fa-times-circle',
            checking: 'spinner',
            taken: 'fas fa-exclamation-triangle'
        };
        
        elements.status.className = `email-status ${type}`;
        elements.status.style.display = 'flex';
        
        const iconElement = elements.status.querySelector('.email-status-icon');
        const textElement = elements.status.querySelector('.email-status-text');
        
        if (type === 'checking') {
            iconElement.innerHTML = '<div class="spinner"></div>';
        } else {
            iconElement.innerHTML = `<i class="${icon || icons[type]}"></i>`;
        }
        
        textElement.textContent = message;
    }
    
    function hideStatus() {
        if (elements.status) {
            elements.status.style.display = 'none';
        }
    }
    
    function generateSuggestions(email) {
        if (!config.showSuggestions || !elements.suggestions) return;
        
        const [localPart] = email.split('@');
        if (!localPart) return;
        
        const suggestions = popularDomains.map(domain => ({
            email: `${localPart}@${domain}`,
            domain: domain,
            icon: getDomainIcon(domain)
        }));
        
        const suggestionsHtml = suggestions.map(suggestion => `
            <div class="suggestion-item" onclick="selectSuggestion('${suggestion.email}')">
                <i class="${suggestion.icon} suggestion-icon"></i>
                <div class="suggestion-text">
                    <div>${suggestion.email}</div>
                    <div class="suggestion-domain">${suggestion.domain}</div>
                </div>
            </div>
        `).join('');
        
        elements.suggestions.innerHTML = suggestionsHtml;
        elements.suggestions.classList.add('show');
    }
    
    function hideSuggestions() {
        if (elements.suggestions) {
            elements.suggestions.classList.remove('show');
        }
    }
    
    function getDomainIcon(domain) {
        const icons = {
            'gmail.com': 'fab fa-google',
            'yahoo.com': 'fab fa-yahoo',
            'hotmail.com': 'fab fa-microsoft',
            'outlook.com': 'fab fa-microsoft',
            'icloud.com': 'fab fa-apple',
            'live.com': 'fab fa-microsoft'
        };
        return icons[domain] || 'fas fa-envelope';
    }
    
    function showFormatHelp() {
        if (elements.help) {
            elements.help.classList.add('show');
        }
    }
    
    function hideFormatHelp() {
        if (elements.help) {
            elements.help.classList.remove('show');
        }
    }
    
    async function checkEmailAvailability(email) {
        if (!config.checkAvailability) return { available: true };

        try {
            // استدعاء API للتحقق من توفر البريد
            const response = await fetch('/api/check-email-availability', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({ email: email })
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const data = await response.json();
            return {
                available: data.available,
                message: data.message
            };
        } catch (error) {
            // في حالة فشل API، نستخدم محاكاة
            await new Promise(resolve => setTimeout(resolve, 500));
            const isAvailable = Math.random() > 0.2;

            return {
                available: isAvailable,
                message: isAvailable ?
                    'البريد الإلكتروني متاح' :
                    'البريد الإلكتروني مستخدم بالفعل'
            };
        }
    }
    
    async function validateEmail(email) {
        // إلغاء التحقق السابق
        if (currentValidation) {
            currentValidation.cancelled = true;
        }
        
        const validation = { cancelled: false };
        currentValidation = validation;
        
        if (!email) {
            hideStatus();
            hideSuggestions();
            hideFormatHelp();
            return;
        }
        
        // التحقق من التنسيق
        if (!validateEmailFormat(email)) {
            showStatus('invalid', 'تنسيق البريد الإلكتروني غير صحيح');
            showFormatHelp();
            
            // إظهار اقتراحات إذا كان هناك @ ولكن لا يوجد نطاق
            if (email.includes('@') && !email.includes('.')) {
                generateSuggestions(email);
            } else {
                hideSuggestions();
            }
            return;
        }
        
        hideFormatHelp();
        hideSuggestions();
        
        // التحقق من التوفر
        if (config.checkAvailability) {
            showStatus('checking', 'جاري التحقق من توفر البريد الإلكتروني...');
            
            const result = await checkEmailAvailability(email);
            
            // التحقق من عدم إلغاء التحقق
            if (validation.cancelled) return;
            
            if (result.error) {
                showStatus('invalid', result.error);
            } else if (result.available) {
                showStatus('valid', 'البريد الإلكتروني متاح ✓');
            } else {
                showStatus('taken', result.message);
            }
        } else {
            showStatus('valid', 'تنسيق البريد الإلكتروني صحيح ✓');
        }
    }
    
    function debounceValidation(email) {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => {
            validateEmail(email);
        }, config.debounceDelay);
    }
    
    // دالة لاختيار اقتراح
    window.selectSuggestion = function(email) {
        emailInput.value = email;
        hideSuggestions();
        validateEmail(email);
        emailInput.focus();
    };
    
    // مستمعي الأحداث
    if (config.realTimeValidation) {
        emailInput.addEventListener('input', function() {
            debounceValidation(this.value.trim());
        });
        
        emailInput.addEventListener('focus', function() {
            if (this.value && !validateEmailFormat(this.value)) {
                showFormatHelp();
            }
        });
        
        emailInput.addEventListener('blur', function() {
            setTimeout(() => {
                hideSuggestions();
                hideFormatHelp();
            }, 200);
        });
    }
    
    // إغلاق الاقتراحات عند النقر خارجها
    document.addEventListener('click', function(event) {
        if (!container.contains(event.target)) {
            hideSuggestions();
            hideFormatHelp();
        }
    });
    
    // تصدير الدالة للاستخدام الخارجي
    window[`validateEmail_${inputId}`] = function() {
        return validateEmail(emailInput.value.trim());
    };
    
    // التحقق الأولي
    if (emailInput.value) {
        validateEmail(emailInput.value.trim());
    }
});
</script>
